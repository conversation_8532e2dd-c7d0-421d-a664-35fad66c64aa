import { TDigital, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetAdministratorsParams = {
  page?: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetAdministratorsMaterials = {
  params?: TGetAdministratorsParams;
};

export type TGetAdministratorsResponse = {
  data: Array<TDigital>;
};

export const getAdministrators = async (
  materials: TGetAdministratorsMaterials
): Promise<TGetAdministratorsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/digital-texts', { params });
  return response.data;
};
