
import { TNotification } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetNotificationResponse = any;

export const createNotification = async (payload: TNotification): Promise<TGetNotificationResponse> => {
  const response = await ApiService.post('/notifications', payload);
  return response.data;
};

export const getNotification = async (id: any ): Promise<TGetNotificationResponse> => {
  const response = await ApiService.get(`/notifications/${id}`);
  return response.data;
};

export const updateNotification = async (id: any, payload: TNotification): Promise<TGetNotificationResponse> => {
  const response = await ApiService.put(`/notifications/${id}`, payload);
  return response.data;
};

export const deleteNotification = async (id: any): Promise<TGetNotificationResponse> => {
  const response = await ApiService.delete(`/notifications/${id}`);
  return response.data;
};