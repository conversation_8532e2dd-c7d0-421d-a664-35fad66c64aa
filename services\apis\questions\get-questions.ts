import { EIsActive, EQuestionForm } from '@/commons/enums';
import { TListData, TQuestion } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetQuestionsParams = {
  page: number;
  category_id?: string;
  show_order?: number;
  question_year?: string;
  id?: number;
  title?: string;
  content?: string;
  is_actives?: Array<EIsActive>;
  types?: Array<EQuestionForm>;
};

export type TGetQuestionPaths = {
  exam_id: number;
};

export type TGetQuestionsMaterials = {
  params?: TGetQuestionsParams;
  paths: TGetQuestionPaths;
};

export type TGetQuestionsResponse = TListData<TQuestion>;

export const getQuestions = async (
  materials: TGetQuestionsMaterials
): Promise<TGetQuestionsResponse> => {
  const { params, paths } = materials;
  const response = await ApiService.get(`/exams/${paths.exam_id}/questions`, {
    params,
  });
  return response.data;
};
