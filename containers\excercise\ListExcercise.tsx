import {
  EIsActive,
  EPlacement,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { TExerciseResponse } from '@/commons/types';
import {
  Button,
  ColumnType,
  EButtonStyleType,
  Pagination,
  PreviewChildIcon,
  PreviewIcon,
  Table,
} from '@/components';
import { getExerciseAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import Link from 'next/link';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FilterExcercise from './FilterExcercise';
import { useRouter } from 'next/router';
import { TExerciseFilter } from './excercise.type';
import { getExportExerciseQuestions } from '@/services/apis';
import moment from 'moment';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import { EXCEPTION } from '@/commons/messages-enum';
import { ParsedUrlQueryInput } from 'querystring';
import { resetExerciseDetailSlice } from '@/store/slices/detailExercise';
import { setCallBackUrl } from '@/store/slices/status/history';
import { PreviewQuestionModal } from '../questions';
import { resetExerciseSlice } from '@/store/slices/exercises';

const ListExcercise = ({ query }: { query: TExerciseFilter }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [filter, setFilter] = useState<TExerciseFilter>();
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [questionPreivew, setQuestionPreivew] = useState<TExerciseResponse>();
  const { getExerciseResponse: mockTests } = useSelector(
    (state: RootState) => state.exercise
  );
  const { data, meta } = mockTests || {};
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
    };
    setFilter(defaultFilter);
  };

  useEffect(() => {
    return () => {
      dispatch(resetExerciseSlice());
    };
  }, [dispatch]);

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
  }, [query]);

  const getActions = useCallback(
    (element: TExerciseResponse) => {
      const handleRedirect = () => {
        dispatch(resetExerciseDetailSlice());
        dispatch(setCallBackUrl(router.asPath));
        router.push(`/exercises/${element.id}/detail`);
      };
      const handlePreviewQuestion = () => {
        setOpenModal(true);
        setQuestionPreivew(element);
      };

      return (
        <div className="flex items-center justify-center gap-5">
          <button onClick={handlePreviewQuestion}>
            <PreviewIcon />
          </button>
          <button onClick={handleRedirect}>
            <PreviewChildIcon />
          </button>
        </div>
      );
    },
    [dispatch, router]
  );

  const columns = useMemo(
    () =>
      [
        {
          title: '問題ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => element?.id,
        },
        {
          title: '出題No.',
          dataIndex: 'show_order',
          key: 'show_order',
          render: (element) => {
            return <div className="w-12 truncate">{element?.show_order}</div>;
          },
        },
        {
          title: '問題番号',
          dataIndex: 'question_no',
          key: 'question_no',
          render: (element) => {
            return <div className="w-12 truncate">{element?.question_no}</div>;
          },
        },
        {
          title: '出題年度',
          dataIndex: 'year',
          key: 'year',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 truncate">
              {element?.question_year}
            </div>
          ),
        },
        {
          title: '大項目名',
          dataIndex: 'category.name',
          key: 'category.name',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 break-all">
              {element?.category.name}
            </div>
          ),
        },
        {
          title: '小項目名',
          dataIndex: 'sub_catagory.name',
          key: 'sub_catagory.name',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 break-all">
              {element?.sub_category.name}
            </div>
          ),
        },
        {
          title: 'タイトル',
          dataIndex: 'title',
          key: 'title',
          render: (element) => (
            <div className="max-w-50 2xl:max-w-50 break-all">
              {element?.title}
            </div>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'invalid',
          key: 'invalid',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TExerciseResponse>>,
    [getActions]
  );

  const fetchData = useCallback(
    (page: number, filter?: TExerciseFilter) => {
      const is_actives = filter?.is_actives as Array<number>;
      const title = filter?.title;
      const category_id = filter?.category_id;
      const question_year = filter?.question_year;
      const content = filter?.content;
      const id = filter?.id;
      const sub_category_id = filter?.sub_category_id;
      const params = {
        page,
        is_actives,
        category_id,
        title,
        question_year,
        content,
        id,
        sub_category_id,
      };
      dispatch(getExerciseAction.request({ params }));
    },
    [dispatch]
  );

  const handleExportData = async () => {
    const category_id = filter?.category_id
      ? Number(filter?.category_id)
      : undefined;
    const sub_category_id = filter?.sub_category_id
      ? Number(filter?.sub_category_id)
      : undefined;
    const content = filter?.content?.trim() || undefined;
    const title = filter?.title?.trim() || undefined;
    const id = filter?.id || undefined;
    const question_year = filter?.question_year || undefined;
    const is_actives = filter?.is_actives as Array<number>;
    const params = {
      category_id,
      sub_category_id,
      content,
      title,
      id,
      question_year,
      is_actives,
    };

    try {
      const response = await getExportExerciseQuestions({ params });
      const fileName = `Kojiro_unkan_exercises_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  useEffect(() => {
    if (filter) {
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ pathname: '/exercises', query }, undefined, {
        shallow: true,
      });
      fetchData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleClickAddQuestion = () => {
    dispatch(setCallBackUrl(router.asPath));
    router.push('exercises/create');
  };

  const handleGotoUpload = () => {
    dispatch(setCallBackUrl(router.asPath));
    router.push('exercises/upload');
  };

  return (
    <Fragment>
      <PreviewQuestionModal
        showModal={openModal}
        handleCloseModal={() => {
          setOpenModal(false);
          setQuestionPreivew(undefined);
        }}
        quesitonContent={questionPreivew}
      />
      <div className="grid grid-cols-1 gap-2.5">
        <div className="flex flex-col gap-y-4">
          <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
            <FilterExcercise
              defaultFilter={filter}
              setFilter={setFilter}
              clearFilter={setDefaultFilter}
            />
          </div>
          {me?.data?.role_id !== ERoleId?.TEACHER && (
            <div className="self-end flex items-center gap-2">
              <Button
                onClick={handleExportData}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="出力"
                className="!font-normal"
              />
              <Button
                onClick={handleGotoUpload}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="アップロード"
                className="!font-normal"
              />
              <Button
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="新規登録"
                className="!font-normal"
                onClick={handleClickAddQuestion}
              />
            </div>
          )}
        </div>
        <div className="flex flex-col items-center gap-y-4 justify-between">
          <div className="w-full flex justify-between items-center">
            <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
              meta?.total || 0
            }件`}</h2>
            {meta?.total !== 0 && (
              <Pagination
                current={meta?.current_page || 0}
                total={meta?.last_page || 0}
                onChange={handleChangePage}
              />
            )}
          </div>
          <Table<TExerciseResponse> columns={columns} dataSource={data} />
          {meta?.total !== 0 && (
            <Pagination
              current={meta?.current_page || 0}
              total={meta?.last_page || 0}
              onChange={handleChangePage}
            />
          )}
        </div>
      </div>
    </Fragment>
  );
};

export default ListExcercise;
