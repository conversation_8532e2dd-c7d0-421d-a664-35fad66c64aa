import { EIsActive, EQuestionForm, ERoleId } from '@/commons/enums';
import { Button, EButtonStyleType } from '@/components';
import { QuestionForm } from '@/containers';
import { getExamAction, getQuestionAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { resetQuestionsSlice } from '@/store/slices/questions';
import { Form } from 'antd';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const ViewExamQuestion = () => {
  const router = useRouter();
  const [form] = Form.useForm<any>();
  const { id, questionId } = router.query;
  const { getQuestionResponse: question } = useSelector(
    (store: RootState) => store.question
  );
  const { getExamResponse: exam } = useSelector(
    (store: RootState) => store.mockTest
  );
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const dispatch = useDispatch();

  useEffect(() => {
    if (id && typeof id === 'string') {
      dispatch(getExamAction.request({ paths: { id } }));
    }
  }, [id, dispatch]);

  useEffect(() => {
    if (
      questionId &&
      typeof questionId === 'string' &&
      id &&
      typeof id === 'string'
    ) {
      const paths = { id: questionId, exam_id: id };
      dispatch(getQuestionAction.request({ paths }));
    }
  }, [questionId, dispatch, id]);

  useEffect(() => {
    if (!question || !question.data) {
      return;
    }
    const { data } = question;
    const {
      category,
      allocation_point,
      content,
      type,
      is_active,
      explanation,
      image,
      question_choices,
      question_explanation_images,
      question_year,
      show_order,
      title,
      laws,
      digital_texts,
      videos,
      question_no,
    } = data;
    const numQuestion = type === EQuestionForm.SINGLE ? 1 : 4;
    const numAns = type === EQuestionForm.SINGLE ? 10 : 10;
    const initChoices = Array(numQuestion).fill(undefined);
    const sub_questions = initChoices.map((_, idx) => {
      const items = question_choices.filter(
        ({ subquestion_no }) => subquestion_no === idx + 1
      );
      if (!items.length) {
        return Array(numAns).fill(undefined);
      }
      return Array(numAns)
        .fill(undefined)
        .map((_, idx) => {
          const item = items.find(({ choice_no }) => choice_no === idx + 1);
          if (item) {
            return {
              ...item,
              disable: true,
              is_correct: item?.is_correct === EIsActive.ENABLE,
            };
          }
          return undefined;
        });
    });

    const explanation_images = Array(2)
      .fill(undefined)
      .map((_, idx) => question_explanation_images?.[idx]);

    const category_id = category.id;
    const law_ids = Array(8)
      .fill(undefined)
      .map((_, idx) => {
        const law = laws?.[idx];
        if (!law) {
          return undefined;
        }
        const { category, name, id } = law;
        return { name, id, category_id: category.id };
      });
    const video_ids = Array(4)
      .fill(undefined)
      .map((_, idx) => {
        const video = videos?.[idx];
        if (!video) {
          return undefined;
        }
        const { category, name, id } = video;
        return { name, id, category_id: category.id };
      });
    const digital_text_ids = Array(4)
      .fill(undefined)
      .map((_, idx) => {
        const digital_text = digital_texts?.[idx];
        if (!digital_text) {
          return undefined;
        }
        const { category, name, id } = digital_text;
        return { name, id, category_id: category.id };
      });

    form.setFieldsValue({
      category_id,
      allocation_point,
      content,
      type,
      is_active,
      explanation,
      image,
      sub_questions,
      explanation_images,
      question_year,
      show_order,
      title,
      law_ids,
      digital_text_ids,
      video_ids,
      question_no,
    });
  }, [form, question]);

  const handleClickToBack = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push(`/exams/${id}/questions`);
  };

  const handleClickToEdit = () => {
    dispatch(resetQuestionsSlice());
    router.push(`/exams/${id}/questions/${questionId}/edit`);
  };

  return (
    <div className="mx-12 my-4 grid grid-cols-1 gap-5">
      <div className="flex justify-between items-center">
        <div className="text-lg font-bold text-black-kj flex break-all">
          <span>{`${exam?.data.name}_問題詳細`}</span>
        </div>
      </div>
      <div className="w-full px-5 pt-4 bg-new-white">
        <h2 className="text-black-kj font-bold !mb-5">
          問題ID:
          <span className="mx-3 text-primary text-base">
            {question?.data.id}
          </span>
        </h2>
        <QuestionForm formInstance={form} disable />
      </div>
      <div className="w-full flex items-center justify-end gap-5">
        <Button
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          title="戻る"
          className="!font-normal"
          onClick={handleClickToBack}
        />
        {me?.data.role_id === ERoleId.ADMIN && (
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="編集"
            className="!font-normal"
            onClick={handleClickToEdit}
          />
        )}
      </div>
    </div>
  );
};

export default ViewExamQuestion;
