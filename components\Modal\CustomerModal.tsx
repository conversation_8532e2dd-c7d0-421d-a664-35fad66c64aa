import { Modal as ModalAntd } from 'antd';
import clsx from 'clsx';
import { CloseIcon } from '../Icon';
import { TCustomerModalProps } from './Modal.types';
import styles from './styles.module.less';

const CustomerModal = ({
  visible,
  centered,
  width,
  className,
  title,
  children,
  zIndex,
  closable = true,
  headerClass,
  onClose,
  destroyOnClose,
}: TCustomerModalProps) => {
  const handleOnClose = () => {
    closable && onClose?.();
  };

  return (
    <ModalAntd
      footer={null}
      title={null}
      closable={false}
      open={visible}
      width={width}
      centered={centered}
      onCancel={handleOnClose}
      className={clsx('CustomModal', styles.modal, className)}
      zIndex={zIndex}
      destroyOnClose={destroyOnClose}
    >
      <div className="relative px-6 py-4">
        <div
          className={clsx(
            'absolute top-0 right-0 w-3 h-3 cursor-pointer z-10',
            !closable && 'hidden'
          )}
          onClick={handleOnClose}
        >
          <CloseIcon fillcolor="#000000" />
        </div>
        <div
          className={clsx(
            'flex items-center justify-center text-dark-shade-of-gray',
            headerClass
          )}
        >
          <div className="text-3xl font-extrabold text-center uppercase">
            {title}
          </div>
        </div>
        {children}
      </div>
    </ModalAntd>
  );
};

export default CustomerModal;
