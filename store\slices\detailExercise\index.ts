import { TGetDetailExerciseResponse } from '@/services/apis';
import {
  EGetDetailExerciseAction,
  TGetDetailExerciseSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TDetailExerciseState {
  getDetailExerciseResponse?: TGetDetailExerciseResponse;
}

const initialState: TDetailExerciseState = {
  getDetailExerciseResponse: undefined,
};

const exerciseDetailSlice = createSlice({
  name: 'exercise-detail-slice',
  initialState,
  reducers: {
    resetExerciseDetailSlice: (state) => ({
      ...state,
      getDetailExerciseResponse: undefined,
    }),
  },
  extraReducers: {
    [EGetDetailExerciseAction.GET_EXERCISE_DETAIL_SUCCESS]: (
      state: TDetailExerciseState,
      action: TGetDetailExerciseSuccess
    ): TDetailExerciseState => ({
      ...state,
      getDetailExerciseResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: excerciseDetailReducer,
  actions: { resetExerciseDetailSlice },
} = exerciseDetailSlice;

export { excerciseDetailReducer, resetExerciseDetailSlice };
