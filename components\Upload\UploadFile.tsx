import { Upload as UploadAntd } from 'antd';
import {
  UploadChangeParam,
  UploadFile as UploadFileAntd,
} from 'antd/lib/upload';
import { TUploadFileProps } from './Upload.types';

const UploadFile = ({
  type,
  accept,
  maxCount,
  children,
  disabled,
  setFile,
  setLoading,
  onError,
  onErrorType,
  onErrorSize,
  className,
  maxSize,
}: TUploadFileProps) => {
  const handleChange = ({ file }: UploadChangeParam<UploadFileAntd>) => {
    if (!file.status) {
      onError?.();
      return;
    }
    setLoading?.(true);
    setFile?.(file.originFileObj as File);
    setLoading?.(false);
  };

  const handleBeforeUpload = (file: File) => {
    const fileName = file.name;
    const fileExtension = fileName
      .substring(fileName.lastIndexOf('.') + 1)
      .toLocaleLowerCase();
    if (type && !type?.includes(fileExtension) && type !== fileExtension) {
      onErrorType?.();
      return false;
    }
    if (maxSize && file.size > maxSize) {
      onErrorSize?.();
      return false;
    }
    return true;
  };

  return (
    <UploadAntd
      accept={accept}
      maxCount={maxCount}
      showUploadList={false}
      onChange={handleChange}
      beforeUpload={handleBeforeUpload}
      customRequest={() => null}
      className={className}
      disabled={disabled}
    >
      {children}
    </UploadAntd>
  );
};

export default UploadFile;
