import { TGetExamSettingResponse } from '@/services/apis';
import { EGetExamSettingAction, TGetExamSettingSuccess } from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TSettingState {
  getExamSettingResponse?: TGetExamSettingResponse;
}

const initialState: TSettingState = {
  getExamSettingResponse: undefined,
};

const settingSlice = createSlice({
  name: 'setting-slice',
  initialState,
  reducers: {
    resetStateSettingSlice: () => initialState,
  },
  extraReducers: {
    [EGetExamSettingAction.GET_EXAM_SETTING_SUCCESS]: (
      state: TSettingState,
      action: TGetExamSettingSuccess
    ): TSettingState => ({
      ...state,
      getExamSettingResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: settingReducer,
  actions: { resetStateSettingSlice },
} = settingSlice;

export { settingReducer, resetStateSettingSlice };
