import {
  TGetExamSettingMaterials,
  TGetExamSettingResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetExamSettingAction {
  GET_EXAM_SETTING = 'GET_EXAM_SETTING',
  GET_EXAM_SETTING_REQUEST = 'GET_EXAM_SETTING_REQUEST',
  GET_EXAM_SETTING_SUCCESS = 'GET_EXAM_SETTING_SUCCESS',
  GET_EXAM_SETTING_FAILED = 'GET_EXAM_SETTING_FAILED',
}

// TYPES

export type TGetExamSettingRequest = {
  type: EGetExamSettingAction.GET_EXAM_SETTING_REQUEST;
  payload: {
    materials: TGetExamSettingMaterials;
    successCallback?: (response: TGetExamSettingResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetExamSettingSuccess = {
  type: EGetExamSettingAction.GET_EXAM_SETTING_SUCCESS;
  payload: { response?: TGetExamSettingResponse };
};

export type TGetExamSettingFailed = {
  type: EGetExamSettingAction.GET_EXAM_SETTING_FAILED;
};

export const getExamSettingAction = {
  request: createActionCreator(
    EGetExamSettingAction.GET_EXAM_SETTING_REQUEST,
    (resolve) =>
      (
        materials: TGetExamSettingMaterials,
        successCallback?: (response: TGetExamSettingResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetExamSettingRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetExamSettingAction.GET_EXAM_SETTING_SUCCESS,
    (resolve) =>
      (response?: TGetExamSettingResponse): TGetExamSettingSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetExamSettingAction.GET_EXAM_SETTING_FAILED,
    (resolve) =>
      (error: unknown): TGetExamSettingFailed =>
        resolve({ error })
  ),
};
