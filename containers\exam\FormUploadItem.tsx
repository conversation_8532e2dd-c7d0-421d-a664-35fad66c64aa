import { AddImageIcon, DeleteIcon, UploadFile } from '@/components';
import clsx from 'clsx';
import React, {
  Fragment,
  MouseEvent,
  useCallback,
  useMemo,
  useState,
} from 'react';
import { TFormUploadItemProps } from './mock-tests.types';
import CustomImage from '@/components/Image/Image';
import { uploadImage } from '@/services/apis';
import { Form, Spin } from 'antd';
import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined';
import { showNotification, toBase64 } from '@/utils/functions';
import { ETypeNotification } from '@/commons/enums';

const antIcon = <LoadingOutlined style={{ fontSize: 64 }} spin />;
const FormUploadItem = ({
  value,
  onChange,
  accept,
  type,
  width,
  disable = false,
  name,
  label,
  folder,
}: TFormUploadItemProps) => {
  const form = Form.useFormInstance();
  const [blurDataURL, setBlurDataURL] = useState<string>();
  const handleErrorType = () => {
    if (name && label) {
      form.setFields([
        {
          name,
          errors: [
            "ファイルには、英数字('a-z','A-Z','0-9')と記号（_,-）が使用できます。 画像の拡張子は、jpegかjpgかpngかJPEGかJPGかPNGの画像を指定してください。",
          ],
        },
      ]);
    }
  };

  const handleErrorSize = () => {
    if (name && label) {
      form.setFields([
        {
          name,
          errors: [`${label}は、5120KB以下のファイルでなければいけません。`],
        },
      ]);
    }
  };

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSuccess = (file: File) => {
    setIsLoading(true);
    uploadImage(file, folder)
      .then((res) => {
        const url = res?.file_full_path;
        onChange?.(url);
        setIsLoading(false);
        if (name && label) {
          form.setFields([
            {
              name,
              errors: [],
            },
          ]);
        }
      })
      .catch((error) => {
        const { data } = error;
        if (name && data?.message) {
          form.setFields([
            {
              name,
              errors: [data.message],
            },
          ]);
        }
        setIsLoading(false);
      });
  };

  const handleRemoveImage = useCallback(
    (event: MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      onChange?.(undefined);
    },
    [onChange]
  );

  const contentUpload = useMemo(() => {
    if (isLoading) {
      return (
        <div className="w-full h-full bg-gray-400 bg-opacity-30">
          <div className="w-full h-full flex items-center justify-center">
            <Spin indicator={antIcon} />
          </div>
        </div>
      );
    }
    if (value) {
      return (
        <Fragment>
          {!disable && (
            <button
              className="absolute top-0 right-0 z-10 m-1 p-1.5 rounded-full hover:bg-slate-50"
              onClick={handleRemoveImage}
            >
              <DeleteIcon />
            </button>
          )}
          <div className="w-full aspect-square">
            <CustomImage
              src={value}
              alt="update-item"
              classnames="w-full h-full"
            />
          </div>
        </Fragment>
      );
    }
    return (
      <div className="h-full flex gap-4 flex-col items-center justify-center mx-5">
        <div className="w-full flex items-center justify-center">
          <AddImageIcon />
        </div>
        <div className={clsx('!text-black-kj opacity-50 text-sm font-bold')}>
          アップロード
        </div>
      </div>
    );
  }, [isLoading, value, disable, handleRemoveImage]);
  return (
    <div
      className={clsx('aspect-square overflow-hidden', width ? width : 'w-75')}
    >
      <UploadFile
        accept={accept}
        type={type}
        setFile={handleSuccess}
        maxCount={1}
        onErrorType={handleErrorType}
        onErrorSize={handleErrorSize}
        maxSize={5242880}
      >
        <div
          className={clsx(
            'aspect-square bg-white border border-gray-90 border-dashed relative',
            width ? width : 'w-75'
          )}
        >
          {contentUpload}
        </div>
      </UploadFile>
    </div>
  );
};

export default FormUploadItem;
