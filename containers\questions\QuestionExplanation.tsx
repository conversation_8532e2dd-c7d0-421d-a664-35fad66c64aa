import { useMemo } from 'react';
import { TExerciseResponse, TQuestion } from '@/commons/types';
import { EButtonStyleType, Image, UserButton } from '@/components';
import LawExplanation from './LawExplanation';
import DigitalTextExplanation from './DigitalTextExplanation';
import VideoExplanation from './VideoExplanation';
import clsx from 'clsx';
import { extractContent } from '@/utils/validate';
import { TQuestionExplanationProps } from './questions.types';
import { EIsActive } from '@/commons/enums';

const QuestionExplanation = ({ question }: TQuestionExplanationProps) => {
  const explanationImages = useMemo(
    () =>
      (question as TExerciseResponse)?.exercise_question_explanation_images ||
      (question as TQuestion)?.question_explanation_images ||
      [],
    [question]
  );

  if (!question) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 gap-4 w-full">
      <div
        className={clsx(
          'rounded bg-new-white px-2 py-3',
          (!question?.explanation || !extractContent(question?.explanation)) &&
            !explanationImages.length &&
            !question?.laws.length &&
            'hidden'
        )}
      >
        <div className="w-full bg-white grid grid-cols-1 gap-4 p-4">
          <div className="w-full grid grid-cols-1 gap-3 justify-center items-start">
            {question?.explanation && (
              <div className="flex flex-grow w-full">
                <div
                  dangerouslySetInnerHTML={{ __html: question.explanation }}
                  className="w-full question-editor text-lg html-render"
                />
              </div>
            )}
            {!!explanationImages?.length && (
              <div
                className={clsx(
                  'flex-none grid items-center gap-5',
                  explanationImages.length === 1
                    ? 'grid-cols-1 w-full mx-auto px-12'
                    : 'w-full grid-cols-1 px-24'
                )}
              >
                {explanationImages.map(({ explanation_image, id }) => (
                  <Image
                    key={id}
                    src={explanation_image}
                    classnames="w-full aspect-video"
                    alt="explanation_image"
                  />
                ))}
              </div>
            )}
          </div>
          <div className={clsx('w-full flex justify-end items-end flex-col')}>
            {question.laws
              ?.filter(({ is_active }) => is_active === EIsActive.ENABLE)
              ?.map((law) => (
                <LawExplanation key={law.id} law={law} />
              ))}
          </div>
        </div>
      </div>
      <div className={clsx('flex items-center justify-center gap-2.5')}>
        <DigitalTextExplanation
          digital_texts={
            question.digital_texts?.filter(
              ({ is_active }) => is_active === EIsActive.ENABLE
            ) || []
          }
        />
        {(question as TQuestion).videos && (
          <VideoExplanation
            videos={(question as TQuestion).videos?.filter(
              ({ is_active }) => is_active === EIsActive.ENABLE
            )}
          />
        )}
        <UserButton
          title="質問する"
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          disabled
          className="!w-36"
        />
      </div>
    </div>
  );
};

export default QuestionExplanation;
