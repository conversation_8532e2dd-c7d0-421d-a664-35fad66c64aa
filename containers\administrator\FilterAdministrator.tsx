import { EIsActive } from '@/commons/enums';
import { Button, EButtonStyleType, Input } from '@/components';
import { Select } from '@/components/Select';
import { Checkbox, Radio, RadioChangeEvent } from 'antd';
import { useEffect, useState } from 'react';
import {
  TFilterAdministrator,
  TFilterAdministratorProps,
} from './administrator.types';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

const { Option } = Select;

const FilterAdministrator = ({
  defaultFilter,
  setFilter,
  clearFilter,
}: TFilterAdministratorProps) => {
  const [filterObject, setFilterObject] = useState<TFilterAdministrator>();

  useEffect(() => {
    setFilterObject(defaultFilter);
  }, [defaultFilter]);

  const handleChangeUsername = (username_kana: string) => {
    setFilterObject((current) => ({ ...current, username_kana }));
  };
  const handleChangeRole = (role_id: any) => {
    setFilterObject((current) => ({ ...current, role_id }));
  };

  const changeIsActive = (checkedValue: Array<CheckboxValueType>) => {
    const is_actives = checkedValue as Array<number>;
    setFilterObject((current) => ({ ...current, is_actives }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterObject);
  };
  const handleClearFilter = () => {
    clearFilter?.();
  };
  return (
    <div className="grid grid-cols-1 gap-y-5 w-full">
      <div className="grid grid-cols-2 gap-5">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <label className="text-black-kj font-bold">氏名カナ</label>
          <Input
            size="large"
            classNames="w-full !rounded"
            name="username_kana"
            onChange={handleChangeUsername}
            value={filterObject?.username_kana}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <label className="text-black-kj font-bold">権限</label>
          <Select
            className="w-full rounded"
            size="large"
            onChange={handleChangeRole}
            value={filterObject?.role_id as any}
            placeholder="選択してください"
          >
            <Option value={1}>管理者</Option>
            <Option value={2}>講師</Option>
          </Select>
        </div>
      </div>
      <div className="flex justify-between items-center">
        {/* <div className="grid grid-cols-1 gap-2 !text-sm !font-bold">
          <p className="text-black-kj">有効・無効</p>
          <div className="flex flex-row items-center mt-3">
            <Radio.Group
              onChange={changeIsActive}
              value={filterObject?.is_active}
            >
              <Radio value={EIsActive.ENABLE}>有効</Radio>
              <Radio value={EIsActive.DISABLE}>無効</Radio>
            </Radio.Group>
          </div>
        </div> */}
        <div className="flex flex-col gap-2 !text-sm col-span-1 !font-bold">
          <p className="text-black-kj">有効・無効</p>
          <div className="flex flex-row items-center">
            <Checkbox.Group
              onChange={changeIsActive}
              value={filterObject?.is_actives}
            >
              <Checkbox value={EIsActive.ENABLE}>有効</Checkbox>
              <Checkbox value={EIsActive.DISABLE}>無効</Checkbox>
            </Checkbox.Group>
          </div>
        </div>
        <div className="flex items-end flex-none gap-2.5">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="クリア"
            className="!font-normal"
            onClick={handleClearFilter}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="検索"
            className="!font-normal"
            onClick={handleUpdateFilter}
          />
        </div>
      </div>
    </div>
  );
};

export default FilterAdministrator;
