import { TGetCouponPlansResponse, getCouponPlans } from '@/services/apis';
import { getCouponPlansAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getCouponPlansSaga(
  action: ActionType<typeof getCouponPlansAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;

  try {
    const response = yield call(getCouponPlans, materials);
    const getCouponPlansResponse = response as TGetCouponPlansResponse;
    yield put(getCouponPlansAction.success(getCouponPlansResponse));
    successCallback?.(getCouponPlansResponse);
  } catch (err) {
    yield put(getCouponPlansAction.failure(err));
    failedCallback?.(err);
  }
}
