import { createActionCreator } from 'deox';

export type TResetActionStatus = { type: EUIAction.RESET_ACTION_STATUS };

export enum EUIAction {
  RESET_ACTION_STATUS = 'RESET_ACTION_STATUS',
}

export const uiActions = {
  resetActionStatus: createActionCreator(
    EUIAction.RESET_ACTION_STATUS,
    (resolve) =>
      (actionName: string): TResetActionStatus =>
        resolve({ actionName: actionName.replace('_REQUEST', '') })
  ),
};
