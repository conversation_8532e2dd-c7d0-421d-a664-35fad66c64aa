import { TGetHomeMaterials, TGetHomeResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetHomeAction {
  GET_HOME = 'GET_HOME',
  GET_HOME_REQUEST = 'GET_HOME_REQUEST',
  GET_HOME_SUCCESS = 'GET_HOME_SUCCESS',
  GET_HOME_FAILED = 'GET_HOME_FAILED',
}

// TYPES

export type TGetHomeRequest = {
  type: EGetHomeAction.GET_HOME_REQUEST;
  payload: {
    materials: TGetHomeMaterials;
    successCallback?: (response: TGetHomeResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetHomeSuccess = {
  type: EGetHomeAction.GET_HOME_SUCCESS;
  payload: { response?: TGetHomeResponse };
};

export type TGetHomeFailed = {
  type: EGetHomeAction.GET_HOME_FAILED;
};

export const getHomeAction = {
  request: createActionCreator(
    EGetHomeAction.GET_HOME_REQUEST,
    (resolve) =>
      (
        materials: TGetHomeMaterials,
        successCallback?: (response: TGetHomeResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetHomeRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetHomeAction.GET_HOME_SUCCESS,
    (resolve) =>
      (response?: TGetHomeResponse): TGetHomeSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetHomeAction.GET_HOME_FAILED,
    (resolve) =>
      (error: unknown): TGetHomeFailed =>
        resolve({ error })
  ),
};
