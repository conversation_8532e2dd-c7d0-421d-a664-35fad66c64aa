import {
  deleteQuestionAction,
  getQuestionAction,
  getQuestionsAction,
  postQuestionAction,
  postUpdateQuestionsAction,
  putQuestionAction,
} from '@/store/actions';
import { all, takeLatest } from 'redux-saga/effects';
import { postQuestionSaga } from './post-question';
import { getQuestionSaga } from './get-question';
import { putQuestionSaga } from './put-question';
import { getQuestionsSaga } from './get-questions';
import { postUpdateQuestionsSaga } from './post-update-questions';
import { deleteQuestionSaga } from './delete-question';

export default function* root(): Generator {
  yield all([
    takeLatest(postQuestionAction.request.type, postQuestionSaga),
    takeLatest(getQuestionAction.request.type, getQuestionSaga),
    takeLatest(putQuestionAction.request.type, putQuestionSaga),
    takeLatest(getQuestionsAction.request.type, getQuestionsSaga),
    takeLatest(postUpdateQuestionsAction.request.type, postUpdateQuestionsSaga),
    takeLatest(deleteQuestionAction.request.type, deleteQuestionSaga),
  ]);
}
