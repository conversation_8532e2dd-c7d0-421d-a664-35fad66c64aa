import {
  getPracticeQuestionAction,
  getPracticeQuestionsAction,
  postUpdatePracticeQuestionsAction,
} from '@/store/actions';
import { all, takeLatest } from 'redux-saga/effects';
import { getPracticeQuestionSaga } from './get-practice-question';
import { getPracticeQuestionsSaga } from './get-practice-questions';
import { postUpdatePracticeQuestionsSaga } from './update-show-order-practice-questions';

export default function* root(): Generator {
  yield all([
    takeLatest(
      getPracticeQuestionsAction.request.type,
      getPracticeQuestionsSaga
    ),
    takeLatest(getPracticeQuestionAction.request.type, getPracticeQuestionSaga),
    takeLatest(
      postUpdatePracticeQuestionsAction.request.type,
      postUpdatePracticeQuestionsSaga
    ),
  ]);
}
