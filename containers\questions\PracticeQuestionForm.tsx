import { useEffect, useMemo, useState } from 'react';
import { Form, Radio, Tabs } from 'antd';
import { SelectBigCategory, SelectSubCategory } from '../select';
import { Rule } from 'antd/lib/form';
import validate from '@/utils/validate';
import { validationMessage } from '@/commons/messages-enum';
import { TQuestionFormProps } from '../exam/mock-tests.types';
import { Input, InputNumber } from '@/components';
import { EFolderUpload, EIsActive, EQuestionForm } from '@/commons/enums';
import dynamic from 'next/dynamic';
const EditorBoxForm = dynamic(
  () => import('@/components/EditorBox/EditorBoxInForm'),
  {
    ssr: false,
  }
);
import FormUploadItem from '../exam/FormUploadItem';
import QuestionOtherTab from '../exam/QuestionOtherTab';
import QuestionMultipleChoice from '../exam/QuestionMultipleChoice';
import QuestionSingleChoice from '../exam/QuestionSingleChoice';
import { usePrevious } from '@/utils/hooks';

const { checkMaxLength, hasWhiteSpace, checkBetweenNumberValue } = validate;

const PracticeQuestionForm = ({
  formInstance,
  invalidTab,
  disable,
  handleSubmit,
  isQuestionsEdit = false,
}: TQuestionFormProps) => {
  const type = Form.useWatch('type', formInstance);
  const is_active = Form.useWatch('is_active', formInstance);
  const [activeTab, setActiveTab] = useState<string>('other');
  useEffect(() => {
    if (invalidTab?.value) {
      setActiveTab(invalidTab?.value);
    }
  }, [invalidTab]);

  const rulesOrder = useMemo(() => {
    const checkLength = () => checkBetweenNumberValue('出題No.', 1, 9999999999);
    return [
      {
        required: true,
        message: validationMessage.required('出題No.'),
      },
      checkLength,
    ];
  }, []) as Array<Rule>;

  const onSubmit = (values: any) => {
    handleSubmit?.(values);
  };

  const tabs = useMemo(() => {
    if (!type) {
      return [];
    }
    if (type === EQuestionForm.SINGLE) {
      return [
        {
          label: '解説・そのた',
          key: 'other',
          children: <QuestionOtherTab disable={disable} />,
        },
        {
          label: '設間',
          key: 'part1',
          children: <QuestionSingleChoice name={['sub_questions', 0]} />,
        },
      ];
    }
    return [
      {
        label: '解説・そのた',
        key: 'other',
        children: <QuestionOtherTab disable={disable} />,
      },
      {
        label: '設間1',
        key: 'part1',
        children: <QuestionMultipleChoice name={['sub_questions', 0]} />,
      },
      {
        label: '設間2',
        key: 'part2',
        children: <QuestionMultipleChoice name={['sub_questions', 1]} />,
      },
      {
        label: '設間3',
        key: 'part3',
        children: <QuestionMultipleChoice name={['sub_questions', 2]} />,
      },
      {
        label: '設間4',
        key: 'part4',
        children: <QuestionMultipleChoice name={['sub_questions', 3]} />,
      },
    ];
  }, [disable, type]);

  const getDisableShowOrder = () => {
    if (is_active !== EIsActive.ENABLE) {
      return true;
    }

    if (!isQuestionsEdit) return true;
    return false;
  };

  return (
    <Form
      className="grid grid-cols-1 gap-5 !-mx-5 bg-white"
      disabled={disable}
      form={formInstance}
      onFinish={onSubmit}
    >
      <div className=" bg-new-white px-5">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5">
          <div className="col-span-2 grid grid-cols-1 xl:grid-cols-2 gap-x-5">
            <div className="col-span-1 grid-cols-1 grid gap-2">
              <p className="text-black-kj font-bold">問題番号</p>
              <Form.Item name={'question_no'}>
                <InputNumber
                  classNames="!w-full !rounded !text-sm input-disabled"
                  size="large"
                  maxLength={10}
                  hideControls
                />
              </Form.Item>
            </div>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">大項目名</p>
            <Form.Item name={'category_id'}>
              <SelectBigCategory placeholder={'選択してください'} />
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">出題No.</p>
            <Form.Item name={'show_order'} rules={rulesOrder}>
              <InputNumber
                classNames="!w-full !rounded !text-sm"
                size="large"
                maxLength={4}
                hideControls
                disabled={getDisableShowOrder()}
                stringMode
              />
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">出題年度</p>
            <Form.Item name={'question_year'}>
              <Input
                classNames="!w-full !rounded !text-sm !py-[9px]"
                size="large"
                maxLength={50}
              />
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">配点</p>
            <Form.Item name={'allocation_point'}>
              <InputNumber
                classNames="!w-full !rounded !text-sm"
                size="large"
                maxLength={3}
                hideControls
              />
            </Form.Item>
          </div>
        </div>
        <div className="col-span-1 grid-cols-1 grid gap-2">
          <p className="text-black-kj font-bold">問題タイトル</p>
          <Form.Item name={'title'}>
            <Input
              classNames="w-full !rounded !text-sm !py-[9px]"
              size="large"
              maxLength={255}
            />
          </Form.Item>
        </div>
        <div className="flex gap-5 items-start justify-center flex-col xl:flex-row">
          <div className="flex-grow grid-cols-1 grid gap-2">
            <p className="text-black-kj !font-bold">問題本文</p>
            <Form.Item name={'content'}>
              <EditorBoxForm disable={disable} />
            </Form.Item>
          </div>
          <div className="flex-none grid-cols-1 grid gap-2">
            <p className="text-black-kj !font-bold">問題画像</p>
            <Form.Item name={'image'} className="w-75">
              <FormUploadItem
                accept={'.png,.jpeg,.jpg'}
                type={['png', 'jpeg', 'jpg']}
                name="image"
                label="アップロード"
                disable={disable}
                folder={EFolderUpload.QUESTIONS}
              />
            </Form.Item>
          </div>
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5">
          <div className="col-span-1 grid-cols-1 grid gap-2 !font-bold">
            <p className="text-black-kj">有効・無効</p>
            <Form.Item name="is_active" initialValue={EIsActive.ENABLE}>
              <Radio.Group>
                <Radio className="font-bold" value={EIsActive.ENABLE}>
                  有効
                </Radio>
                <Radio className="font-bold" value={EIsActive.DISABLE}>
                  無効
                </Radio>
              </Radio.Group>
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2 !font-bold">
            <p className="text-black-kj">出題形式</p>
            <Form.Item name="type" initialValue={EQuestionForm.SINGLE}>
              <Radio.Group>
                <Radio className="font-bold" value={EQuestionForm.SINGLE}>
                  単一設間
                </Radio>
                <Radio className="font-bold" value={EQuestionForm.MULTIPLE}>
                  複数設間
                </Radio>
              </Radio.Group>
            </Form.Item>
          </div>
        </div>
      </div>
      <div className="w-full">
        <Tabs
          type="card"
          items={tabs}
          activeKey={activeTab}
          className="question_tab"
          onChange={setActiveTab}
        />
      </div>
    </Form>
  );
};

export default PracticeQuestionForm;
