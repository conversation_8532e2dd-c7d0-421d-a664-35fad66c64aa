import { TListData, TUser } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetExportUsersParams = {
  username: string | undefined;
  video_plans: Array<number>;
  plan_status: number | undefined;
  role_id: number | undefined;
  email: string | undefined;
  register_types?: Array<number>;
  is_actives: Array<number>;
  plans?: Array<number>;
};

export type TGetExportUsersMaterials = {
  params?: TGetExportUsersParams;
};

export type TGetExportUsersResponse = Blob;

export const getExportUsers = async (
  materials: TGetExportUsersMaterials
): Promise<TGetExportUsersResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/export/users', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
