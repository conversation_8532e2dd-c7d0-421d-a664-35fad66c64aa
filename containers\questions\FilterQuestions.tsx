import { EIsActive, EQuestionForm } from '@/commons/enums';
import { Button, EButtonStyleType, Input, InputNumber } from '@/components';
import { Checkbox } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { useEffect, useState } from 'react';
import { SelectBigCategory } from '../select';
import { TFilterQuestionsProps, TQuestionsFilter } from './questions.types';

const FilterQuestions = ({
  defaultFilter,
  setFilter,
  clearFilter,
}: TFilterQuestionsProps) => {
  const [filterObject, setFilterObject] = useState<TQuestionsFilter>();

  useEffect(() => {
    setFilterObject(defaultFilter);
  }, [defaultFilter]);

  const handleChangeCategoryId = (category_id: string) => {
    setFilterObject((current) => ({
      ...current,
      category_id,
    }));
  };
  const handleChangeProblemTitle = (title: string) => {
    setFilterObject((current) => ({
      ...current,
      title,
    }));
  };
  const handleChangeShowOrder = (show_order: number) => {
    setFilterObject((current) => ({
      ...current,
      show_order,
    }));
  };
  const handleChangeQuestionYear = (question_year: string) => {
    setFilterObject((current) => ({
      ...current,
      question_year,
    }));
  };

  const handleChangeProblemId = (id: number) => {
    setFilterObject((current) => ({
      ...current,
      id,
    }));
  };

  const handleChangeContent = (content: string) => {
    setFilterObject((current) => ({
      ...current,
      content,
    }));
  };

  const handlechangeQuestionType = (types: Array<CheckboxValueType>) => {
    setFilterObject((current) => ({ ...current, types }));
  };

  const handlechangeIsActives = (is_actives: Array<CheckboxValueType>) => {
    setFilterObject((current) => ({ ...current, is_actives }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterObject);
  };
  const handleClearFilter = () => {
    clearFilter?.();
  };

  return (
    <div className="grid grid-cols-1 gap-6 w-full">
      <div className="grid grid-cols-2 gap-x-5 gap-y-4 w-full">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">大項目名</p>
          <SelectBigCategory
            value={filterObject?.category_id}
            onChange={handleChangeCategoryId}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">出題タイトル</p>
          <Input
            value={filterObject?.title}
            size="large"
            classNames="w-full !rounded"
            onChange={handleChangeProblemTitle}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">問題ID</p>
          <InputNumber
            value={filterObject?.id}
            size="large"
            hideControls
            classNames="!w-full !rounded"
            onChange={handleChangeProblemId}
            min={0}
            maxLength={20}
            stringMode
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">出題年度</p>
          <Input
            value={filterObject?.question_year}
            size="large"
            classNames="w-full !rounded"
            onChange={handleChangeQuestionYear}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">問題本文</p>
          <Input
            value={filterObject?.content}
            size="large"
            classNames="!w-full !rounded"
            onChange={handleChangeContent}
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-x-5 gap-y-4 w-full">
        <div className="col-span-1 grid grid-cols-2">
          <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj">出題形式</p>
            <div className="flex flex-row items-center">
              <Checkbox.Group
                onChange={handlechangeQuestionType}
                value={filterObject?.types}
              >
                <Checkbox value={EQuestionForm.SINGLE}>単一設問</Checkbox>
                <Checkbox value={EQuestionForm.MULTIPLE}>複数設問</Checkbox>
              </Checkbox.Group>
            </div>
          </div>
        </div>
        <div className="col-span-1 grid grid-cols-2">
          <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj">有効・無効</p>
            <div className="flex flex-row items-center">
              <Checkbox.Group
                onChange={handlechangeIsActives}
                value={filterObject?.is_actives}
              >
                <Checkbox value={EIsActive.ENABLE}>有効</Checkbox>
                <Checkbox value={EIsActive.DISABLE}>無効</Checkbox>
              </Checkbox.Group>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-2.5">
        <Button
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          title="クリア"
          className="!font-normal"
          onClick={handleClearFilter}
        />
        <Button
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          title="検索"
          className="!font-normal"
          onClick={handleUpdateFilter}
        />
      </div>
    </div>
  );
};

export default FilterQuestions;
