import ApiService from '@/services/apis';

export type TGetExportVideosParams = {
  category_id: string | undefined;
  name: string | undefined;
  is_actives: Array<number>;
};

export type TGetExportVideosMaterials = {
  params?: TGetExportVideosParams;
};

export type TGetExportVideosResponse = Blob;

export const getExportVideos = async (
  materials: TGetExportVideosMaterials
): Promise<TGetExportVideosResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/export/videos', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
