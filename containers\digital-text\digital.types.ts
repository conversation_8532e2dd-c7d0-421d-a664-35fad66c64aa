import { TDigital } from '@/commons/types';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

export type TFilterDigital = {
  category_id?: string;
  sub_category_id?: string;
  name?: string;
  is_actives?: Array<CheckboxValueType>;
};

export type TFilterDigitalTextsProps = {
  defaultFilter?: TFilterDigital;
  setFilter?: (value?: TFilterDigital) => void;
  clearFilter?: () => void;
};
