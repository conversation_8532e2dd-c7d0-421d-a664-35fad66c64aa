import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function PreviewIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="15.304"
      viewBox="1225 363.5 16 15.304"
    >
      <g data-name="enter">
        <path
          d="M1235.609 371.674h-10.087a.522.522 0 0 1 0-1.044h10.087a.522.522 0 0 1 0 1.044Zm0 0"
          fill="#3e87bf"
          fillRule="evenodd"
          data-name="Path 7689"
        />
        <path
          d="M1232.826 374.457a.522.522 0 0 1-.369-.891l2.414-2.414-2.414-2.414a.522.522 0 0 1 .739-.739l2.782 2.783a.522.522 0 0 1 0 .738l-2.782 2.783a.518.518 0 0 1-.37.154Zm0 0"
          fill="#3e87bf"
          fillRule="evenodd"
          data-name="Path 7690"
        />
        <path
          d="M1233.348 378.804a7.613 7.613 0 0 1-7.122-4.853.522.522 0 1 1 .97-.38 6.575 6.575 0 0 0 6.152 4.19 6.617 6.617 0 0 0 6.609-6.609 6.617 6.617 0 0 0-6.61-6.609 6.575 6.575 0 0 0-6.15 4.191.522.522 0 1 1-.971-.381 7.613 7.613 0 0 1 7.122-4.853c4.219 0 7.652 3.433 7.652 7.652 0 4.22-3.433 7.652-7.652 7.652Zm0 0"
          fill="#3e87bf"
          fillRule="evenodd"
          data-name="Path 7691"
        />
      </g>
    </svg>
  );
}
