import { TGetAdminsMaterials, TGetAdminsResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetAdminsAction {
  GET_ADMINS = 'GET_ADMINS',
  GET_ADMINS_REQUEST = 'GET_ADMINS_REQUEST',
  GET_ADMINS_SUCCESS = 'GET_ADMINS_SUCCESS',
  GET_ADMINS_FAILED = 'GET_ADMINS_FAILED',
}

// TYPES

export type TGetAdminsRequest = {
  type: EGetAdminsAction.GET_ADMINS_REQUEST;
  payload: {
    materials: TGetAdminsMaterials;
    successCallback?: (response: TGetAdminsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetAdminsSuccess = {
  type: EGetAdminsAction.GET_ADMINS_SUCCESS;
  payload: { response?: TGetAdminsResponse };
};

export type TGetAdminsFailed = {
  type: EGetAdminsAction.GET_ADMINS_FAILED;
};

export const getAdminsAction = {
  request: createActionCreator(
    EGetAdminsAction.GET_ADMINS_REQUEST,
    (resolve) =>
      (
        materials: TGetAdminsMaterials,
        successCallback?: (response: TGetAdminsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetAdminsRequest => {
        return resolve({ materials, successCallback, failedCallback });
      }
  ),
  success: createActionCreator(
    EGetAdminsAction.GET_ADMINS_SUCCESS,
    (resolve) =>
      (response?: TGetAdminsResponse): TGetAdminsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetAdminsAction.GET_ADMINS_FAILED,
    (resolve) =>
      (error: unknown): TGetAdminsFailed =>
        resolve({ error })
  ),
};
