import { all, fork } from 'redux-saga/effects';
import accountSaga from './account';
import notificationSaga from './notification';
import categorySaga from './category';
import subCategorySaga from './sub-category';
import lawsSaga from './laws';
import digitalTextsSaga from './digital-texts';
import authSaga from './auth';
import administratorSaga from './administrator';
import myNotificationSaga from './my-notification';
import mocktestSaga from './mock-tests';
import settingSaga from './settings';
import usersSaga from './users';
import adminsSaga from './admins';
import questionSaga from './questions';
import exerciseSaga from './exercises';
import videoSaga from './videos';
import practiceQuestionSaga from './practice-questions';
import knowledgeBoardsSaga from './knowledge-board';
import homeSaga from './home';
import selectSaga from './select2';
import couponSaga from './coupon';

const rootSaga = function* root(): Generator {
  yield all([
    fork(accountSaga),
    fork(notificationSaga),
    fork(categorySaga),
    fork(subCategorySaga),
    fork(lawsSaga),
    fork(digitalTextsSaga),
    fork(authSaga),
    fork(administratorSaga),
    fork(myNotificationSaga),
    fork(mocktestSaga),
    fork(settingSaga),
    fork(usersSaga),
    fork(adminsSaga),
    fork(questionSaga),
    fork(exerciseSaga),
    fork(videoSaga),
    fork(practiceQuestionSaga),
    fork(knowledgeBoardsSaga),
    fork(homeSaga),
    fork(selectSaga),
    fork(couponSaga),
  ]);
};

export default rootSaga;
