import { TPostLoginResponse, helpers, postLogin } from '@/services/apis';
import { postLoginAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* postLoginSaga(
  action: ActionType<typeof postLoginAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(postLogin, materials);
    const postLoginResponse = response as TPostLoginResponse;
    helpers.storeAccessToken(postLoginResponse.data.access_token);
    yield put(postLoginAction.success(postLoginResponse));
    successCallback?.(postLoginResponse);
  } catch (err) {
    yield put(postLoginAction.failure(err));
    failedCallback?.(err);
  }
  materials.payload.setDisable(false);
}
