import { EIsActive } from '../enums';
import { TCategory } from './Category.types';

export interface TLaws {
  id?: string | number;
  name: string;
  is_active: EIsActive;
  content: string;
  category: TCategory;
  text_id: string | number;
}

export interface DataLaws {
  name: string;
  is_active: number;
  content: string;
  category_id: string | number;
  text_id: string | number | undefined;
}

export type TOptionLaws = {
  id: number;
  name: string;
};
