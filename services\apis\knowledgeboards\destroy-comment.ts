import ApiService from '@/services/apis';

export type TDeleteCommentsPaths = {
  commentId: string | number;
};

export type TDeleteCommentsMaterials = {
  paths: TDeleteCommentsPaths;
};

export type TDeleteCommentsResponse = unknown;

export const deleteComments = async (
  materials: TDeleteCommentsMaterials
): Promise<TDeleteCommentsResponse> => {
  const { paths } = materials;
  const response = await ApiService.delete(`/comments/${paths.commentId}`);
  return response.data;
};
