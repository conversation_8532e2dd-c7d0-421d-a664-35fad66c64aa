import { TGetMeResponse, getMe, helpers } from '@/services/apis';
import { getMeAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getMeSaga(
  action: ActionType<typeof getMeAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const me = helpers.getMeCookie();
    const response = me ? { data: me } : yield call(getMe, materials);
    const getMeResponse = response as TGetMeResponse;
    helpers.storeUserInfo(getMeResponse.data);
    yield put(getMeAction.success(getMeResponse));
    successCallback?.(getMeResponse);
  } catch (err) {
    console.log(err);
    yield put(getMeAction.failure(err));
    failedCallback?.(err);
  }
}
