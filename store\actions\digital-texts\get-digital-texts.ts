import {
  TGetDigitalTextsMaterials,
  TGetDigitalTextsResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetDigitalTextsAction {
  GET_DIGITALTEXTS = 'GET_DIGITALTEXTS',
  GET_DIGITALTEXTS_REQUEST = 'GET_DIGITALTEXTS_REQUEST',
  GET_DIGITALTEXTS_SUCCESS = 'GET_DIGITALTEXTS_SUCCESS',
  GET_DIGITALTEXTS_FAILED = 'GET_DIGITALTEXTS_FAILED',
}

// TYPES

export type TGetDigitalTextsRequest = {
  type: EGetDigitalTextsAction.GET_DIGITALTEXTS_REQUEST;
  payload: {
    materials: TGetDigitalTextsMaterials;
    successCallback?: (response: TGetDigitalTextsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetDigitalTextsSuccess = {
  type: EGetDigitalTextsAction.GET_DIGITALTEXTS_SUCCESS;
  payload: { response?: TGetDigitalTextsResponse };
};

export type TGetDigitalTextsFailed = {
  type: EGetDigitalTextsAction.GET_DIGITALTEXTS_FAILED;
};

export const getDigitalTextsAction = {
  request: createActionCreator(
    EGetDigitalTextsAction.GET_DIGITALTEXTS_REQUEST,
    (resolve) =>
      (
        materials: TGetDigitalTextsMaterials,
        successCallback?: (response: TGetDigitalTextsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetDigitalTextsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetDigitalTextsAction.GET_DIGITALTEXTS_SUCCESS,
    (resolve) =>
      (response?: TGetDigitalTextsResponse): TGetDigitalTextsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetDigitalTextsAction.GET_DIGITALTEXTS_FAILED,
    (resolve) =>
      (error: unknown): TGetDigitalTextsFailed =>
        resolve({ error })
  ),
};
