import { EIsActive } from '@/commons/enums';
import { Button } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Select } from '@/components/Select';
import { Radio, RadioChangeEvent } from 'antd';
import { Fragment, useEffect, useState } from 'react';
import {
  TFilterNotification,
  TFilterNotificationProps,
} from './notification.types';

const { Option } = Select;

const notificationStatus = [
  {
    value: 1,
    title: '公開前 ',
  },
  {
    value: 2,
    title: '公開中',
  },
  {
    value: 3,
    title: ' 公開終了',
  },
];

const FilterNotification = ({
  defaultFilter,
  setFilter,
  clearFilter,
}: TFilterNotificationProps) => {
  const [filterNotification, setFilterNotification] =
    useState<TFilterNotification>();

  useEffect(() => {
    setFilterNotification(defaultFilter);
  }, [defaultFilter]);

  const handleChangeName = (status: string) => {
    setFilterNotification((current) => ({ ...current, status }));
  };

  const changeIsActive = (e: RadioChangeEvent) => {
    const is_active = e.target.value;
    setFilterNotification((current) => ({ ...current, is_active }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterNotification);
  };

  return (
    <Fragment>
      <div className="flex items-end flex-grow gap-5">
        <div className="grid grid-cols-1 gap-2 !text-sm">
          <p className="text-black-kj font-bold">ステータス</p>
          <Select
            className="w-125 rounded"
            size="large"
            placeholder="選択してください"
            onChange={handleChangeName}
            value={filterNotification?.status}
          >
            {notificationStatus.map(({ value, title }) => (
              <Option value={`${value}`} key={value}>
                {title}
              </Option>
            ))}
          </Select>
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm !font-bold">
          <p className="text-black-kj">有効・無効</p>
          <div className="flex flex-row items-center mt-3">
            <Radio.Group
              onChange={changeIsActive}
              value={filterNotification?.is_active}
            >
              <Radio value={EIsActive.ENABLE}>有効</Radio>
              <Radio value={EIsActive.DISABLE}>無効</Radio>
            </Radio.Group>
          </div>
        </div>
      </div>
      <div className="flex items-end flex-none gap-2.5">
        <Button
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          title="クリア"
          className="!font-normal"
          onClick={clearFilter}
        />
        <Button
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          title="検索"
          className="!font-normal"
          onClick={handleUpdateFilter}
        />
      </div>
    </Fragment>
  );
};

export default FilterNotification;
