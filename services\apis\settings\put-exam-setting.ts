import { TExamSetting } from '@/commons/types';
import ApiService from '@/services/apis';

export type TPutExamSettingPayLoad = TExamSetting;

export type TPutExamSettingMaterials = {
  payload: TPutExamSettingPayLoad;
};

export type TPutExamSettingResponse = TExamSetting;

export const putExamSetting = async (
  materials: TPutExamSettingMaterials
): Promise<TPutExamSettingResponse> => {
  const { payload } = materials;
  const response = await ApiService.post('/settings/exam', payload);
  return response.data;
};
