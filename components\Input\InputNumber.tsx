import { useMemo } from 'react';
import clsx from 'clsx';
import { InputNumber as InputNumberAntd } from 'antd';
// import { ArrowUpIcon, ArrowDownIcon } from '@/components/Icon';

import { TInputNumberProps } from './Input.types';

const InputNumber = ({
  classNames,
  hideControls,
  defaultValue,
  value,
  min,
  max,
  disabled,
  prefix,
  onChange,
  size,
  placeholder,
  type,
  stringMode,
}: TInputNumberProps) => {

  const handleOnChange = (inputValue: number | null): void => {
    if (inputValue === null) {
      onChange?.(null);
    }
    const reg = /^-?\d*(\d*)?$/;
    const isNumbericPass = reg.test(`${inputValue}`);
    isNumbericPass && onChange?.(inputValue);
  };

  return (
    <InputNumberAntd
      className={clsx(classNames, 'input-number-customer')}
      controls={!hideControls}
      disabled={disabled}
      defaultValue={defaultValue}
      prefix={prefix}
      value={value}
      min={min}
      max={max}
      size={size}
      type={type}
      onChange={handleOnChange}
      placeholder={placeholder}
      stringMode={stringMode}
    />
  );
};

export default InputNumber;
