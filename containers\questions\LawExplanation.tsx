import { Fragment, useState } from 'react';
import CustomerModal from '@/components/Modal/CustomerModal';
import { TLawExplanationProps } from './questions.types';

const LawExplanation = ({ law }: TLawExplanationProps) => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const handleShowModal = () => {
    setShowModal(true);
  };
  const handleHideModal = () => {
    setShowModal(false);
  };

  return (
    <Fragment>
      <CustomerModal visible={showModal} onClose={handleHideModal} centered>
        <div className="bg-new-white px-2 py-3 w-full lg:w-232">
          <div className="bg-white px-4 py-5">
            <div className="pb-3 border-b border-blue-border border-dashed">
              <h3 className="text-base font-bold text-dark-gray">
                {law?.name}
              </h3>
            </div>
            <div className="p-5 h-80 overflow-scroll scrollbar-hide">
              <div
                className="text-black text-lg !leading-normal html-render"
                dangerouslySetInnerHTML={{ __html: law?.content }}
              />
            </div>
          </div>
        </div>
      </CustomerModal>
      <div
        className="text-green-blue hover:underline text-lg break-all cursor-pointer"
        onClick={() => setShowModal(true)}
      >
        {law.name}
      </div>
    </Fragment>
  );
};

export default LawExplanation;
