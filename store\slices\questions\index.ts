import { TGetQuestionResponse, TGetQuestionsResponse } from '@/services/apis';
import {
  EGetQuestionAction,
  EGetQuestionsAction,
  TGetQuestionSuccess,
  TGetQuestionsSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TQuestionsState {
  getQuestionResponse?: TGetQuestionResponse;
  getQuestionsResponse?: TGetQuestionsResponse;
}

const initialState: TQuestionsState = {
  getQuestionsResponse: undefined,
};

const questionsSlice = createSlice({
  name: 'questions-slice',
  initialState,
  reducers: {
    resetQuestionsSlice: (state) => ({
      ...state,
      getQuestionResponse: undefined,
    }),
    resetGetQuestionsResponse: (state) => ({
      ...state,
      getQuestionsResponse: undefined,
    }),
  },
  extraReducers: {
    [EGetQuestionAction.GET_QUESTION_SUCCESS]: (
      state: TQuestionsState,
      action: TGetQuestionSuccess
    ): TQuestionsState => ({
      ...state,
      getQuestionResponse: action.payload?.response,
    }),
    [EGetQuestionsAction.GET_QUESTIONS_SUCCESS]: (
      state: TQuestionsState,
      action: TGetQuestionsSuccess
    ): TQuestionsState => ({
      ...state,
      getQuestionsResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: questionsReducer,
  actions: { resetQuestionsSlice, resetGetQuestionsResponse },
} = questionsSlice;

export { questionsReducer, resetQuestionsSlice, resetGetQuestionsResponse };
