import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function ArrowLeftIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="7"
      height="12"
      viewBox="1171.25 739.25 7 12"
      {...props}
    >
      <path
        d="m1177 740-5 5 5 5"
        strokeLinejoin="round"
        strokeLinecap="round"
        strokeWidth="1.5"
        stroke={props.strokeColor || '#333'}
        fill="transparent"
        data-name="Path 549"
      />
    </svg>
  );
}
