import { EXCEPTION } from '@/commons/messages-enum';

const NotificationErrors = ({ error }: any) => {
  const mapErrors = error?.data?.errors;
  let errorList: unknown[] = [];
  if (typeof mapErrors === 'object') {
    errorList = Object.values(mapErrors).flat(Infinity);
  }

  const message = error?.data?.message
    ? error?.data?.message
    : EXCEPTION.ACTION_FAILURE;

  return (
    <div>
      {errorList.length > 0
        ? errorList?.map((err: any, index: number) => (
          <div key={index}>{err}</div>
        ))
        : message}
    </div>
  );
};

export default NotificationErrors;
