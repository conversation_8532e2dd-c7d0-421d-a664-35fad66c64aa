import {
  EIsActive,
  ENumberChoice,
  ENumberQuestion,
  EQuestionForm,
  EQuestionType,
} from '@/commons/enums';
import { Fragment, useMemo } from 'react';
import { TQuestionSigleChoiceProps } from './questions.types';
import { TSubQuestion } from '@/commons/types';
import MultipleChoice from './MultipleChoice';

const QuestionMultipleChoice = ({
  type,
  choices,
  disabled,
  choiceSelected,
  choiceCorrect,
  handleChoice,
}: TQuestionSigleChoiceProps) => {
  const choicesAnswers = useMemo(() => {
    const numQuestion =
      type === EQuestionForm.SINGLE
        ? ENumberQuestion.SINGLE
        : ENumberQuestion.MULTIPLE;
    const initChoices = Array(numQuestion).fill(undefined);
    return initChoices.reduce((s: Array<Array<TSubQuestion>>, _, idx) => {
      const items = choices?.filter(
        ({ subquestion_no }) => subquestion_no === idx + 1
      );
      if (!items?.length) {
        return s;
      }
      return [...s, items];
    }, []);
  }, [choices, type]);

  return (
    <div className="grid w-full grid-cols-1 gap-3">
      {choicesAnswers.map((item, idx) => (
        <MultipleChoice
          key={idx}
          choices={item}
          titleNums={`設問${idx + 1}`}
          showTitle={choicesAnswers.length > 1}
          disabled={disabled}
          choiceSelected={choiceSelected}
          choiceCorrect={choiceCorrect}
          handleChoice={handleChoice}
        />
      ))}
    </div>
  );
};

export default QuestionMultipleChoice;
