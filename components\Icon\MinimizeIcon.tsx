import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function MinimizeIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="1225 362.5 16 16"
      {...props}
    >
      <path
        d="M1231.857 372.215v4a.571.571 0 1 1-1.143 0v-2.621l-4.739 4.739a.57.57 0 0 1-.808 0 .571.571 0 0 1 0-.808l4.739-4.74h-2.62a.571.571 0 1 1 0-1.142h4c.315 0 .57.256.57.572Zm2.857-2.858h4a.571.571 0 1 0 0-1.142h-2.62l4.738-4.74a.571.571 0 1 0-.808-.807l-4.739 4.739v-2.621a.571.571 0 1 0-1.142 0v4c0 .316.255.571.571.571Zm1.38 3.429h2.62a.571.571 0 1 0 0-1.143h-4a.571.571 0 0 0-.571.572v4a.571.571 0 1 0 1.142 0v-2.621l4.74 4.739a.57.57 0 0 0 .807 0 .571.571 0 0 0 0-.808l-4.739-4.74Zm-4.809-8.571a.571.571 0 0 0-.571.571v2.62l-4.739-4.738a.571.571 0 1 0-.808.808l4.739 4.739h-2.62a.571.571 0 1 0 0 1.142h4a.571.571 0 0 0 .57-.571v-4a.571.571 0 0 0-.57-.571Z"
        fill="#3e87bf"
        fillRule="evenodd"
        data-name="Minimize-Alt"
      />
    </svg>
  );
}
