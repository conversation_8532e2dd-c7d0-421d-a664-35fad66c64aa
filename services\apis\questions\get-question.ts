import { TQuestion } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetQuestionPath = {
  exam_id: string;
  id: string;
};

export type TGetQuestionMaterials = {
  paths: TGetQuestionPath;
};

export type TGetQuestionResponse = { data: TQuestion };

export const getQuestion = async (
  materials: TGetQuestionMaterials
): Promise<TGetQuestionResponse> => {
  const { paths } = materials;
  const response = await ApiService.get(
    `/exams/${paths.exam_id}/questions/${paths.id}`
  );
  return response.data;
};
