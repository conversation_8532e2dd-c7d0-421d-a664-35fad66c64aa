import { ERoleId } from '@/commons/enums';
import { ListUser } from '@/containers';
import { TFilterUser } from '@/containers/user/user.types';
import helpers from '@/services/helpers';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';

const ListUserPage = ({ query }: { query: TFilterUser }) => {
  return (
    <div className="mx-12 my-4">
      <h1 className="text-lg font-bold text-black-kj">ユーザー一覧</h1>
      <div className="mt-3 w-full">
        <ListUser query={query} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    const { id, ...restQuery } = query;
    const is_actives =
      restQuery?.is_actives
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const video_plans =
      restQuery?.video_plans
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const plan_status = restQuery?.plan_status
      ? Number(restQuery?.plan_status)
      : null;

    const register_types =
      restQuery?.register_types
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const role_id = restQuery?.role_id ? Number(restQuery?.role_id) : null;
    const plans =
      restQuery?.plans
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const filter = removeEmpty({
      ...restQuery,
      is_actives,
      video_plans,
      plan_status,
      role_id,
      register_types,
      plans,
    });
    return {
      props: { query: filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default ListUserPage;
