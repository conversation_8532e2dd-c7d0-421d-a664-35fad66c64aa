import { TGetVideoResponse, TGetVideosResponse } from '@/services/apis';
import {
  EGetVideoAction,
  EGetVideosAction,
  TGetVideoSuccess,
  TGetVideosSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TVideosState {
  getVideosResponse?: TGetVideosResponse;
  getVideoResponse?: TGetVideoResponse;
}

const initialState: TVideosState = {
  getVideosResponse: undefined,
};

const videoSlice = createSlice({
  name: 'video-slice',
  initialState,
  reducers: {
    resetVideoSlice: (state) => ({ ...state, getVideoResponse: undefined }),
    resetGetVideosResponse: (state) => ({
      ...state,
      getVideosResponse: undefined,
    }),
  },
  extraReducers: {
    [EGetVideosAction.GET_VIDEOS_SUCCESS]: (
      state: TVideosState,
      action: TGetVideosSuccess
    ): TVideosState => ({
      ...state,
      getVideosResponse: action.payload?.response,
    }),
    [EGetVideoAction.GET_VIDEO_SUCCESS]: (
      state: TVideosState,
      action: TGetVideoSuccess
    ): TVideosState => ({
      ...state,
      getVideoResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: videoReducer,
  actions: { resetVideoSlice, resetGetVideosResponse },
} = videoSlice;

export { videoReducer, resetVideoSlice, resetGetVideosResponse };
