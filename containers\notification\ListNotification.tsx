import { TNotification } from '@/commons/types';
import { Button, ColumnType, EditIcon, Pagination, Table } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { TFilterNotification } from './notification.types';
import FilterNotification from './FilterNotification';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import { getNotificationsAction } from '@/store/actions';
import { isObjectEmpty, removeEmpty } from '@/utils/functions';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import { resetNotificationSlice } from '@/store/slices/notification';

const ListNotification = ({ query }: { query: TFilterNotification }) => {
  const route = useRouter();
  const { getNotificationsResponse: notifications } = useSelector(
    (state: RootState) => state.notification
  );
  const dispatch = useDispatch();

  const [filter, setFilter] = useState<TFilterNotification>();

  useEffect(() => {
    return () => {
      dispatch(resetNotificationSlice());
    };
  }, [dispatch]);

  const getActions = useCallback(
    (element: TNotification) => {
      return (
        <div className="flex items-center justify-between gap-5">
          <Link
            href={`/notifications/${element.id}/edit`}
            onClick={() => dispatch(setCallBackUrl(route.asPath))}
          >
            <EditIcon />
          </Link>
        </div>
      );
    },
    [dispatch, route.asPath]
  );

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_active: 1,
    };
    setFilter(defaultFilter);
  };

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const columns = useMemo(
    () =>
      [
        {
          title: 'タイトル',
          dataIndex: 'title',
          key: 'title',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-lg break-all">
              {element?.title}
            </div>
          ),
        },
        {
          title: '本文',
          dataIndex: 'content',
          key: 'content',
          render: (element) => (
            <span>
              <div
                className="max-w-70 2xl:max-w-lg line-clamp-3 !leading-normal html-render"
                dangerouslySetInnerHTML={{ __html: element?.content || '' }}
              />
            </span>
          ),
        },
        {
          title: '公開開始日',
          dataIndex: 'start_time',
          key: 'start_time',
          render: (element) => <>{element?.start_time}</>,
        },
        {
          title: '公開終了日',
          dataIndex: 'end_time',
          key: 'end_time',
          render: (element) => <>{element?.end_time}</>,
        },
        {
          title: '有効・無効',
          dataIndex: 'isValid',
          key: 'isValid',
          render: (element) => (
            <>{element?.is_active === 1 ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TNotification>>,
    [getActions]
  );

  const fetchData = useCallback(
    (page: number, filter?: TFilterNotification) => {
      const status = filter?.status ? Number(filter?.status) : undefined;
      const is_active = filter?.is_active;
      const params = { page, status, is_active };
      dispatch(getNotificationsAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      fetchData(1, filter);
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      route.push({ query }, undefined, {
        shallow: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(route.asPath));
    route.push('/notifications/create');
  };

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-end rounded-md">
          <FilterNotification
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        <div className="self-end flex items-center gap-2">
          <Button
            onClick={handleRedirectToAdd}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="新規登録"
            className="!font-normal"
          />
        </div>
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            notifications?.meta.total || 0
          }件`}</h2>
          <Pagination
            current={notifications?.meta?.current_page || 0}
            total={notifications?.meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<TNotification>
          columns={columns}
          dataSource={notifications?.data}
        />
        {notifications?.meta?.total !== 0 && (
          <Pagination
            current={notifications?.meta?.current_page || 0}
            total={notifications?.meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default ListNotification;
