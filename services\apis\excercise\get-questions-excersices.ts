import { TExerciseResponse, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetExersiceParams = {
  page?: number;
  title?: string;
  is_actives?: Array<number>;
};

export type TGetExerciseMaterials = {
  params?: TGetExersiceParams;
};

export type TGetExerciseResponse = TListData<TExerciseResponse>;

export const getExercises = async (
  materials: TGetExerciseMaterials
): Promise<TGetExerciseResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/exercise-questions', { params });
  return response.data;
};
