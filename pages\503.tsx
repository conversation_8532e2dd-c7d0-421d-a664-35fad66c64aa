import { BankLayout } from '@/layouts';
import { TGetCommonMaintainResponse, getCommonMaintain } from '@/services/apis';
import { GetStaticProps } from 'next';

const MaintainPage = ({ data }: { data: TGetCommonMaintainResponse }) => {
  const content = data?.data?.content;
  if (content) {
    return (
      <div
        className="w-full h-full mx-auto html-render"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  }
  return (
    <div className="w-full h-full mx-auto pt-40">
      <div className="flex justify-center text-primary text-2xl font-bold">
        許可されていない操作のため実行できません。
      </div>
    </div>
  );
};

export const getStaticProps: GetStaticProps = async () => {
  try {
    const data = await getCommonMaintain({});
    return {
      props: { data },
      revalidate: 1,
    };
  } catch (error) {
    console.log(error);
    return {
      props: {},
      revalidate: 1,
    };
  }
};

MaintainPage.Layout = BankLayout;

export default MaintainPage;
