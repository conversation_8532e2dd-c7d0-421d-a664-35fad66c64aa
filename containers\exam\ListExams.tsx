import {
  EIsActive,
  EPlacement,
  ERoleId,
  ESetingStatus,
  ETypeNotification,
} from '@/commons/enums';
import { TExam } from '@/commons/types';
import {
  Button,
  ColumnType,
  EButtonStyleType,
  EditIcon,
  Pagination,
  PreviewChildIcon,
  PreviewIcon,
  Table,
} from '@/components';
import { RootState } from '@/store/configureStore';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FilterExams from './FilterExams';
import { TExamsFilter } from './mock-tests.types';
import { getExamsAction } from '@/store/actions';
import { useRouter } from 'next/router';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import { ParsedUrlQueryInput } from 'querystring';
import { getExportQuestionsByExamName } from '@/services/apis';
import moment from 'moment';
import { EXCEPTION } from '@/commons/messages-enum';
import { resetGetExamsResponse } from '@/store/slices/mock-tests';

const ListExams = ({ query }: { query: TExamsFilter }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [filter, setFilter] = useState<TExamsFilter>();
  const { getExamsResponse } = useSelector(
    (state: RootState) => state.mockTest
  );
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);
  const { data, meta } = getExamsResponse || {};

  useEffect(() => {
    return () => {
      dispatch(resetGetExamsResponse());
    };
  }, [dispatch]);

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const getActions = useCallback(
    (element: TExam) => {
      return (
        <div className="flex items-center justify-center gap-5">
          <Link href={`/exams/${element.id}/questions`}>
            <PreviewChildIcon />
          </Link>
          {me?.data.role_id === ERoleId.ADMIN && (
            <Link href={`/exams/${element.id}/edit`}>
              <EditIcon />
            </Link>
          )}
        </div>
      );
    },
    [me?.data.role_id]
  );

  const columns = useMemo(
    () =>
      [
        {
          title: '模擬試験ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => element?.id,
        },
        {
          title: '表示順',
          dataIndex: 'order',
          key: 'order',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-lg break-all">
              {element?.show_order}
            </div>
          ),
        },
        {
          title: '模擬試験名',
          dataIndex: 'name',
          key: 'name',
          render: (element) => (
            <div className="max-w-xl 2xl:max-w-lg break-all">
              {element?.name}
            </div>
          ),
        },
        {
          title: '出題設定',
          dataIndex: 'setting',
          key: 'setting',
          render: (element) => {
            return (
              <div className="max-w-70 2xl:max-w-lg break-all">
                {element?.setting_status === ESetingStatus.DONE ? '完了' : '未'}
              </div>
            );
          },
        },
        {
          title: '有効・無効',
          dataIndex: 'invalid',
          key: 'invalid',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TExam>>,
    [getActions]
  );

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
    };
    setFilter(defaultFilter);
  };

  const fetchData = useCallback(
    (page: number, filter?: TExamsFilter) => {
      const is_actives = filter?.is_actives as Array<number>;
      const name = filter?.name || undefined;
      const params = { page, is_actives, name };
      dispatch(getExamsAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      fetchData(1, filter);
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ pathname: '/exams', query }, undefined, {
        shallow: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleClickToAdd = () => {
    router.push('/exams/create');
  };

  const handleClickToSetting = () => {
    router.push('/exams/setting');
  };

  const goToUploadPage = () => {
    router.push('/exams/upload-questions');
  };

  const handleExportData = async () => {
    const name = filter?.name?.trim() || undefined;
    const is_actives = filter?.is_actives as Array<number>;
    const params = {
      name,
      is_actives,
    };
    try {
      const response = await getExportQuestionsByExamName({ params });
      const fileName = `Kojiro_unkan_questions_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
          <FilterExams
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        {me?.data.role_id === ERoleId.ADMIN && (
          <div className="self-end flex items-center gap-2">
            <Button
              onClick={handleExportData}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="出力"
              className="!font-normal"
            />
            <Button
              onClick={goToUploadPage}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="アップロード"
              className="!font-normal"
            />
            <Button
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="模擬試験設定"
              className="!font-normal"
              onClick={handleClickToSetting}
            />
            <Button
              onClick={handleClickToAdd}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="新規登録"
              className="!font-normal"
            />
          </div>
        )}
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            meta?.total || 0
          }件`}</h2>
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<TExam> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default ListExams;
