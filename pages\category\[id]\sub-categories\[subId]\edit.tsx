import { Input } from '../../../../../components/Input';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Form, InputNumber, Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  deleteSubCategory,
  getSubCategory,
  helpers,
  updateSubCategory,
} from '@/services/apis';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { EIsActive, ERoleId, ETypeNotification } from '@/commons/enums';
import { TSubCategory } from '@/commons/types';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';
import { GetServerSideProps } from 'next';
import { useHistory } from '@/utils/contexts';

const EditSmallCatagory = () => {
  const [dataDetail, setDataDetail] = useState<TSubCategory | null>(null);
  const [form] = Form.useForm();
  const router = useRouter();
  const { subId, id } = router.query;
  const [isVisibleEdit, setVisibleEdit] = useState<boolean>(false);
  const { hasDot, checkMaxLength } = validate;
  const historyManager = useHistory();
  const canGoBack = historyManager.canGoBack();

  const fetchData = async () => {
    try {
      const data = await getSubCategory(subId);
      const paramData = data?.data;
      if (paramData) {
        setDataDetail(paramData);
        form.setFieldsValue({
          is_active: paramData?.is_active,
          name: paramData?.name,
          show_order: paramData?.show_order,
        });
      }
    } catch (error: any) {
      showNotification(ETypeNotification?.ERROR, error?.data?.message);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const onBack = () => {
    if (canGoBack) {
      router.back();
      return;
    }
    router?.push(`/category/${id}/sub-categories`);
  };

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-2">
        <Form.Item name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onCloseModalEdit = () => {
    setVisibleEdit(false);
  };

  const onOpenModalEdit = () => {
    setVisibleEdit(true);
  };

  const onFinish = async () => {
    try {
      const values = form.getFieldsValue();
      const payload = {
        name: values?.name,
        is_active: values?.is_active,
        show_order: values?.show_order,
      };
      const data = await updateSubCategory(subId, payload);
      if (data) {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        onBack();
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onDelete = async () => {
    try {
      const data = await deleteSubCategory(subId);
      showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      onBack();
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  return (
    <div className="mx-12 mt-4">
      <span className="text-lg font-bold">小項目編集</span>
      <ConfirmModal
        visible={isVisibleEdit}
        onClose={onCloseModalEdit}
        content={MODAL_MESSAGE?.CONFIRM_EDIT}
        onConfirm={onFinish}
      />
      <Form form={form} onFinish={onOpenModalEdit}>
        <div className="bg-alice-blue-light py-4 px-5 mt-3">
          <div className="grid grid-cols-2 gap-5">
            <div className="flex flex-row">
              <span className="text-base font-bold">大項目名</span>
              <span className="ml-3 text-primary text-base">
                {dataDetail?.category?.name}
              </span>
            </div>
            <div className="flex flex-row">
              <span className="text-base font-bold">小項目ID</span>
              <span className="ml-3 text-primary text-base">
                {dataDetail?.id}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 justify-between mt-6 gap-5">
            <div>
              <span className="text-textGray text-sm font-bold">小項目名</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('小項目名'),
                    },
                    () => checkMaxLength('小項目名', 255),
                  ] as Rule[]
                }
                className={'!mb-0'}
                name={'name'}
              >
                <Input classNames={'!w-full !mt-2'} />
              </Form.Item>
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">表示順</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('表示順'),
                    },
                    checkMaxLength('表示順', 3),
                    hasDot('表示順', 3),
                  ] as Rule[]
                }
                name={'show_order'}
              >
                <InputNumber className={'!w-full !mt-2'} min={0} />
              </Form.Item>
            </div>
          </div>
          <div className="mt-8">
            <div className="flex flex-row">
              <span className="text-textGray text-sm font-bold">
                有効・無効
              </span>
            </div>
            {ratioRender()}
          </div>
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={onBack}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
          />
        </div>
      </Form>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default EditSmallCatagory;
