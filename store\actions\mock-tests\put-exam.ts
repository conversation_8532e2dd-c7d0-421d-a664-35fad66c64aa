import { TPutExamMaterials, TPutExamResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPutExamAction {
  PUT_EXAM = 'PUT_EXAM',
  PUT_EXAM_REQUEST = 'PUT_EXAM_REQUEST',
  PUT_EXAM_SUCCESS = 'PUT_EXAM_SUCCESS',
  PUT_EXAM_FAILED = 'PUT_EXAM_FAILED',
}

// TYPES

export type TPutExamRequest = {
  type: EPutExamAction.PUT_EXAM_REQUEST;
  payload: {
    materials: TPutExamMaterials;
    successCallback?: (response: TPutExamResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPutExamSuccess = {
  type: EPutExamAction.PUT_EXAM_SUCCESS;
  payload: { response?: TPutExamResponse };
};

export type TPutExamFailed = {
  type: EPutExamAction.PUT_EXAM_FAILED;
};

export const putExamAction = {
  request: createActionCreator(
    EPutExamAction.PUT_EXAM_REQUEST,
    (resolve) =>
      (
        materials: TPutExamMaterials,
        successCallback?: (response: TPutExamResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TPutExamRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPutExamAction.PUT_EXAM_SUCCESS,
    (resolve) =>
      (response?: TPutExamResponse): TPutExamSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPutExamAction.PUT_EXAM_FAILED,
    (resolve) =>
      (error: unknown): TPutExamFailed =>
        resolve({ error })
  ),
};
