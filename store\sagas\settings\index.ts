import { getExamSettingAction, putExamSettingAction } from '@/store/actions';
import { all, takeLatest } from 'redux-saga/effects';
import { getExamSettingSaga } from './get-exam-setting';
import { putExamSettingSaga } from './put-exam-setting';

export default function* root(): Generator {
  yield all([
    takeLatest(getExamSettingAction.request.type, getExamSettingSaga),
    takeLatest(putExamSettingAction.request.type, putExamSettingSaga),
  ]);
}
