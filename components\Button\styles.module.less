:global {
  .Button {
    width: fit-content;

    & .ant-btn {
      height: theme('height.fit');
      min-height: theme('height[7.5]');
      font-weight: theme('fontWeight.bold');
      border-radius: theme('borderRadius.full');
      padding: theme('spacing[0.5]') theme('spacing.6');
      @apply flex items-center justify-center gap-2;
    }

    & .ant-btn.rounded {
      border-radius: theme('borderRadius.md');
    }

    & .ant-btn.primary {
      border-color: theme('colors.primary');
      background-color: theme('colors.primary');
      color: theme('colors.white');
    }

    & .ant-btn.primary-variant-2 {
      border-color: theme('colors.primary-variant-2');
      background-color: theme('colors.primary-variant-2');
      color: theme('colors.white');
    }

    & .ant-btn.outline-primary {
      border-color: theme('colors.primary');
      background-color: theme('colors.white');
      color: theme('colors.primary');
    }

    & .ant-btn.primary-variant {
      border-color: theme('colors.primary-variant');
      background-color: theme('colors.primary-variant');
      color: theme('colors.white');
    }

    & .ant-btn.green {
      border-color: theme('colors.green');
      background-color: theme('colors.green');
      color: theme('colors.white');
    }

    & .ant-btn.danger {
      border-color: theme('colors.danger');
      background-color: theme('colors.danger');
      color: theme('colors.white');
    }

    & .ant-btn-lg {
      border-radius: theme('borderRadius.full');
      min-width: theme('width.84');
      padding: theme('spacing.4') theme('spacing.7');
    }

    & .ant-btn-sm {
      border-radius: theme('borderRadius.full');
      min-width: theme('width.24');
      padding: theme('spacing[0.5]') theme('spacing.5');
    }

    & .ant-btn.outline-green-blue {
      border-color: theme('colors.green-blue');
      background-color: theme('colors.white');
      color: theme('colors.green-blue');
    }

    & .ant-btn.green-blue {
      border-color: theme('colors.green-blue');
      background-color: theme('colors.green-blue');
      color: theme('colors.white');
    }

    & .ant-btn.bright-orange {
      border-color: theme('colors.bright-orange');
      background-color: theme('colors.bright-orange');
      color: theme('colors.black');
    }
  }
}