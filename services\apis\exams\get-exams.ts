import { TExam, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetExamsParams = {
  page?: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetExamsMaterials = {
  params?: TGetExamsParams;
};

export type TGetExamsResponse = TListData<TExam>;

export const getExams = async (
  materials: TGetExamsMaterials
): Promise<TGetExamsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/exams', { params });
  return response.data;
};
