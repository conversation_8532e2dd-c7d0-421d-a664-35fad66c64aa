import { TGetUsersResponse } from '@/services/apis';
import { EGetUsersAction, TGetUsersSuccess } from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TusersState {
  getUsersResponse?: TGetUsersResponse;
}

const initialState: TusersState = {
  getUsersResponse: undefined,
};

const userSlice = createSlice({
  name: 'user-slice',
  initialState,
  reducers: {
    resetUserSlice: () => initialState,
  },
  extraReducers: {
    [EGetUsersAction.GET_USERS_SUCCESS]: (
      state: TusersState,
      action: TGetUsersSuccess
    ): TusersState => ({
      ...state,
      getUsersResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: userReducer,
  actions: { resetUserSlice },
} = userSlice;

export { userReducer, resetUserSlice };
