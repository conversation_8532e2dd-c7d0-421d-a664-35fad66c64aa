import { EIsActive } from '@/commons/enums';
import { Button, EButtonStyleType, Input } from '@/components';
import { Checkbox } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { useEffect, useState } from 'react';
import { TExamsFilter, TExamsFilterProps } from './mock-tests.types';

const FilterExams = ({
  defaultFilter,
  setFilter,
  clearFilter,
}: TExamsFilterProps) => {
  const [filterObject, setFilterObject] = useState<TExamsFilter>();

  useEffect(() => {
    setFilterObject(defaultFilter);
  }, [defaultFilter]);

  const handleChangeName = (name: string) => {
    setFilterObject((current) => ({ ...current, name }));
  };

  const handlechangeIsActives = (is_actives: Array<CheckboxValueType>) => {
    setFilterObject((current) => ({ ...current, is_actives }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterObject);
  };
  const handleClearFilter = () => {
    clearFilter?.();
  };

  return (
    <div className="grid grid-cols-1 xl:grid-cols-2 w-full gap-5">
      <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
        <p className="text-black-kj font-bold">模擬試験名</p>
        <Input
          value={filterObject?.name}
          size="large"
          classNames="w-full !rounded"
          onChange={handleChangeName}
        />
      </div>
      <div className="col-span-1 flex justify-between items-end">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
          <p className="text-black-kj">有効・無効</p>
          <div className="flex flex-row items-center mt-3">
            <Checkbox.Group
              onChange={handlechangeIsActives}
              value={filterObject?.is_actives}
            >
              <Checkbox value={EIsActive.ENABLE}>有効</Checkbox>
              <Checkbox value={EIsActive.DISABLE}>無効</Checkbox>
            </Checkbox.Group>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="クリア"
            className="!font-normal"
            onClick={handleClearFilter}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="検索"
            className="!font-normal"
            onClick={handleUpdateFilter}
          />
        </div>
      </div>
    </div>
  );
};

export default FilterExams;
