import { TGetCategoriesResponse, getUsers } from '@/services/apis';
import { getUsersAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getUsersSaga(
  action: ActionType<typeof getUsersAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getUsers, materials);
    const getUsersResponse = response as any;
    yield put(getUsersAction.success(getUsersResponse));
    successCallback?.(getUsersResponse);
  } catch (err) {
    yield put(getUsersAction.failure(err));
    failedCallback?.(err);
  }
}
