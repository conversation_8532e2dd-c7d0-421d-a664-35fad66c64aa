:global {
  .Pagination {
    & .ant-pagination-item {
      font-size: theme('fontSize.xs');
      font-weight: theme('fontWeight.bold');
      color: theme("colors.black-kj");
      border: none;
      width: theme('width.6');
      height: theme('height.6');
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: theme('width.fit');
      min-width: theme('height.fit');
      line-height: normal;
    }

    & .ant-pagination-item-active {
      background-color: theme('colors.primary-variant');
    }

    & .ant-pagination-item-active a {
      color: theme("colors.white");
    }

    & .ant-pagination-prev,
    & .ant-pagination-item {
      margin-right: theme("spacing.1");
    }

    & .ant-pagination {
      display: flex;
      align-items: center;
    }
  }

}