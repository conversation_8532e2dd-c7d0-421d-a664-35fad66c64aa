import { TDeleteExamMaterials, TDeleteExamResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EDeleteExamAction {
  DELETE_EXAM = 'DELETE_EXAM',
  DELETE_EXAM_REQUEST = 'DELETE_EXAM_REQUEST',
  DELETE_EXAM_SUCCESS = 'DELETE_EXAM_SUCCESS',
  DELETE_EXAM_FAILED = 'DELETE_EXAM_FAILED',
}

// TYPES

export type TDeleteExamRequest = {
  type: EDeleteExamAction.DELETE_EXAM_REQUEST;
  payload: {
    materials: TDeleteExamMaterials;
    successCallback?: (response: TDeleteExamResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TDeleteExamSuccess = {
  type: EDeleteExamAction.DELETE_EXAM_SUCCESS;
  payload: { response?: TDeleteExamResponse };
};

export type TDeleteExamFailed = {
  type: EDeleteExamAction.DELETE_EXAM_FAILED;
};

export const deleteExamAction = {
  request: createActionCreator(
    EDeleteExamAction.DELETE_EXAM_REQUEST,
    (resolve) =>
      (
        materials: TDeleteExamMaterials,
        successCallback?: (response: TDeleteExamResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TDeleteExamRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EDeleteExamAction.DELETE_EXAM_SUCCESS,
    (resolve) =>
      (response?: TDeleteExamResponse): TDeleteExamSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EDeleteExamAction.DELETE_EXAM_FAILED,
    (resolve) =>
      (error: unknown): TDeleteExamFailed =>
        resolve({ error })
  ),
};
