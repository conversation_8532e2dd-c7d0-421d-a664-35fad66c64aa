const formatDateTime = (date: string) => {
  const dateObj = new Date(date);
  const dateString =
    dateObj.getFullYear() +
    '/' +
    ('0' + (dateObj.getMonth() + 1)).slice(-2) +
    '/' +
    ('0' + dateObj.getDate()).slice(-2) +
    ' ' +
    ('0' + dateObj.getHours()).slice(-2) +
    ':' +
    ('0' + dateObj.getMinutes()).slice(-2) +
    ':' +
    ('0' + dateObj.getSeconds()).slice(-2);
  return dateString;
};

export { formatDateTime };
