import { Button, ConfirmModal, EButtonStyleType, Input } from '@/components';
import { Select } from '@/components/Select';
import { Input as AntdInput, DatePicker, Form, Radio } from 'antd';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';
import { useRouter } from 'next/router';
import locale from 'antd/lib/date-picker/locale/ja_JP';
import moment from 'moment-timezone';
import { useEffect, useMemo, useState } from 'react';
import { showNotification } from '@/utils/functions';
import {
  EFormatDateTime,
  EPlacement,
  ERegisterType,
  ERoleId,
  ETypeNotification,
  EVideoPlan,
} from '@/commons/enums';
import { deleteUsers, getUser, putEditUser } from '@/services/apis/users';
import { GetServerSideProps } from 'next';
import helpers from '@/services/helpers';
import { RootState } from '@/store/configureStore';
import { useSelector } from 'react-redux';
import NotificationErrors from '@/components/NotificationErrors';

const AddNewUserPage = () => {
  moment.tz.setDefault('Asia/Tokyo');
  const [form] = Form.useForm();
  const router = useRouter();
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const [isShowDeleteModal, setIsShowDeleteModal] = useState<boolean>(false);
  const [isFirstStartDate, setFirstStartDate] = useState<boolean>(true);
  const [isFirstEndDate, setFirstEndDate] = useState<boolean>(true);
  const [registerType, setRegisterType] = useState<number>();
  const [planType, setPlanType] = useState<number>();
  const [lastLoginAt, setLastLoginAt] = useState<string>('');
  const {
    disabledDateBeforNow,
    disabledDateBeforeOrEqualNow,
    checkMaxString,
    checkfieldUsernameKana,
    checkFieldLoginId,
    checkFieldEmail,
    checkFieldPassword,
    validateStartEndDateByDay,
    validateStartDateByDay,
  } = validate;
  const { id } = router.query;
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const onHandleOpenModal = () => {
    setIsShowModal(true);
  };

  const onHandleCloseModal = () => {
    setIsShowModal(false);
  };

  const onHandleOpenDeleteModal = () => {
    setIsShowDeleteModal(true);
  };

  const onHandleCloseDeleteModal = () => {
    setIsShowDeleteModal(false);
  };

  const onHandleConfirmSubmit = async () => {
    const values = form.getFieldsValue();
    const payload = {
      id,
      username: values?.username,
      username_kana: values.username_kana,
      role_id: values.role_id,
      video_plan: values.video_plan,
      email: values.email,
      login_id: values.login_id,
      password: values.password,
      is_active: values.is_active,
      plan_start_date: values?.plan_start_date?.format('YYYY-MM-DD'),
      plan_end_date: values?.plan_end_date?.format('YYYY-MM-DD'),
    };

    try {
      const response = await putEditUser(payload);
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      backToList();
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };
  const onHandleConfirmDelete = async () => {
    const payload = {
      id,
    };
    try {
      const response = await deleteUsers(payload);
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      backToList();
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };
  const backToList = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/users');
  };

  const onFetchUsers = async () => {
    const payload = {
      id,
    };

    try {
      const response = await getUser(payload);
      form.setFieldsValue({
        username: response.username,
        username_kana: response.username_kana,
        role_id: response.role_id,
        video_plan: response.video_plan,
        plan_start_date: moment(response.plan_start_date),
        plan_end_date: moment(response.plan_end_date),
        email: response.email,
        login_id: response.login_id,
        is_active: response.is_active,
      });
      setPlanType(response.plan);
      setRegisterType(response.register_type);
      setLastLoginAt(response.last_login_at);
    } catch (error: any) {
      if (error?.request.status !== 503) {
        showNotification(
          ETypeNotification.ERROR,
          error?.data?.message
            ? error?.data?.message
            : EXCEPTION.ACTION_FAILURE,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
  };

  const onValuesChange = () => {
    const values = form.getFieldsValue();
    if (values?.plan_start_date && values?.plan_end_date) {
      form.validateFields(['plan_start_date', 'plan_end_date']);
    }
  };

  useEffect(() => {
    onFetchUsers();
  }, []);

  const planText = useMemo(() => {
    if (planType === 1) {
      return '無料';
    } else if (planType === 2) {
      return '有料';
    }
    return '';
  }, [planType]);

  const registerTypeText = useMemo(() => {
    if (registerType === 1) {
      return 'EC';
    } else if (registerType === 2) {
      return '管理';
    } else if (registerType === 3) {
      return 'アプリ';
    }
    return null;
  }, [registerType]);

  const lastLoginLabel = useMemo(() => {
    if (!lastLoginAt) {
      return null;
    }
    return moment(lastLoginAt).format(EFormatDateTime.YYYY_MM_DD_HH_MM);
  }, [lastLoginAt]);

  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">ユーザー編集</div>
      <Form
        onValuesChange={onValuesChange}
        onFinish={onHandleOpenModal}
        form={form}
        initialValues={{
          is_active: 1,
          role_id: 1,
          video_plan: 1,
        }}
      >
        <div className="bg-alice-blue-light py-4 px-5">
          <div className="flex flex-row justify-between items-center mb-6">
            <div className="flex items-center gap-12">
              <div className="text-base font-bold">
                ユーザーID <span className="text-primary ml-1">{id}</span>
              </div>
              {lastLoginLabel && (
                <div className="text-danger font-semibold text-sm flex items-center gap-6">
                  <span>最終ログイン日時</span>
                  <span>{lastLoginLabel}</span>
                </div>
              )}
            </div>
            <div className="flex flex-col gap-2 justify-end text-right">
              {planText && (
                <div className="text-textGray text-sm font-bold">
                  <span>利用タイプ: </span>
                  <span>{planText}</span>
                </div>
              )}
              {registerTypeText && (
                <div className="text-textGray text-sm font-bold">
                  <span>購入方法: </span>
                  <span>{registerTypeText}</span>
                </div>
              )}
            </div>
          </div>
          {/* row 1 */}
          <div className="flex flex-row justify-between mb-4">
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">氏名</div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('氏名'),
                    },
                    () => checkMaxString('氏名', 255),
                  ] as Rule[]
                }
                name={'username'}
                className="!mb-0"
              >
                <Input classNames={'w-2/5 mt-2'} maxLength={255} />
              </Form.Item>
            </div>
            <span className="w-12"></span>
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                氏名カナ
              </div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('氏名カナ'),
                    },
                    () => checkfieldUsernameKana('氏名カナ', 255),
                  ] as Rule[]
                }
                name={'username_kana'}
                className="!mb-0"
              >
                <Input classNames={'!w-full'} maxLength={255} />
              </Form.Item>
            </div>
          </div>
          {/* row 3 */}
          <div className="flex flex-row justify-between mb-4">
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                利用期間
              </div>
              <div className="grid grid-cols-2 gap-5 mt-2">
                <Form.Item
                  rules={
                    [
                      {
                        required: true,
                        message: validationMessage.required('開始日'),
                      },
                      validateStartDateByDay(isFirstStartDate),
                    ] as Rule[]
                  }
                  className={'!mb-0'}
                  name={'plan_start_date'}
                >
                  <DatePicker
                    onChange={() => setFirstStartDate(false)}
                    inputReadOnly
                    locale={locale}
                    format={'YYYY-MM-DD'}
                    // disabledDate={disabledDateBeforeOrEqualNow}
                    placeholder=""
                    className="w-full"
                  />
                </Form.Item>
                <Form.Item
                  rules={
                    [
                      {
                        required: true,
                        message: validationMessage.required('終了日'),
                      },
                      validateStartEndDateByDay(isFirstEndDate),
                    ] as Rule[]
                  }
                  className={'!mb-0'}
                  name={'plan_end_date'}
                >
                  <DatePicker
                    onChange={() => setFirstEndDate(false)}
                    inputReadOnly
                    locale={locale}
                    format={'YYYY-MM-DD'}
                    disabledDate={disabledDateBeforeOrEqualNow}
                    placeholder=""
                    className="w-full"
                  />
                </Form.Item>
              </div>
            </div>
            <span className="w-12"></span>
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                メールアドレス
              </div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('メールアドレス'),
                    },
                    () => checkFieldEmail('メールアドレス', 255),
                  ] as Rule[]
                }
                name={'email'}
                className="!mb-0"
              >
                <Input classNames={'w-2/5 mt-2'} maxLength={255} />
              </Form.Item>
            </div>
          </div>
          {/* row 4 */}
          <div className="flex flex-row justify-between mb-4">
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                ログインID
              </div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('ログインID'),
                    },
                    () => checkFieldLoginId('ログインID', 8, 20),
                  ] as Rule[]
                }
                name={'login_id'}
                className="!mb-0"
              >
                <Input classNames={'w-2/5 mt-2'} maxLength={20} />
              </Form.Item>
            </div>
            <span className="w-12"></span>
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                パスワード
              </div>
              <Form.Item
                rules={
                  [() => checkFieldPassword('パスワード', 10, 20)] as Rule[]
                }
                name={'password'}
                className="!mb-0"
              >
                {/* <Input classNames={'w-2/5 mt-2'} maxLength={255} /> */}
                <AntdInput
                  className={'w-2/5 mt-2'}
                  autoComplete="new-password"
                />
              </Form.Item>
              <div style={{ color: '#ff4d4f' }}>
                {validationMessage.new_password()}
              </div>
            </div>
          </div>
          {/* row 2 */}
          <div className="grid grid-cols-2 xl:grid-cols-4 gap-6 mb-4">
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                対策動画
              </div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('対策動画'),
                    },
                  ] as Rule[]
                }
                name={'video_plan'}
                className="!mb-0"
              >
                <Radio.Group>
                  <Radio className="font-bold" value={EVideoPlan.HAVE_VIDEO}>
                    あり
                  </Radio>
                  <Radio className="font-bold" value={EVideoPlan.NOT_VIDEO}>
                    なし
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </div>
            <div className="w-full">
              <div className="flex flex-row">
                <span className="text-textGray text-sm font-bold">権限</span>
              </div>
              <div className="flex flex-row items-center mt-2">
                <Form.Item
                  name="role_id"
                  rules={[
                    {
                      required: true,
                      message: validationMessage.required('権限'),
                    },
                  ]}
                  className="!mb-0"
                >
                  <Radio.Group>
                    <Radio className="font-bold" value={1}>
                      ユーザー
                    </Radio>
                    <Radio className="font-bold" value={2}>
                      講師
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
            <div className="w-full">
              <div className="flex flex-row">
                <span className="text-textGray text-sm font-bold">
                  有効・無効
                </span>
              </div>
              <div className="flex flex-row items-center mt-2">
                <Form.Item
                  name="is_active"
                  rules={[
                    {
                      required: true,
                      message: validationMessage.required('有効・無効'),
                    },
                  ]}
                  className="!mb-0"
                >
                  <Radio.Group>
                    <Radio className="font-bold" value={1}>
                      有効
                    </Radio>
                    <Radio className="font-bold" value={2}>
                      無効
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={backToList}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            onClick={onHandleOpenDeleteModal}
            size={'small'}
            styleType={EButtonStyleType.DANGER}
            title={'削除'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
          />
        </div>
      </Form>
      <ConfirmModal
        visible={isShowModal}
        onClose={onHandleCloseModal}
        onConfirm={onHandleConfirmSubmit}
        content={
          registerType === ERegisterType.EC_SITE
            ? MODAL_MESSAGE.CONFIRM_EDIT_USER_FROM_EC_SITE
            : MODAL_MESSAGE.CONFIRM_EDIT
        }
      />
      <ConfirmModal
        visible={isShowDeleteModal}
        onClose={onHandleCloseDeleteModal}
        onConfirm={onHandleConfirmDelete}
        content={MODAL_MESSAGE.CONFIRM_DELETE}
      />
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default AddNewUserPage;
