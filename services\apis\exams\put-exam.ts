import { EIsActive } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPutExamPath = {
  id: string;
};

export type TPutExamPayload = {
  show_order: number;
  name: string;
  is_active: EIsActive;
};

export type TPutExamMaterials = {
  paths: TPutExamPath;
  payload: TPutExamPayload;
};

export type TPutExamResponse = unknown;

export const putExam = async (
  materials: TPutExamMaterials
): Promise<TPutExamResponse> => {
  const { payload, paths } = materials;
  const response = await ApiService.put(`/exams/${paths.id}`, payload);
  return response.data;
};
