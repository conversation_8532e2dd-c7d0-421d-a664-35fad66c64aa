import { TComment } from '@/commons/types';
import { Button, EButtonStyleType } from '@/components';
import { TPutEditCommentMaterials } from '@/services/apis';
import validate from '@/utils/validate';
import { Form } from 'antd';
import clsx from 'clsx';
import { useEffect } from 'react';
import autosize from 'autosize';
import { ERoleId } from '@/commons/enums';

type TEditInputProps = {
  editComment: TComment;
  setEditComment: (comment: TComment | undefined) => void;
  handleUpdateComment: (materials: TPutEditCommentMaterials) => void;
};

const EditInput = ({
  editComment,
  setEditComment,
  handleUpdateComment,
}: TEditInputProps) => {
  const [editForm] = Form.useForm();

  const putEditComment = async () => {
    const materials = {
      paths: {
        id: editComment.id.toString(),
      },
      payload: {
        content: editForm.getFieldValue('content'),
        _method: 'PUT',
      },
    };
    handleUpdateComment(materials);
  };

  const autoSizeHeight = () => {
    const refTextArea = document.querySelector('textarea');
    if (refTextArea) autosize(refTextArea);
  };

  useEffect(() => {
    if (editComment) {
      editForm.setFieldsValue({
        content: editComment.content,
      });
    }
  }, [editComment]);

  return (
    <Form form={editForm} onFinish={putEditComment}>
      {/* Edit input */}
      <div className="flex justify-between items-end font-bold border rounded-md bg-white mt-4 py-4 px-5">
        <div className="grow border rounded-md py-2 px-4 mr-4">
          {editComment && (
            <div className="flex items-center">
              <div className="text-primary mr-6">
                {`＞${editComment.comment_no}.`}
              </div>
              <div className="border rounded-full min-w-[96px] bg-[#ff0000] py-[4px] text-white text-center">
                {editComment.admin &&
                editComment.admin.role_id === ERoleId.ADMIN
                  ? '管理者'
                  : '講師'}
              </div>
            </div>
          )}
          <Form.Item
            name="content"
            className="w-full"
            rules={[() => validate.checkRequiredContentComment('本文', 65535)]}
          >
            <textarea
              onFocus={autoSizeHeight}
              rows={2}
              className={clsx(
                'w-full resize-none box-border',
                'max-h-60 min-h-8',
                'focus:outline-none'
              )}
            />
          </Form.Item>
        </div>
        <div className="grid grid-cols-1 gap-2">
          <Button
            onClick={() => setEditComment(undefined)}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'キャンセル'}
            className="w-28"
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY}
            title={'更新'}
            className="w-28"
          />
        </div>
      </div>
    </Form>
  );
};

export default EditInput;
