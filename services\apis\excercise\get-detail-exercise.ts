import ApiService from '@/services/apis';

export type TGetDetailExersiceParams = {
  id?: string;
};

export type TGetDetailExerciseMaterials = {
  params?: TGetDetailExersiceParams;
};

export type TGetDetailExerciseResponse = any;

export const getDetailExercises = async (
  materials: TGetDetailExerciseMaterials
): Promise<TGetDetailExerciseResponse> => {
  const { params } = materials;
  const response = await ApiService.get(`/exercise-questions/${params?.id}`);
  return response.data;
};
