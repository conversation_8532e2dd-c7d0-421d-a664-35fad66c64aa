import { TGetLawsResponse } from '@/services/apis';
import { EGetLawsAction, TGetLawsSuccess } from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TlawState {
  getLawsResponse?: TGetLawsResponse;
}

const initialState: TlawState = {
  getLawsResponse: undefined,
};

const lawSlice = createSlice({
  name: 'laws-slice',
  initialState,
  reducers: {
    resetLawSlice: () => initialState,
  },
  extraReducers: {
    [EGetLawsAction.GET_LAWS_SUCCESS]: (
      state: TlawState,
      action: TGetLawsSuccess
    ): TlawState => ({
      ...state,
      getLawsResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: lawReducer,
  actions: { resetLawSlice },
} = lawSlice;

export { lawReducer, resetLawSlice };
