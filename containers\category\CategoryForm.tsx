import { Input } from '../../components/Input';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { TCategoryFormContainerProps } from '@/containers';
import { Form, InputNumber, Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import validate from '@/utils/validate';
import { TCategory } from '@/commons/types';
import { Rule } from 'antd/lib/form';
import { useRouter } from 'next/router';
import { FormInstance } from 'antd/es/form/Form';
import { MODAL_MESSAGE, validationMessage } from '@/commons/messages-enum';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';

const CategoryFormContainer = ({
  intinitalValues,
  handleSubmit,
  isUpdate = false,
}: TCategoryFormContainerProps) => {
  const [isShowModalSubmit, setShowModalSubmit] = useState<boolean>(false);
  const { checkMaxLength, hasDot } = validate;
  const [form] = Form.useForm();
  const router = useRouter();
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const backToList = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/category');
  };

  const onSubmit = (form: FormInstance) => {
    const values: TCategory = {
      is_active: form.getFieldValue('is_active'),
      name: form.getFieldValue('name'),
      show_order: form.getFieldValue('show_order'),
    };
    handleSubmit(values);
  };

  const onOpenModalSubmit = () => {
    setShowModalSubmit(true);
  };

  const onCloseModalSubmit = () => {
    setShowModalSubmit(false);
  };

  useEffect(() => {
    form.setFieldsValue(intinitalValues);
  }, [form, intinitalValues]);

  return (
    <>
      <Form
        onFinish={onOpenModalSubmit}
        form={form}
        initialValues={{ is_active: 1 }}
      >
        <div className="bg-alice-blue-light py-4 px-5">
          {isUpdate && (
            <div className="text-base font-bold mb-6">
              大項目ID{' '}
              <span className="text-primary ml-1">{intinitalValues?.id}</span>
            </div>
          )}
          <div className="flex flex-row justify-between">
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                大項目名
              </div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('大項目名'),
                    },
                    () => checkMaxLength('大項目名', 255),
                  ] as Rule[]
                }
                name={'name'}
                className="!mb-0"
              >
                <Input classNames={'w-2/5 mt-2'} maxLength={255} />
              </Form.Item>
            </div>
            <span className="w-12"></span>
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">表示順</div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('表示順'),
                    },
                    checkMaxLength('表示順', 3),
                    hasDot('表示順', 3),
                  ] as Rule[]
                }
                name={'show_order'}
                className="!mb-0"
              >
                <InputNumber className={'!w-full'} min={0} stringMode />
              </Form.Item>
            </div>
          </div>
          <div className="mt-8">
            <div className="flex flex-row">
              <span className="text-textGray text-sm font-bold">
                有効・無効
              </span>
            </div>
            <div className="flex flex-row items-center mt-2">
              <Form.Item
                name="is_active"
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('有効・無効'),
                  },
                ]}
                className="!mb-0"
              >
                <Radio.Group>
                  <Radio className="font-bold" value={1}>
                    有効
                  </Radio>
                  <Radio className="font-bold" value={2}>
                    無効
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={backToList}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={isUpdate ? '更新' : '登録'}
          />
        </div>
      </Form>
      <ConfirmModal
        visible={isShowModalSubmit}
        content={
          isUpdate ? MODAL_MESSAGE.CONFIRM_EDIT : MODAL_MESSAGE.CONFIRM_CREATE
        }
        onClose={onCloseModalSubmit}
        onConfirm={() => onSubmit(form)}
      />
    </>
  );
};

export default CategoryFormContainer;
