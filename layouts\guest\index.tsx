import { ReactElement, useCallback, useEffect, useMemo, useState } from 'react';
import Header from './header-layout';
import Footer from './footer-layout';
import clsx from 'clsx';
import SiderBar from './sider-bar';
import { useDispatch, useSelector } from 'react-redux';
import { helpers } from '@/services/apis';
import { getMeAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import {
  adminRoutes,
  defaultRoutes,
  exceptionRoute,
  teacherRoutes,
} from '@/commons/routes';
import { ERoleId } from '@/commons/enums';
import { useRouter } from 'next/router';
import { useDebounce } from '@/utils/hooks';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const antIcon = <LoadingOutlined style={{ fontSize: 64 }} spin />;

const GuestLayout = ({ children }: { children: ReactElement }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { asPath } = router;
  const accessToken = helpers.getAccessToken();
  const [showSidebar, setShowSidebar] = useState<boolean>(true);
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);
  const loading = useSelector((state: RootState) => state.loading);
  const isLoading = useMemo(
    () =>
      Object.keys(loading)
        .map((key) => loading[key])
        .some((item) => !!item),
    [loading]
  );
  const debounceLoading = useDebounce(isLoading, 500);

  const routes = useMemo(() => {
    if (!me?.data) {
      return defaultRoutes;
    }
    if (me.data.role_id === ERoleId.TEACHER) {
      return teacherRoutes;
    }
    return adminRoutes;
  }, [me]);

  const checkRule = useCallback(
    (link: string) => {
      const path = asPath || '';
      if (path === link && link === '/') {
        return true;
      }
      if (link !== '/' && path.includes(link)) {
        return true;
      }
      return false;
    },
    [asPath]
  );

  useEffect(() => {
    if (accessToken) {
      dispatch(getMeAction.request({}));
    }
  }, [accessToken, dispatch]);

  useEffect(() => {
    if (!me?.data || exceptionRoute.includes(asPath)) {
      return;
    }
    const hasPermission = routes.reduce(
      (s, { link }) => checkRule(link) || s,
      false
    );
    if (!hasPermission) {
      router.push('/403');
    }
  }, [asPath, routes, me, checkRule, router]);

  const handleChangeSidebar = () => {
    setShowSidebar((current) => !current);
  };

  return (
    <div className="min-h-screen flex flex-col w-full -mb-[1px]">
      {debounceLoading && (
        <div className="fixed w-full h-full top-0 left-0 bg-gray-400 z-50 bg-opacity-30">
          <div className="w-full h-full flex items-center justify-center">
            <Spin indicator={antIcon} />
          </div>
        </div>
      )}
      <header className="w-full flex-none sticky top-0 left-0 z-20">
        <Header
          account={me}
          showSidebar={showSidebar}
          handleChangeSidebar={handleChangeSidebar}
        />
      </header>
      <div className="flex flex-grow">
        <div className={clsx('relative bg-primary', !showSidebar && 'hidden')}>
          <div className="sticky top-16 left-0 w-50 py-7 px-5 z-10 overflow-scroll scrollbar-hide">
            <SiderBar routes={routes} />
          </div>
        </div>
        <div className="flex flex-col flex-grow">
          <main className="flex-grow">{children}</main>
          <footer className="w-full flex-none">
            <Footer />
          </footer>
        </div>
      </div>
    </div>
  );
};

export default GuestLayout;
