import { TGetCategoriesResponse, getCategories } from '@/services/apis';
import { getCategoriesAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getCategoriesSaga(
  action: ActionType<typeof getCategoriesAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getCategories, materials);
    const getCategoriesResponse = response as TGetCategoriesResponse;
    yield put(getCategoriesAction.success(getCategoriesResponse));
    successCallback?.(getCategoriesResponse);
  } catch (err) {
    yield put(getCategoriesAction.failure(err));
    failedCallback?.(err);
  }
}
