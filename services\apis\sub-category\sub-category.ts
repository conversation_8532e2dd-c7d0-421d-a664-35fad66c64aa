
import { DataSubCategory } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetSubCategoryResponse = any;

export const createSubCategory = async (payload: DataSubCategory): Promise<TGetSubCategoryResponse> => {
    const response = await ApiService.post('/sub-categories', payload);
    return response.data;
};

export const getSubCategory = async (id: any): Promise<TGetSubCategoryResponse> => {
    const response = await ApiService.get(`/sub-categories/${id}`);
    return response.data;
};

export const updateSubCategory = async (id: any, payload: DataSubCategory): Promise<TGetSubCategoryResponse> => {
    const response = await ApiService.put(`/sub-categories/${id}`, payload);
    return response.data;
};

export const deleteSubCategory = async (id: any): Promise<TGetSubCategoryResponse> => {
    const response = await ApiService.delete(`/sub-categories/${id}`);
    return response.data;
};