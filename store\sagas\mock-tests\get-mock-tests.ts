import { TGetMockTestsResponse, getMockTests } from '@/services/apis';
import { getMockTestsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getMockTestsSaga(
  action: ActionType<typeof getMockTestsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getMockTests, materials);
    const getMockTestsResponse = response as TGetMockTestsResponse;
    yield put(getMockTestsAction.success(getMockTestsResponse));
    successCallback?.(getMockTestsResponse);
  } catch (err) {
    yield put(getMockTestsAction.failure(err));
    failedCallback?.(err);
  }
}
