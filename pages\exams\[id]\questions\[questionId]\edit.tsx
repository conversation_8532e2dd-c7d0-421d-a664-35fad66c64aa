import {
  EIsActive,
  ENeedCheckWithSetting,
  EQuestionForm,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { TOptionSub, TSubQuestion } from '@/commons/types';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { QuestionForm } from '@/containers';
import {
  deleteQuestionAction,
  getExamAction,
  getQuestionAction,
  putQuestionAction,
} from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { resetQuestionsSlice } from '@/store/slices/questions';
import { showNotification } from '@/utils/functions';
import { Form } from 'antd';
import { useRouter } from 'next/router';
import { Fragment, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const EditExamQuestion = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [form] = Form.useForm<any>();
  const { id, questionId } = router.query;
  const [isVisibleDelete, setIsVisibleDelete] = useState<boolean>(false);
  const [isShowModalSubmit, setIsShowModalSubmit] = useState<boolean>(false);
  const [isShowModalSetting, setIsShowModalSetting] = useState<boolean>(false);
  const [invalidTab, setInvalidTab] = useState<{ value: string }>();
  const [isShowModalSettingDelete, setIsShowModalSettingDelete] =
    useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const { getExamResponse: exam } = useSelector(
    (store: RootState) => store.mockTest
  );
  const { getQuestionResponse: question } = useSelector(
    (store: RootState) => store.question
  );
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (me?.data.role_id === ERoleId.TEACHER) {
      router.push('/403');
    }
  }, [me?.data.role_id, router]);

  useEffect(() => {
    if (id && typeof id === 'string') {
      dispatch(getExamAction.request({ paths: { id } }));
    }
  }, [id, dispatch]);

  useEffect(() => {
    if (
      questionId &&
      typeof questionId === 'string' &&
      id &&
      typeof id === 'string'
    ) {
      const paths = { id: questionId, exam_id: id };
      dispatch(getQuestionAction.request({ paths }));
    }
  }, [questionId, dispatch, id]);

  useEffect(() => {
    if (!question || !question.data) {
      return;
    }
    const { data } = question;
    const {
      category,
      allocation_point,
      content,
      type,
      is_active,
      explanation,
      image,
      question_choices,
      question_explanation_images,
      question_year,
      show_order,
      title,
      laws,
      digital_texts,
      videos,
      question_no,
    } = data;
    const numQuestion = type === EQuestionForm.SINGLE ? 1 : 4;
    const numAns = type === EQuestionForm.SINGLE ? 10 : 10;
    const initChoices = Array(numQuestion).fill(undefined);
    const sub_questions = initChoices.map((_, idx) => {
      const items = question_choices.filter(
        ({ subquestion_no }) => subquestion_no === idx + 1
      );
      if (!items.length) {
        return Array(numAns).fill(undefined);
      }
      return Array(numAns)
        .fill(undefined)
        .map((_, idx) => {
          const item = items.find(({ choice_no }) => choice_no === idx + 1);
          if (item) {
            return {
              ...item,
              is_correct: item?.is_correct === EIsActive.ENABLE,
            };
          }
          return undefined;
        });
    });

    const explanation_images = Array(2)
      .fill(undefined)
      .map((_, idx) => question_explanation_images?.[idx]);

    const category_id = category.id;
    const law_ids = Array(8)
      .fill(undefined)
      .map((_, idx) => {
        const law = laws?.[idx];
        if (!law) {
          return undefined;
        }
        const { category, name, id } = law;
        return { name, id, category_id: category.id };
      });
    const video_ids = Array(4)
      .fill(undefined)
      .map((_, idx) => {
        const video = videos?.[idx];
        if (!video) {
          return undefined;
        }
        const { category, name, id } = video;
        return { name, id, category_id: category.id };
      });
    const digital_text_ids = Array(4)
      .fill(undefined)
      .map((_, idx) => {
        const digital_text = digital_texts?.[idx];
        if (!digital_text) {
          return undefined;
        }
        const { category, name, id } = digital_text;
        return { name, id, category_id: category.id };
      });

    form.setFieldsValue({
      category_id,
      allocation_point,
      content,
      type,
      is_active,
      explanation,
      image,
      sub_questions,
      explanation_images,
      question_year,
      show_order,
      title,
      law_ids,
      digital_text_ids,
      video_ids,
      sub_questions_edit: sub_questions,
      root_type: type,
      question_no,
    });
  }, [form, question]);

  const deleteExamQuestion = (
    need_check_with_setting: ENeedCheckWithSetting
  ) => {
    if (
      id &&
      typeof id === 'string' &&
      questionId &&
      typeof questionId === 'string'
    ) {
      const paths = { exam_id: id, questionId };
      const params = { need_check_with_setting };
      dispatch(
        deleteQuestionAction.request({ params, paths }, (response: any) => {
          const { is_pass_setting } = response;
          if (is_pass_setting === false) {
            setIsShowModalSettingDelete(true);
          } else {
            showNotification(
              ETypeNotification?.SUCCESS,
              EXCEPTION.ACTION_SUCCESS
            );
            setTimeout(() => {
              if (callBackUrl) {
                router.push(callBackUrl);
                return;
              }
              router.push(`/exams/${id}/questions`);
            });
          }
        })
      );
    }
  };

  const editExamQuestion = (values: any) => {
    if (
      id &&
      typeof id === 'string' &&
      questionId &&
      typeof questionId === 'string'
    ) {
      const paths = { exam_id: id, questionId };
      const {
        law_ids: lawOptions,
        digital_text_ids: digitalOptions,
        video_ids: videoOptions,
        explanation_images: explanationImages,
        image: imageContent = '',
        ...restValues
      } = values;
      const subQuestions = form.getFieldValue('sub_questions');
      const sub_questions = subQuestions?.reduce(
        (
          s: Array<Array<TSubQuestion>>,
          questions: Array<TSubQuestion>,
          mIdx: number
        ) => {
          if (
            !questions ||
            questions.every((question) => !question || !question.content)
          ) {
            return s;
          }
          const subQ = questions.reduce((s: Array<TSubQuestion>, item, idx) => {
            if (!item || !item.content) {
              return s;
            }
            const rsI = {
              subquestion_no: mIdx + 1,
              choice_no: idx + 1,
              is_correct: item?.is_correct
                ? EIsActive.ENABLE
                : EIsActive.DISABLE,
              content: item.content,
            };
            return [...s, rsI];
          }, []);
          return [...s, subQ];
        },
        []
      );
      const law_ids = lawOptions
        ?.filter((law: TOptionSub) => law)
        .map(({ id }: TOptionSub) => id);
      const digital_text_ids = digitalOptions
        ?.filter((digital: TOptionSub) => digital)
        .map(({ id }: TOptionSub) => id);
      const video_ids = videoOptions
        ?.filter((digital: TOptionSub) => digital)
        .map(({ id }: TOptionSub) => id);
      const explanation_images = explanationImages?.filter(
        (i: any) => i?.explanation_image
      );
      const payload = {
        ...restValues,
        image: imageContent,
        sub_questions,
        law_ids,
        digital_text_ids,
        video_ids,
        explanation_images,
      };
      dispatch(
        putQuestionAction.request({ payload, paths }, (response: any) => {
          const { is_pass_setting } = response;
          if (is_pass_setting === false) {
            setIsShowModalSetting(true);
          } else {
            showNotification(
              ETypeNotification?.SUCCESS,
              EXCEPTION.ACTION_SUCCESS
            );
            setTimeout(() => {
              handleClickToBack();
            });
          }
        })
      );
    }
  };

  const handleSubmit = (values: any) => {
    editExamQuestion({
      ...values,
      need_check_with_setting: ENeedCheckWithSetting.NEED,
    });
  };

  const handleClickToBack = () => {
    dispatch(resetQuestionsSlice());
    router.push(`/exams/${id}/questions/${questionId}/view`);
  };

  const onCloseModalDelete = () => {
    setIsVisibleDelete(false);
  };

  const handleDelete = () => {
    deleteExamQuestion(ENeedCheckWithSetting.NEED);
  };

  const showModalDelete = async () => {
    setIsVisibleDelete(true);
  };

  const onCloseModalSubmit = () => {
    setIsShowModalSubmit(false);
  };

  const handleClickSubmit = () => {
    if (!form) {
      return;
    }
    form.submit();
  };

  const showModalSubmit = async () => {
    try {
      await form.validateFields();
      const subQuestions = form.getFieldValue('sub_questions');
      if (!subQuestions) {
        setInvalidTab({ value: 'part1' });
        return;
      }
      setIsShowModalSubmit(true);
    } catch (error) {
      try {
        await form.validateFields();
        const subQuestions = form.getFieldValue('sub_questions');
        if (!subQuestions) {
          setInvalidTab({ value: 'part1' });
          return;
        }
        setIsShowModalSubmit(true);
      } catch ({ values, errorFields }: any) {
        const firstErrorTab = (errorFields as any)?.find(({ name }: any) => {
          return name.indexOf('sub_questions') !== -1;
        });
        if (firstErrorTab) {
          const { name, errors } = firstErrorTab;
          if (errors.includes('設問の項目数は、2個以上にしてください。')) {
            const { sub_questions } = values as any;
            const firstEmpty = sub_questions.findIndex(
              (questions: Array<any>) =>
                !questions ||
                questions?.every((question) => !question || !question.content)
            );
            if (firstEmpty !== -1) {
              setInvalidTab({ value: `part${firstEmpty + 1}` });
            } else {
              setInvalidTab({ value: `part${sub_questions.length + 1}` });
            }
            return;
          }
          setInvalidTab({ value: `part${name[1] + 1}` });
        }
      }
    }
  };

  const handleClickSubmitWithCheck = async () => {
    try {
      await form.validateFields();
      const values = form.getFieldsValue();
      editExamQuestion({
        ...values,
        need_check_with_setting: ENeedCheckWithSetting.DONT_NEED,
      });
    } catch (error) {}
  };

  const handleClickDeleteWithCheck = () => {
    deleteExamQuestion(ENeedCheckWithSetting.DONT_NEED);
  };
  const onCloseModalSetting = () => {
    setIsShowModalSetting(false);
    setIsShowModalSettingDelete(false);
  };

  return (
    <Fragment>
      <ConfirmModal
        visible={isShowModalSetting}
        content={
          <div className="max-w-lg">{MODAL_MESSAGE.CONFIRM_SETTING_EXAM}</div>
        }
        onClose={onCloseModalSetting}
        onConfirm={handleClickSubmitWithCheck}
      />
      <ConfirmModal
        visible={isShowModalSettingDelete}
        content={
          <div className="max-w-lg">{MODAL_MESSAGE.CONFIRM_SETTING_EXAM}</div>
        }
        onClose={onCloseModalSetting}
        onConfirm={handleClickDeleteWithCheck}
      />
      <ConfirmModal
        visible={isShowModalSubmit}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
        onClose={onCloseModalSubmit}
        onConfirm={handleClickSubmit}
      />
      <ConfirmModal
        visible={isVisibleDelete}
        onClose={onCloseModalDelete}
        content={MODAL_MESSAGE?.CONFIRM_DELETE}
        onConfirm={handleDelete}
      />
      <div className="mx-12 my-4 grid grid-cols-1 gap-5">
        <div className="flex justify-between items-center">
          <div className="text-lg font-bold text-black-kj flex break-all">
            <span>{`${exam?.data.name}_問題編集`}</span>
          </div>
        </div>
        <div className="w-full px-5 pt-4 bg-new-white">
          <h2 className="text-black-kj font-bold !mb-5">
            問題ID:
            <span className="mx-3 text-primary text-base">
              {question?.data.id}
            </span>
          </h2>
          <QuestionForm
            formInstance={form}
            handleSubmit={handleSubmit}
            invalidTab={invalidTab}
          />
        </div>
        <div className="w-full flex items-center justify-end gap-5">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="戻る"
            className="!font-normal"
            onClick={handleClickToBack}
          />
          <Button
            styleType={EButtonStyleType.DANGER}
            title="削除"
            className="!font-normal"
            onClick={showModalDelete}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="更新"
            className="!font-normal"
            onClick={showModalSubmit}
          />
        </div>
      </div>
    </Fragment>
  );
};

export default EditExamQuestion;
