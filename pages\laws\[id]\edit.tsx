import { Input } from '../../../components/Input';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Form, InputNumber, Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { EIsActive, ERoleId, ETypeNotification } from '@/commons/enums';
import {
  deleteLaws,
  getDetailLaws,
  helpers,
  updateLaws,
} from '@/services/apis';
import { useRouter } from 'next/router';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { DataLaws, TLaws } from '@/commons/types';
import { SelectBigCategory } from '@/containers/select';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';
import { GetServerSideProps } from 'next';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import clsx from 'clsx';
import NotificationErrors from '@/components/NotificationErrors';
import { PreviewLawsModalEditUpdate } from '@/containers';

const EditDecree = () => {
  const EditorBoxForm = dynamic(
    () => import('@/components/EditorBox/EditorBoxForm'),
    { ssr: false }
  );
  const {
    checkEmptyString,
    checkMaxLengthEditor,
    checkMaxLength,
    hasWhiteSpace,
  } = validate;
  const [data, setData] = useState<TLaws | null>(null);
  const [isVisible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const router = useRouter();
  const { id } = router.query;
  const [isLoading, setIsLoading] = useState(true);
  const [isVisibleEdit, setVisibleEdit] = useState<boolean>(false);
  const [isShowPreviewLawsModal, setIsShowPreviewLawsModal] =
    useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const data = await getDetailLaws(id);
      const paramData = data?.data;
      if (paramData) {
        setData(paramData);
        form.setFieldsValue({
          is_active: paramData?.is_active,
          content: paramData?.content,
          name: paramData?.name,
          category_id: paramData?.category?.id,
          text_id: paramData?.text_id,
        });
        setIsLoading(false);
      }
    } catch (error: any) {
      setIsLoading(false);
      showNotification(ETypeNotification?.ERROR, error?.data?.message);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-2">
        <Form.Item className={'!mb-0'} name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onBack = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/laws');
  };

  const onSubmit = async () => {
    try {
      const values = form.getFieldsValue();
      const payload = {
        content: values?.content,
        is_active: values?.is_active,
        category_id: values?.category_id,
        name: values?.name,
        text_id: values?.text_id,
      };
      const dataResponse = await updateLaws(id, payload);
      if (dataResponse) {
        onBack();
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        <NotificationErrors error={error} />
      );
    }
  };

  const onCloseModal = () => {
    setVisible(false);
  };

  const onOpenModal = () => {
    setVisible(true);
  };

  const onDelete = async () => {
    try {
      const data = await deleteLaws(id);
      showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      onBack();
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onCloseModalEdit = () => {
    setVisibleEdit(false);
  };

  const onOpenModalEdit = () => {
    setVisibleEdit(true);
  };

  const onClosePreviewLawsModal = () => {
    setIsShowPreviewLawsModal(false);
  };

  const onOpenPreviewLawsModal = () => {
    setIsShowPreviewLawsModal(true);
  };

  return (
    <div className={clsx('mx-12 mt-4', isLoading && 'hidden')}>
      <span className="text-lg font-bold">法令編集</span>
      <PreviewLawsModalEditUpdate
        visiable={isShowPreviewLawsModal}
        handleCloseModal={onClosePreviewLawsModal}
        content={form.getFieldValue('content')}
        name={form.getFieldValue('name')}
      />
      <Form form={form} onFinish={onOpenModalEdit}>
        <div className="bg-alice-blue-light py-4 px-5 mt-3">
          <div className="flex justify-between">
            <div className="flex flex-row">
              <span className="text-base font-bold">法令ID</span>
              <span className="ml-3 text-primary text-base font-bold">
                {data?.id}
              </span>
            </div>
            <Button
              size={'small'}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title={'プレビュー'}
              onClick={onOpenPreviewLawsModal}
            />
          </div>

          <div className="grid grid-cols-2 justify-between mt-6 gap-5">
            <div>
              <span className="text-textGray text-sm font-bold">大項目</span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('大項目'),
                  },
                ]}
                className={'!mb-0'}
                name={'category_id'}
              >
                <SelectBigCategory
                  placeholder={'選択してください'}
                  size={'middle'}
                  className={'!mt-2'}
                />
              </Form.Item>
            </div>
            <div className={'w-full'}>
              <span className="text-textGray text-sm font-bold">法令名</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('法令名'),
                    },
                    () => checkMaxLength('タイトル', 255),
                  ] as Rule[]
                }
                className={'!mb-0'}
                name={'name'}
              >
                <Input maxLength={255} classNames={'!mt-2'} />
              </Form.Item>
            </div>
            <div>
              <div className="flex flex-row">
                <span className="text-textGray text-sm font-bold">
                  有効・無効
                </span>
              </div>
              {ratioRender()}
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">本文</span>
              <Form.Item
                rules={
                  [
                    checkEmptyString(false),
                    () => checkMaxLengthEditor('本文', 1, 65535),
                  ] as Rule[]
                }
                className={'!mb-0 !mt-2'}
                name={'content'}
              >
                <EditorBoxForm />
              </Form.Item>
            </div>
          </div>
        </div>
        <ConfirmModal
          visible={isVisibleEdit}
          onClose={onCloseModalEdit}
          content={MODAL_MESSAGE?.CONFIRM_EDIT}
          onConfirm={onSubmit}
        />
        <ConfirmModal
          visible={isVisible}
          onClose={onCloseModal}
          content={MODAL_MESSAGE.CONFIRM_DELETE}
          onConfirm={onDelete}
        />
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={onBack}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            onClick={onOpenModal}
            size={'small'}
            styleType={EButtonStyleType.DANGER}
            title={'削除'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
          />
        </div>
      </Form>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default EditDecree;
