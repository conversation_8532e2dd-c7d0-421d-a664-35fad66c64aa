import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function PreviewChildIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="16.333"
      viewBox="1225 485 14 16.333"
      {...props}
    >
      <g data-name="Preview">
        <g data-name="Group 263">
          <path
            d="m1238.825 488.675-3.5-3.5c-.117-.117-.175-.175-.408-.175h-9.334c-.35 0-.583.233-.583.583v15.167c0 .35.233.583.583.583h12.834c.35 0 .583-.233.583-.583v-11.667a.53.53 0 0 0-.175-.408Zm-1.808-.175h-1.517v-1.517l1.517 1.517Zm-10.85 11.667v-14h8.166v2.916c0 .35.234.584.584.584h2.916v10.5h-11.666Z"
            fill="#3e87bf"
            fillRule="evenodd"
            data-name="Path 566"
          />
          <path
            d="M1233.867 490.658c-1.342-1.341-3.559-1.341-4.959 0-1.4 1.342-1.341 3.559 0 4.959a3.465 3.465 0 0 0 4.492.35l1.75 1.75a.564.564 0 0 0 .817 0 .564.564 0 0 0 0-.817l-1.75-1.75a3.465 3.465 0 0 0-.35-4.492Zm-.875 4.142a2.324 2.324 0 0 1-3.325 0 2.324 2.324 0 0 1 0-3.325 2.324 2.324 0 0 1 3.325 0 2.324 2.324 0 0 1 0 3.325Z"
            fill="#3e87bf"
            fillRule="evenodd"
            data-name="Path 567"
          />
        </g>
      </g>
    </svg>
  );
}
