import { Form } from 'antd';
import {
  TPostPayloadTermSettingsMaterials,
  getTermSettings,
  getTermSettingsTrial,
  helpers,
  postTermSettings,
  postTermSettingsTrial,
} from '@/services/apis';
import { useEffect, useState } from 'react';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { EPlacement, ERoleId, ETypeNotification } from '@/commons/enums';
import { SettingsForm } from '@/components/SettingsForm';
import { EXCEPTION } from '@/commons/messages-enum';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import { Select } from '@/components/Select';

const termOptions = [
  {
    value: '1',
    title: '無料 ',
  },
  {
    value: '2',
    title: '有料',
  },
];

const { Option } = Select;

const TermSetting = () => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(true);
  const [isFirstTime, setIsFirstTime] = useState(false);
  const [selectTerm, setSelectTerm] = useState<string>(termOptions[1].value);
  const router = useRouter();

  const onSubmit = async (values: any) => {
    try {
      const payload: TPostPayloadTermSettingsMaterials = {
        description: values.description as string,
        is_public: values.is_public as number,
      };
      if (selectTerm === '2') {
        await postTermSettings(payload);
      } else {
        await postTermSettingsTrial(payload);
      }

      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      router.push('/');
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  const onFetchGuidanceSettings = async () => {
    try {
      setIsLoading(true);
      const data = await getTermSettings();
      form.setFieldsValue({
        is_public: data.data.is_public,
        description: data.data.description,
      });

      if (data.data.is_public === 2 && data.data.description === '') {
        setIsFirstTime(true);
      }
    } catch (error: any) {
      if (error?.request.status !== 503) {
        showNotification(
          ETypeNotification.ERROR,
          error?.data?.message
            ? error?.data?.message
            : EXCEPTION.ACTION_FAILURE,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
    setIsLoading(false);
  };

  const onFetchGuidanceSettingsTrial = async () => {
    try {
      setIsLoading(true);
      const data = await getTermSettingsTrial();
      form.setFieldsValue({
        is_public: data.data.is_public,
        description: data.data.description,
      });

      if (data.data.is_public === 2 && data.data.description === '') {
        setIsFirstTime(true);
      }
    } catch (error: any) {
      if (error?.request.status !== 503) {
        showNotification(
          ETypeNotification.ERROR,
          error?.data?.message
            ? error?.data?.message
            : EXCEPTION.ACTION_FAILURE,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (selectTerm === '2') {
      onFetchGuidanceSettings();
    } else {
      onFetchGuidanceSettingsTrial();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectTerm]);

  const handleChangeTerm = (term: string) => {
    setSelectTerm(term);
  };

  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">利用規約管理</div>
      <div className="bg-alice-blue-light py-4 px-5">
        <div className="flex items-center gap-4">
          <span className="text-textGray text-sm font-bold">
            利用タイプ選択
          </span>
          <Select
            className="w-40 rounded"
            size="large"
            placeholder="利用タイプ選択"
            onChange={handleChangeTerm}
            value={selectTerm}
          >
            {termOptions.map(({ value, title }) => (
              <Option value={value} key={value}>
                {title}
              </Option>
            ))}
          </Select>
        </div>
      </div>
      {!isLoading && (
        <SettingsForm
          onSubmit={onSubmit}
          form={form}
          initialValues={{
            is_public: 2,
          }}
          isFirstTime={isFirstTime}
          fieldMessage={'利用規約'}
        />
      )}
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default TermSetting;
