import { EIsActive } from '@/commons/enums';
import { TOptionCategory } from '@/commons/types';
import { Select } from '@/components/Select';
import { getCategory, getSelectCategories } from '@/services/apis';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { TFilterSelectCategory, TSelectBigCategoryProps } from './select.types';

const { Option } = Select;

const SelectBigCategory = ({
  value,
  onChange,
  size = 'large',
  className = 'w-full rounded input-disabled',
  placeholder = '選択してください',
  disabled,
}: TSelectBigCategoryProps) => {
  const [filterSelect, setFilterSelect] = useState<TFilterSelectCategory>({
    page: 1,
    name: '',
  });
  const [options, setOptions] = useState<Array<TOptionCategory>>([]);
  const [lastPage, setLastPage] = useState<number>(1);
  // const [lable, setLable] = useState<TOptionCategory>();

  const fetchData = async (page: number = 1, name?: string) => {
    try {
      const params = {
        page,
        name: name ? name : undefined,
        is_actives: [EIsActive.ENABLE, EIsActive.DISABLE],
      };
      const { data, meta } = await getSelectCategories({ params });
      setLastPage(meta.last_page);
      if (meta.current_page === 1) {
        setOptions(data);
      } else {
        setOptions((current) => [...current, ...data]);
      }
    } catch (error) {
      setOptions([]);
    }
  };

  useEffect(() => {
    fetchData(filterSelect?.page, filterSelect?.name);
  }, [filterSelect?.name, filterSelect?.page]);

  const setKeyword = (name: string) => {
    if (name !== filterSelect.name) {
      setFilterSelect({ page: 1, name });
    }
  };

  const loadMore = useCallback(() => {
    if (
      !filterSelect?.page ||
      (filterSelect?.page && filterSelect?.page >= lastPage)
    ) {
      return null;
    }
    setFilterSelect((current) => ({
      ...current,
      page: (current?.page || 0) + 1,
    }));
  }, [filterSelect?.page, lastPage]);

  useEffect(() => {
    const check = options.find(({ id }) => `${value}` === `${id}`);
    if (!check && value) {
      loadMore();
    }
  }, [loadMore, options, value]);

  const handleOnChange = (value: string) => {
    onChange?.(value);
  };

  return (
    <Select
      showSearch={true}
      onSearch={setKeyword}
      onLoadMore={loadMore}
      onChange={handleOnChange}
      size={size}
      disabled={disabled}
      className={className}
      value={value}
      placeholder={placeholder}
    >
      {options.map((option) => (
        <Option key={option.id} value={option.id}>
          {`(ID:${option.id}) ${option.name}`}
        </Option>
      ))}
    </Select>
  );
};

export default SelectBigCategory;
