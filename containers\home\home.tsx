import React, { Fragment, useEffect, useState } from 'react';
import { Collapse } from 'antd';
import { ChatIcon, CollapseIcon, MessageIcon } from '@/components';
import clsx from 'clsx';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import { getHomeAction, getMyNotificationsAction } from '@/store/actions';
import { TNotification } from '@/commons/types';
import moment from 'moment';
import { EFormatDateTime, ERoleId } from '@/commons/enums';
import { TGetHomeResponse } from '@/services/apis';
import Link from 'next/link';

const { Panel } = Collapse;

const mockData = {
  message: {
    number: 150,
    text: '講師の未回答数',
    type: 1,
  },
  notification: {
    number: 150,
    text: '管理者の未回答数',
    type: 2,
  },
  titleHeader: '2022/10/25 10:00',
  underLine: 'メンテナンスのお知らせ',
  messageNoti:
    '11月5日10：00～13：00（予定）にシステムメンテナンスを行います。上記時間帯はKojiro＜運管＞はご利用いただけませんのでご注意ください今回は、2020年度1回目の模擬試験の問題が追加されます。3月の試験に向けて、追加される問題も引き続きご活用ください。',
};

const HomeContainer = () => {
  const dispatch = useDispatch();
  const [home, setHome] = useState<TGetHomeResponse>();
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);
  const { getMyNotificationResponse: myNotifications } = useSelector(
    (state: RootState) => state.myNotification
  );

  const { data = [] } = myNotifications || {};

  useEffect(() => {
    dispatch(getMyNotificationsAction.request({}));
    dispatch(
      getHomeAction.request({}, (home) => {
        setHome(home);
      })
    );
  }, [dispatch]);

  const inforComponent = (item: any) => {
    const { number, text, type } = item;
    return (
      <div className="py-5 bg-alice-blue-light pl-5 flex flex-row ">
        <div className="w-11 h-11 bg-primary rounded-full flex items-center justify-center">
          {type === 1 ? <ChatIcon /> : <MessageIcon />}
        </div>
        <div className="h-12 flex flex-col ml-5">
          <span className="text-green-blue text-xl font-bold">
            {type === 1
              ? home?.not_answer_question_by_teacher
              : home?.not_answer_question_by_admin}
          </span>
          <Link
            href={
              type === 1
                ? '/knowledge-boards?is_publics=1&is_publics=2&types=3&types=1&types=2&order_by_last_updated_at=desc&teacher_reply=1'
                : '/knowledge-boards?is_publics=1&is_publics=2&types=3&types=1&types=2&order_by_last_updated_at=desc&admin_reply=1'
            }
          >
            <span className="text-textGray text-lg font-bold">{text}</span>
          </Link>
        </div>
      </div>
    );
  };
  const header = (notification: TNotification) => {
    return (
      <div>
        <span className="text-textGray text-sm font-bold">
          {moment(notification.start_time).format(EFormatDateTime.YYYY_MM_DD)}
        </span>
        <span className="text-dark-cerulean text-sm underline decoration-solid ml-3 font-bold">
          {notification?.title}
        </span>
      </div>
    );
  };

  return (
    <div className="px-10 mt-10">
      {home && (
        <div className="w-full">
          <p className="text-dark-shade-of-gray text-lg font-bold">知識板</p>
          <div className="grid grid-cols-2 justify-between gap-10 mt-2">
            {inforComponent(mockData?.message)}
            {me?.data.role_id !== ERoleId.TEACHER &&
              inforComponent(mockData?.notification)}
          </div>
        </div>
      )}
      <div className="w-full mt-14">
        <p className="text-dark-shade-of-gray text-lg font-bold">お知らせ</p>
        <div className="grid grid-cols-1 gap-2 mb-5">
          {data.map((notification) => (
            <Collapse
              key={notification.id}
              className={clsx('CustomCollapse')}
              expandIcon={({ isActive }) => (
                <div className="icon">
                  <div className={clsx(isActive && 'rotate-180')}>
                    <CollapseIcon />
                  </div>
                </div>
              )}
              defaultActiveKey={['2']}
              expandIconPosition={'end'}
            >
              <Panel header={header(notification)} key="1">
                <div className="flex flex-col html-render">
                  <div
                    dangerouslySetInnerHTML={{ __html: notification.content }}
                  />
                </div>
              </Panel>
            </Collapse>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HomeContainer;
