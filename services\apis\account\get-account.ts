// import TAccount from 'common/types/Account.types';

import ApiService from '@/services/apis';

// TYPES

export type TGetAccountPaths = {};
export type TGetAccountParams = unknown;

export type TGetAccountMaterials = {
  // paths?: TGetAccountPaths;
  // params?: TGetAccountParams;
};

export type TGetAccountResponse = any;

export const getAccount = async (
  materials: TGetAccountMaterials
): Promise<TGetAccountResponse> => {
  const response = await ApiService.get('/api/Account/info');
  return response.data;
};
