import { TGetVideosResponse, getVideos } from '@/services/apis';
import { getVideosAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getVideosSaga(
  action: ActionType<typeof getVideosAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getVideos, materials);
    const getVideosResponse = response as TGetVideosResponse;
    yield put(getVideosAction.success(getVideosResponse));
    successCallback?.(getVideosResponse);
  } catch (err) {
    yield put(getVideosAction.failure(err));
    failedCallback?.(err);
  }
}
