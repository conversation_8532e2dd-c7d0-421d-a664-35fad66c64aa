import {
  TGetDetailExerciseMaterials,
  TGetDetailExerciseResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetDetailExerciseAction {
  GET_EXERCISE_DETAIL = 'GET_EXERCISE_DETAIL',
  GET_EXERCISE_DETAIL_REQUEST = 'GET_EXERCISE_DETAIL_REQUEST',
  GET_EXERCISE_DETAIL_SUCCESS = 'GET_EXERCISE_DETAIL_SUCCESS',
  GET_EXERCISE_DETAIL_FAILED = 'GET_EXERCISE_DETAIL_FAILED',
}

// TYPES

export type TGetDetailExerciseRequest = {
  type: EGetDetailExerciseAction.GET_EXERCISE_DETAIL_REQUEST;
  payload: {
    materials: TGetDetailExerciseMaterials;
    successCallback?: (response: TGetDetailExerciseResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetDetailExerciseSuccess = {
  type: EGetDetailExerciseAction.GET_EXERCISE_DETAIL_SUCCESS;
  payload: { response?: TGetDetailExerciseResponse };
};

export type TGetDetailExerciseFailed = {
  type: EGetDetailExerciseAction.GET_EXERCISE_DETAIL_FAILED;
};

export const getDetailExerciseAction = {
  request: createActionCreator(
    EGetDetailExerciseAction.GET_EXERCISE_DETAIL_REQUEST,
    (resolve) =>
      (
        materials: TGetDetailExerciseMaterials,
        successCallback?: (response: TGetDetailExerciseResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetDetailExerciseRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetDetailExerciseAction.GET_EXERCISE_DETAIL_SUCCESS,
    (resolve) =>
      (response?: TGetDetailExerciseResponse): TGetDetailExerciseSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetDetailExerciseAction.GET_EXERCISE_DETAIL_FAILED,
    (resolve) =>
      (error: unknown): TGetDetailExerciseFailed =>
        resolve({ error })
  ),
};
