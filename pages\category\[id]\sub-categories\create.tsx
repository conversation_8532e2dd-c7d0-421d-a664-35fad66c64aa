import { Input } from '../../../../components/Input';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Form, InputNumber, Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import {
  TGetCategoryProps,
  createSubCategory,
  getCategory,
  helpers,
} from '@/services/apis';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { EIsActive, ERoleId, ETypeNotification } from '@/commons/enums';
import { useRouter } from 'next/router';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';
import { TCategory } from '@/commons/types';
import { GetServerSideProps } from 'next';
import { useHistory } from '@/utils/contexts';

const SmallCatagory = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { id } = router.query;
  const [isVisible, setVisible] = useState<boolean>(false);
  const { hasDot, checkMaxLength } = validate;
  const [category, setCategory] = useState<TCategory>();
  const historyManager = useHistory();
  const canGoBack = historyManager.canGoBack();

  useEffect(() => {
    fetchCategory();
    form.setFieldsValue({
      is_active: EIsActive.ENABLE,
    });
  }, []);

  const fetchCategory = async () => {
    const payload: TGetCategoryProps = {
      id: id as string,
    };
    try {
      const data = await getCategory(payload);
      setCategory(data);
    } catch (error: any) {
      showNotification(ETypeNotification.ERROR, error?.data?.message);
    }
  };

  const onBack = () => {
    if (canGoBack) {
      router.back();
      return;
    }
    router?.push(`/category/${id}/sub-categories`);
  };

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-3">
        <Form.Item className={'!mb-0'} name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onSubmit = async () => {
    try {
      const values = form.getFieldsValue();
      const payload = {
        category_id: id,
        name: values?.name,
        is_active: values?.is_active,
        show_order: values?.show_order,
      };
      const data = await createSubCategory(payload);
      if (data) {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        onBack();
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onCloseModal = () => {
    setVisible(false);
  };

  const onOpenModal = () => {
    setVisible(true);
  };

  return (
    <div className="mx-12 mt-4">
      <span className="text-lg font-bold">小項目規新登録</span>
      <Form form={form} onFinish={onOpenModal}>
        <div className="bg-alice-blue-light py-4 px-5 mt-3">
          <div>
            <div className="flex flex-row">
              <span className="text-base font-bold">大項目名</span>
              <span className="ml-3 text-primary text-base">
                {category?.name}
              </span>
            </div>
          </div>
          <div className="mt-6 grid grid-cols-2 gap-5">
            <div>
              <span className="text-textGray text-sm font-bold">小項目名</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('小項目名'),
                    },
                    () => checkMaxLength('小項目名', 255),
                  ] as Rule[]
                }
                name={'name'}
              >
                <Input classNames={'!mt-2'} />
              </Form.Item>
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">表示順</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('表示順'),
                    },
                    checkMaxLength('表示順', 3),
                    hasDot('表示順', 3),
                  ] as Rule[]
                }
                name={'show_order'}
              >
                <InputNumber className={'!w-full !mt-2'} min={0} stringMode />
              </Form.Item>
            </div>
          </div>
          <div className="mt-8">
            <div className="flex flex-row">
              <span className="text-textGray text-sm font-bold">
                有効・無効
              </span>
            </div>
            {ratioRender()}
          </div>
        </div>
        <ConfirmModal
          visible={isVisible}
          onClose={onCloseModal}
          content={MODAL_MESSAGE?.CONFIRM_CREATE}
          onConfirm={onSubmit}
        />
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={onBack}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'登録'}
          />
        </div>
      </Form>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default SmallCatagory;
