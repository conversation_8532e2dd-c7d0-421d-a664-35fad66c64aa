import { TAdministrator, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetAdminsParams = {
  page: number;
  username_kana?: string;
  role_id?: number;
  is_actives: Array<number>;
};

export type TGetAdminsMaterials = {
  params?: TGetAdminsParams;
};

export type TGetAdminsResponse = TListData<TAdministrator>;

export const getAdmins = async (
  materials: TGetAdminsMaterials
): Promise<TGetAdminsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/admins', { params });
  return response.data;
};
