import { Fragment } from 'react';
import { TQuestionSigleChoiceProps } from './questions.types';
import QuestionChoice from './QuestionChoice';

const QuestionSigleChoice = ({
  choices,
  choiceCorrect,
  choiceSelected,
  handleChoice,
  disabled,
}: TQuestionSigleChoiceProps) => {
  return (
    <Fragment>
      {choices?.map((choice, idx) => {
        const onClick = () => {
          handleChoice?.(choice.id);
        };
        return (
          <QuestionChoice
            key={choice.id || idx}
            choice={choice}
            disabled={disabled}
            onClick={onClick}
            isCorrect={choiceCorrect === choice.id}
            isFail={
              !!choiceCorrect &&
              choice.id === choiceSelected &&
              choiceCorrect !== choice.id
            }
            isSelected={choice.id === choiceSelected}
          />
        );
      })}
    </Fragment>
  );
};

export default QuestionSigleChoice;
