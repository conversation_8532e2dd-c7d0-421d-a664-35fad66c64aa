import {
  TGetPracticeQuestionResponse,
  getPracticeQuestion,
} from '@/services/apis';
import { getPracticeQuestionAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getPracticeQuestionSaga(
  action: ActionType<typeof getPracticeQuestionAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getPracticeQuestion, materials);
    const getPracticeQuestionResponse =
      response as TGetPracticeQuestionResponse;
    yield put(getPracticeQuestionAction.success(getPracticeQuestionResponse));
    successCallback?.(getPracticeQuestionResponse);
  } catch (err) {
    yield put(getPracticeQuestionAction.failure(err));
    failedCallback?.(err);
  }
}
