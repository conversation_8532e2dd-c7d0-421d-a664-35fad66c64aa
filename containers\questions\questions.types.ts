import { EQuestionForm } from '@/commons/enums';
import {
  TDigital,
  TExerciseResponse,
  TLaw,
  TQuestion,
  TSubQuestion,
  TVideo,
} from '@/commons/types';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

export type TQuestionsFilter = {
  category_id?: string;
  show_order?: number;
  question_year?: string;
  id?: number;
  title?: string;
  content?: string;
  is_actives?: Array<CheckboxValueType>;
  types?: Array<CheckboxValueType>;
};

export type TFilterQuestionsProps = {
  defaultFilter?: TQuestionsFilter;
  setFilter?: (value?: TQuestionsFilter) => void;
  clearFilter?: () => void;
};

export type TQuestionScreenProps = {
  question?: TExerciseResponse | TQuestion;
  disabled?: boolean;
  choiceSelected?: number | Array<number>;
  choiceCorrect?: number | Array<number>;
  handleChoice?: (choice_id?: number) => void;
  isExamQuestion?: boolean;
};

export type TQuestionChoiceProps = {
  choice: TSubQuestion;
  disabled?: boolean;
  onClick?: () => void;
  isCorrect?: boolean;
  isFail?: boolean;
  isSelected?: boolean;
  isCorrectNotChoice?: boolean;
};

export type TMultipleChoiceProps = {
  titleNums: string;
  choiceSelected?: number | Array<number>;
  disabled?: boolean;
  choices: Array<TSubQuestion>;
  choiceCorrect?: number | Array<number>;
  showTitle: boolean;
  handleChoice?: (choice_id?: number) => void;
};

export type TQuestionSigleChoiceProps = {
  type?: EQuestionForm;
  disabled?: boolean;
  choices: Array<TSubQuestion>;
  choiceSelected?: number | Array<number>;
  choiceCorrect?: number | Array<number>;
  handleChoice?: (choice_id?: number) => void;
};

export type TQuestionModalProps = {
  showModal: boolean;
  quesitonContent?: TExerciseResponse | TQuestion;
  handleCloseModal: () => void;
};

export type TQuestionExplanationProps = {
  question?: TExerciseResponse | TQuestion;
};

export type TLawExplanationProps = {
  law: TLaw;
};

export type TDigitalTextExplanationProps = {
  digital_texts: Array<TDigital>;
};

export type TVideoExplanationProps = {
  videos: Array<TVideo>;
};
