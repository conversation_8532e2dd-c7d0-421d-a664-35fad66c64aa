import {
  TGetKnowledgeBoardsMaterials,
  TGetKnowledgeBoardsResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetKnowledgeBoardsAction {
  GET_KNOWLEDGE_BOARDS = 'GET_KNOWLEDGE_BOARDS',
  GET_KNOWLEDGE_BOARDS_REQUEST = 'GET_KNOWLEDGE_BOARDS_REQUEST',
  GET_KNOWLEDGE_BOARDS_SUCCESS = 'GET_KNOWLEDGE_BOARDS_SUCCESS',
  GET_KNOWLEDGE_BOARDS_FAILED = 'GET_KNOWLEDGE_BOARDS_FAILED',
}

// TYPES

export type TGetKnowledgeBoardsRequest = {
  type: EGetKnowledgeBoardsAction.GET_KNOWLEDGE_BOARDS_REQUEST;
  payload: {
    materials: TGetKnowledgeBoardsMaterials;
    successCallback?: (response: TGetKnowledgeBoardsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetKnowledgeBoardsSuccess = {
  type: EGetKnowledgeBoardsAction.GET_KNOWLEDGE_BOARDS_SUCCESS;
  payload: { response?: TGetKnowledgeBoardsResponse };
};

export type TGetKnowledgeBoardsFailed = {
  type: EGetKnowledgeBoardsAction.GET_KNOWLEDGE_BOARDS_FAILED;
};

export const getKnowledgeBoardsAction = {
  request: createActionCreator(
    EGetKnowledgeBoardsAction.GET_KNOWLEDGE_BOARDS_REQUEST,
    (resolve) =>
      (
        materials: TGetKnowledgeBoardsMaterials,
        successCallback?: (response: TGetKnowledgeBoardsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetKnowledgeBoardsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetKnowledgeBoardsAction.GET_KNOWLEDGE_BOARDS_SUCCESS,
    (resolve) =>
      (response?: TGetKnowledgeBoardsResponse): TGetKnowledgeBoardsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetKnowledgeBoardsAction.GET_KNOWLEDGE_BOARDS_FAILED,
    (resolve) =>
      (error: unknown): TGetKnowledgeBoardsFailed =>
        resolve({ error })
  ),
};
