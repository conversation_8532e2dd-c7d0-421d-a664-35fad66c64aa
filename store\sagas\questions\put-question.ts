import { TPutQuestionResponse, putQuestion } from '@/services/apis';
import { putQuestionAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* putQuestionSaga(
  action: ActionType<typeof putQuestionAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(putQuestion, materials);
    const putQuestionResponse = response as TPutQuestionResponse;
    yield put(putQuestionAction.success(putQuestionResponse));
    successCallback?.(putQuestionResponse);
  } catch (err) {
    yield put(putQuestionAction.failure(err));
    failedCallback?.(err);
  }
}
