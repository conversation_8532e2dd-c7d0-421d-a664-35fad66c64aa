import {
  deleteExamAction,
  getExamAction,
  getExamsAction,
  getMockTestsAction,
  postExamAction,
  putExamAction,
} from '@/store/actions';
import { all, takeLatest } from 'redux-saga/effects';
import { getExamsSaga } from './get-exams';
import { getMockTestsSaga } from './get-mock-tests';
import { postExamSaga } from './post-exam';
import { getExamSaga } from './get-exam';
import { putExamSaga } from './put-exam';
import { deleteExamSaga } from './delete-exam';

export default function* root(): Generator {
  yield all([
    takeLatest(getMockTestsAction.request.type, getMockTestsSaga),
    takeLatest(getExamsAction.request.type, getExamsSaga),
    takeLatest(postExamAction.request.type, postExamSaga),
    takeLatest(getExamAction.request.type, getExamSaga),
    takeLatest(putExamAction.request.type, putExamSaga),
    takeLatest(deleteExamAction.request.type, deleteExamSaga),
  ]);
}
