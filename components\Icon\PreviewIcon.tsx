import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function PreviewIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="11.731"
      viewBox="1227 515.769 16 11.731"
      {...props}
    >
      <g data-name="eye">
        <path
          d="M1242.951 521.41c-1.632-3.482-4.676-5.641-7.95-5.641-3.275 0-6.32 2.16-7.951 5.641a.533.533 0 0 0 0 .448c1.631 3.509 4.676 5.642 7.95 5.642 3.275 0 6.32-2.133 7.951-5.642a.533.533 0 0 0 0-.448Zm-7.95 5.023c-2.784 0-5.397-1.834-6.874-4.799 1.477-2.965 4.09-4.799 6.873-4.799 2.784 0 5.397 1.834 6.874 4.8-1.477 2.964-4.09 4.798-6.874 4.798Z"
          fill="#3e87bf"
          fillRule="evenodd"
          data-name="Path 564"
        />
        <path
          d="M1235 518.435a3.2 3.2 0 1 0 0 6.399 3.2 3.2 0 0 0 0-6.4Zm0 5.332a2.133 2.133 0 1 1 0-4.266 2.133 2.133 0 0 1 0 4.266Z"
          fill="#3e87bf"
          fillRule="evenodd"
          data-name="Path 565"
        />
      </g>
    </svg>
  );
}
