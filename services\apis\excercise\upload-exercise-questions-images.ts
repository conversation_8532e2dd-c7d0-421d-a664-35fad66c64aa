import { EIsCreating } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPostUploadExerciseQuestionsImagesPayload = {
  file_zip: File;
  is_creating: EIsCreating;
};

export type TPostUploadExerciseQuestionsImagesMaterials = {
  payload: TPostUploadExerciseQuestionsImagesPayload;
};

export type TPostUploadExerciseQuestionsImagesResponse = unknown;

export const postUploadExerciseQuestionsImages = async (
  materials: TPostUploadExerciseQuestionsImagesMaterials
): Promise<TPostUploadExerciseQuestionsImagesResponse> => {
  const { payload } = materials;
  const bodyFormData = new FormData();
  bodyFormData.append('file_zip', payload?.file_zip);
  bodyFormData.append('is_creating', `${payload?.is_creating}`);
  const response = await ApiService.post('/upload/zip-image/exercise-question-images', bodyFormData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
};
