import ApiService from '@/services/apis';

export type TPutUpdatePracticeQuestionPayload = {
  show_order: number;
};
export type TPutUpdatePracticeQuestionPath = {
  id: string;
};

export type TPutUpdatePracticeQuestionMaterials = {
  path: TPutUpdatePracticeQuestionPath;
  payload: TPutUpdatePracticeQuestionPayload;
};

export type TPutUpdatePracticeQuestionResponse = unknown;

export const putUpdatePracticeQuestion = async (
  materials: TPutUpdatePracticeQuestionMaterials
): Promise<TPutUpdatePracticeQuestionResponse> => {
  const { payload, path } = materials;
  const response = await ApiService.put(
    `/practice-questions/${path.id}`,
    payload
  );
  return response.data;
};
