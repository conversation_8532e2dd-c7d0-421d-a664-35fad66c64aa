import {
  TGetPracticeQuestionsMaterials,
  TGetPracticeQuestionsResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetPracticeQuestionsAction {
  GET_PRACTICE_QUESTIONS = 'GET_PRACTICE_QUESTIONS',
  GET_PRACTICE_QUESTIONS_REQUEST = 'GET_PRACTICE_QUESTIONS_REQUEST',
  GET_PRACTICE_QUESTIONS_SUCCESS = 'GET_PRACTICE_QUESTIONS_SUCCESS',
  GET_PRACTICE_QUESTIONS_FAILED = 'GET_PRACTICE_QUESTIONS_FAILED',
}

// TYPES

export type TGetPracticeQuestionsRequest = {
  type: EGetPracticeQuestionsAction.GET_PRACTICE_QUESTIONS_REQUEST;
  payload: {
    materials: TGetPracticeQuestionsMaterials;
    successCallback?: (response: TGetPracticeQuestionsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetPracticeQuestionsSuccess = {
  type: EGetPracticeQuestionsAction.GET_PRACTICE_QUESTIONS_SUCCESS;
  payload: { response?: TGetPracticeQuestionsResponse };
};

export type TGetPracticeQuestionsFailed = {
  type: EGetPracticeQuestionsAction.GET_PRACTICE_QUESTIONS_FAILED;
};

export const getPracticeQuestionsAction = {
  request: createActionCreator(
    EGetPracticeQuestionsAction.GET_PRACTICE_QUESTIONS_REQUEST,
    (resolve) =>
      (
        materials: TGetPracticeQuestionsMaterials,
        successCallback?: (response: TGetPracticeQuestionsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetPracticeQuestionsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetPracticeQuestionsAction.GET_PRACTICE_QUESTIONS_SUCCESS,
    (resolve) =>
      (
        response?: TGetPracticeQuestionsResponse
      ): TGetPracticeQuestionsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetPracticeQuestionsAction.GET_PRACTICE_QUESTIONS_FAILED,
    (resolve) =>
      (error: unknown): TGetPracticeQuestionsFailed =>
        resolve({ error })
  ),
};
