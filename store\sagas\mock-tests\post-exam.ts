import { TGetExamsResponse, postExam } from '@/services/apis';
import { postExamAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* postExamSaga(
  action: ActionType<typeof postExamAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(postExam, materials);
    const postExamResponse = response as TGetExamsResponse;
    yield put(postExamAction.success(postExamResponse));
    successCallback?.(postExamResponse);
  } catch (err) {
    yield put(postExamAction.failure(err));
    failedCallback?.(err);
  }
}
