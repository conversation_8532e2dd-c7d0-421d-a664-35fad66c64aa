import {
  TGetSelectSubCategoriesMaterials,
  TGetSelectSubCategoriesResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetSelectSubCategoriesAction {
  GET_SELECT_SUB_CATEGORIES = 'GET_SELECT_SUB_CATEGORIES',
  GET_SELECT_SUB_CATEGORIES_REQUEST = 'GET_SELECT_SUB_CATEGORIES_REQUEST',
  GET_SELECT_SUB_CATEGORIES_SUCCESS = 'GET_SELECT_SUB_CATEGORIES_SUCCESS',
  GET_SELECT_SUB_CATEGORIES_FAILED = 'GET_SELECT_SUB_CATEGORIES_FAILED',
}

// TYPES

export type TGetSelectSubCategoriesRequest = {
  type: EGetSelectSubCategoriesAction.GET_SELECT_SUB_CATEGORIES_REQUEST;
  payload: {
    materials: TGetSelectSubCategoriesMaterials;
    successCallback?: (response: TGetSelectSubCategoriesResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetSelectSubCategoriesSuccess = {
  type: EGetSelectSubCategoriesAction.GET_SELECT_SUB_CATEGORIES_SUCCESS;
  payload: { response?: TGetSelectSubCategoriesResponse };
};

export type TGetSelectSubCategoriesFailed = {
  type: EGetSelectSubCategoriesAction.GET_SELECT_SUB_CATEGORIES_FAILED;
};

export const getSelectSubCategoriesAction = {
  request: createActionCreator(
    EGetSelectSubCategoriesAction.GET_SELECT_SUB_CATEGORIES_REQUEST,
    (resolve) =>
      (
        materials: TGetSelectSubCategoriesMaterials,
        successCallback?: (response: TGetSelectSubCategoriesResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetSelectSubCategoriesRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetSelectSubCategoriesAction.GET_SELECT_SUB_CATEGORIES_SUCCESS,
    (resolve) =>
      (
        response?: TGetSelectSubCategoriesResponse
      ): TGetSelectSubCategoriesSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetSelectSubCategoriesAction.GET_SELECT_SUB_CATEGORIES_FAILED,
    (resolve) =>
      (error: unknown): TGetSelectSubCategoriesFailed =>
        resolve({ error })
  ),
};
