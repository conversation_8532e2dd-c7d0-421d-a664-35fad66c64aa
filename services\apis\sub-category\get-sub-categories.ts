import { TListData, TSubCategory } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetSubCategoriesParams = {
  category_id: string;
  page: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetSubCategoriesMaterials = {
  params?: TGetSubCategoriesParams;
};

export type TGetSubCategoriesResponse = TListData<TSubCategory>;

export const getSubCategories = async (
  materials: TGetSubCategoriesMaterials
): Promise<TGetSubCategoriesResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/sub-categories', { params });
  return response.data;
};
