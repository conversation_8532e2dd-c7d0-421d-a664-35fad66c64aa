import { TGetDigitalTextsResponse } from '@/services/apis';
import {
  EGetDigitalTextsAction,
  TGetDigitalTextsSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TDigitalTextState {
  getDigitalTextsResponse?: TGetDigitalTextsResponse;
}

const initialState: TDigitalTextState = {
  getDigitalTextsResponse: undefined,
};

const digitalTextSlice = createSlice({
  name: 'digitalTexts-slice',
  initialState,
  reducers: {
    resetDigitalTextSlice: () => initialState,
  },
  extraReducers: {
    [EGetDigitalTextsAction.GET_DIGITALTEXTS_SUCCESS]: (
      state: TDigitalTextState,
      action: TGetDigitalTextsSuccess
    ): TDigitalTextState => ({
      ...state,
      getDigitalTextsResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: digitalTextsReducer,
  actions: { resetDigitalTextSlice },
} = digitalTextSlice;

export { digitalTextsReducer, resetDigitalTextSlice };
