import { TPostVideoMaterials, TPostVideoResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPostVideoAction {
  POST_VIDEO = 'POST_VIDEO',
  POST_VIDEO_REQUEST = 'POST_VIDEO_REQUEST',
  POST_VIDEO_SUCCESS = 'POST_VIDEO_SUCCESS',
  POST_VIDEO_FAILED = 'POST_VIDEO_FAILED',
}

// TYPES

export type TPostVideoRequest = {
  type: EPostVideoAction.POST_VIDEO_REQUEST;
  payload: {
    materials: TPostVideoMaterials;
    successCallback?: (response: TPostVideoResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPostVideoSuccess = {
  type: EPostVideoAction.POST_VIDEO_SUCCESS;
  payload: { response?: TPostVideoResponse };
};

export type TPostVideoFailed = {
  type: EPostVideoAction.POST_VIDEO_FAILED;
};

export const postVideoAction = {
  request: createActionCreator(
    EPostVideoAction.POST_VIDEO_REQUEST,
    (resolve) =>
      (
        materials: TPostVideoMaterials,
        successCallback?: (response: TPostVideoResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TPostVideoRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPostVideoAction.POST_VIDEO_SUCCESS,
    (resolve) =>
      (response?: TPostVideoResponse): TPostVideoSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPostVideoAction.POST_VIDEO_FAILED,
    (resolve) =>
      (error: unknown): TPostVideoFailed =>
        resolve({ error })
  ),
};
