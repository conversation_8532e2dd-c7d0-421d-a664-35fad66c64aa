import { EIsActive } from '@/commons/enums';
import {
  Button,
  ColumnType,
  ConfirmModal,
  EButtonStyleType,
  Pagination,
  Table,
} from '@/components';
import { useMemo, useState } from 'react';
import moment from 'moment';
import { TUploadUsersResponse } from '@/services/apis';
import { MODAL_MESSAGE } from '@/commons/messages-enum';

type PreviewProps = {
  data: Array<TUploadUsersResponse>;
  handleImportUsers: () => Promise<void>;
  pagination: any;
  handleChangePage: (page: number) => void;
};

const PreviewUsers = ({
  data,
  handleImportUsers,
  pagination,
  handleChangePage,
}: PreviewProps) => {
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const columns = useMemo(
    () =>
      [
        {
          title: '行数',
          dataIndex: 'line_no',
          key: 'line_no',
          render: (element) => element?.line_no || '...',
        },
        {
          title: 'ログインID',
          dataIndex: 'login_id',
          key: 'login_id',
          render: (element) => element?.login_id || '...',
        },
        {
          title: '氏名',
          dataIndex: 'username',
          key: 'username',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 break-all">
              {element?.username || '...'}
            </div>
          ),
        },
        {
          title: '氏名カナ',
          dataIndex: 'username_kana',
          key: 'username_kana',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 break-all">
              {element?.username_kana || '...'}
            </div>
          ),
        },
        {
          title: 'メールアドレス',
          dataIndex: 'email',
          key: 'email',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 break-all">
              {element?.email || '...'}
            </div>
          ),
        },
        {
          title: 'パスワード',
          dataIndex: 'password',
          key: 'password',
          render: (element) => element?.password || '...',
        },
        {
          title: '権限',
          dataIndex: 'role_id',
          key: 'role_id',
          render: (element) => (
            <div>{element?.role_id === 1 ? 'ユーザー' : '講師'}</div>
          ),
        },
        {
          title: '利用開始日',
          dataIndex: 'plan_start_date',
          key: 'plan_start_date',
          render: (element) =>
            moment(element?.plan_start_date).format('YYYY-MM-DD') || '...',
        },
        {
          title: '利用終了日',
          dataIndex: 'plan_end_date',
          key: 'plan_end_date',
          render: (element) =>
            moment(element?.plan_end_date).format('YYYY-MM-DD') || '...',
        },
        {
          title: '対策動画',
          dataIndex: 'video_plan',
          key: 'video_plan',
          render: (element) => (
            <div>{element?.video_plan === 1 ? 'なし' : 'あり'}</div>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'is_active',
          key: 'is_active',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
      ] as Array<ColumnType<TUploadUsersResponse>>,
    []
  );

  const onHandleOpenModal = () => {
    setIsShowModal(true);
  };

  const onHandleCloseModal = () => {
    setIsShowModal(false);
  };

  return (
    <div className="grid grid-cols-1 mt-8 bg-alice-blue py-8 px-5">
      <div className="flex flex-col items-center gap-y-4 justify-between -mt-4">
        <div className="w-full text-left">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">
            登録
            <span className="text-primary ml-5">
              {pagination?.total ? pagination?.total : 0}件
            </span>
          </h2>
        </div>
        <Table<TUploadUsersResponse>
          columns={columns}
          dataSource={data}
          isPreview={true}
        />
        {data.length !== 0 && (
          <div className="w-full flex justify-center relative">
            <Pagination
              current={pagination.current_page || 0}
              total={pagination?.last_page || 0}
              onChange={handleChangePage}
            />
            <Button
              onClick={onHandleOpenModal}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="実行"
              className="!font-normal !absolute top-0 right-0"
            />
          </div>
        )}
      </div>
      <ConfirmModal
        visible={isShowModal}
        onClose={onHandleCloseModal}
        onConfirm={handleImportUsers}
        content={MODAL_MESSAGE.CONFIRM_CREATE}
      />
    </div>
  );
};

export default PreviewUsers;
