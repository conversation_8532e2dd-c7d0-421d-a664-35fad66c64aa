import { SizeType } from 'antd/lib/config-provider/SizeContext';
import { ButtonType } from 'antd/lib/button';
import { EButtonStyleType } from './Button.enums';
import { ReactElement } from 'react';

export type TButtonProps = {
  id?: string;
  className?: string;
  title?: string;
  reverse?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  size?: SizeType;
  type?: ButtonType;
  styleType?: EButtonStyleType;
  icon?: string | ReactElement;
  htmlType?: 'button' | 'reset' | 'submit';
  prefixIcon?: ReactElement;
};
