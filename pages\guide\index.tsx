import { Form } from 'antd';
import {
  TPostPayloadGuidanceSettingsMaterials,
  getGuidanceSettings,
  helpers,
  postGuidanceSettings,
} from '@/services/apis';
import { useEffect, useState } from 'react';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { EPlacement, ERoleId, ETypeNotification } from '@/commons/enums';
import { SettingsForm } from '@/components/SettingsForm';
import { EXCEPTION } from '@/commons/messages-enum';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';

const GuidanceSetting = () => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(true);
  const [isFirstTime, setIsFirstTime] = useState(false);
  const router = useRouter();

  const onSubmit = async (values: any) => {
    try {
      const payload: TPostPayloadGuidanceSettingsMaterials = {
        description: values.description as string,
        is_public: values.is_public as number,
      };
      const data = await postGuidanceSettings(payload);
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      router.push('/');
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  const onFetchGuidanceSettings = async () => {
    try {
      setIsLoading(true);
      const data = await getGuidanceSettings();

      form.setFieldsValue({
        is_public: data.data.is_public,
        description: data.data.description,
      });

      if (data.data.is_public === 2 && data.data.description === '') {
        setIsFirstTime(true);
      }
    } catch (error: any) {
      if (error?.request.status !== 503) {
        showNotification(
          ETypeNotification.ERROR,
          error?.data?.message
            ? error?.data?.message
            : EXCEPTION.ACTION_FAILURE,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
    setIsLoading(false);
  };

  useEffect(() => {
    onFetchGuidanceSettings();
  }, []);

  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">ガイダンス設定</div>
      {!isLoading && (
        <SettingsForm
          onSubmit={onSubmit}
          form={form}
          initialValues={{
            is_public: 2,
          }}
          isFirstTime={isFirstTime}
          fieldMessage="ガイダンス設定"
        />
      )}
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default GuidanceSetting;
