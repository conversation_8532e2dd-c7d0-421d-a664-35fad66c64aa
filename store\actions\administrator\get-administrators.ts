import {
  TGetAdministratorsMaterials,
  TGetAdministratorsResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetAdministratorsAction {
  GET_ADMINISTRATORS = 'GET_ADMINISTRATORS',
  GET_ADMINISTRATORS_REQUEST = 'GET_ADMINISTRATORS_REQUEST',
  GET_ADMINISTRATORS_SUCCESS = 'GET_ADMINISTRATORS_SUCCESS',
  GET_ADMINISTRATORS_FAILED = 'GET_ADMINISTRATORS_FAILED',
}

// TYPES

export type TGetAdministratorsRequest = {
  type: EGetAdministratorsAction.GET_ADMINISTRATORS_REQUEST;
  payload: {
    materials: TGetAdministratorsMaterials;
    successCallback?: (response: TGetAdministratorsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetAdministratorsSuccess = {
  type: EGetAdministratorsAction.GET_ADMINISTRATORS_SUCCESS;
  payload: { response?: TGetAdministratorsResponse };
};

export type TGetAdministratorsFailed = {
  type: EGetAdministratorsAction.GET_ADMINISTRATORS_FAILED;
};

export const getAdministratorsAction = {
  request: createActionCreator(
    EGetAdministratorsAction.GET_ADMINISTRATORS_REQUEST,
    (resolve) =>
      (
        materials: TGetAdministratorsMaterials,
        successCallback?: (response: TGetAdministratorsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetAdministratorsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetAdministratorsAction.GET_ADMINISTRATORS_SUCCESS,
    (resolve) =>
      (response?: TGetAdministratorsResponse): TGetAdministratorsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetAdministratorsAction.GET_ADMINISTRATORS_FAILED,
    (resolve) =>
      (error: unknown): TGetAdministratorsFailed =>
        resolve({ error })
  ),
};
