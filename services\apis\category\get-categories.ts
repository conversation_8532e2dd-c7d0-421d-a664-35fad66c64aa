import { TCategory, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetCategoriesParams = {
  page: number;
  username?: string;
  video_plan?: Array<number>;
  is_actives?: Array<number>;
};

export type TGetCategoriesMaterials = {
  params?: TGetCategoriesParams;
};

export type TGetCategoriesResponse = TListData<TCategory>;

export const getCategories = async (
  materials: TGetCategoriesMaterials
): Promise<TGetCategoriesResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/categories', { params });
  return response.data;
};
