import { TGetSubCategoriesResponse, getSubCategories } from '@/services/apis';
import { getSubCategoriesAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getSubCategoriesSaga(
  action: ActionType<typeof getSubCategoriesAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getSubCategories, materials);
    const getSubCategoriesResponse = response as TGetSubCategoriesResponse;
    yield put(getSubCategoriesAction.success(getSubCategoriesResponse));
    successCallback?.(getSubCategoriesResponse);
  } catch (err) {
    yield put(getSubCategoriesAction.failure(err));
    failedCallback?.(err);
  }
}
