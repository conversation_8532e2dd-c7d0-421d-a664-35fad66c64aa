import { CustomerModal } from '@/components';
import QuestionContentScreen from './QuesitonContent';
import { TQuestionModalProps } from './questions.types';
import QuestionExplanation from './QuestionExplanation';
import { useMemo } from 'react';
import { TExerciseResponse, TQuestion } from '@/commons/types';
import { EIsActive } from '@/commons/enums';
import clsx from 'clsx';

const PreviewQuestionModal = ({
  showModal,
  quesitonContent,
  handleCloseModal,
}: TQuestionModalProps) => {
  const choiceCorrect = useMemo(() => {
    const choices = (quesitonContent as TQuestion)?.question_choices
      ?.filter(({ is_correct }) => is_correct === EIsActive.ENABLE)
      .map(({ id }) => id || 0)
      .sort((a, b) => a - b);

    const choice = (
      quesitonContent as TExerciseResponse
    )?.exercise_choices?.find(
      ({ is_correct }) => is_correct === EIsActive.ENABLE
    )?.id;
    return choices || choice;
  }, [quesitonContent]);

  if (!quesitonContent) {
    return null;
  }
  return (
    <CustomerModal
      visible={showModal && !!quesitonContent}
      onClose={handleCloseModal}
    >
      <div className="w-full min-w-181 md:max-w-5xl grid grid-cols-1 gap-6">
        <QuestionContentScreen
          disabled
          question={quesitonContent}
          choiceCorrect={choiceCorrect}
          choiceSelected={choiceCorrect}
        />
        <div className="w-full grid grid-cols-3 items-center">
          <div className="justify-items-center col-start-2 flex items-center justify-center h-full">
            <div
              className={clsx(
                'h-9 flex items-center justify-center w-32 font-bold text-black',
                'bg-bright-orange'
              )}
            >
              正解
            </div>
          </div>
        </div>
        <QuestionExplanation question={quesitonContent} />
      </div>
    </CustomerModal>
  );
};

export default PreviewQuestionModal;
