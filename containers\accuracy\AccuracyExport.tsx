import { Button, EButtonStyleType } from '@/components';
import { EXCEPTION, validationMessage } from '@/commons/messages-enum';
import locale from 'antd/lib/date-picker/locale/ja_JP';
import { DatePicker, Form } from 'antd';
import { Rule } from 'postcss';
import { EQuestionType, ETypeNotification } from '@/commons/enums';
import moment from 'moment';
import { exportFile, showNotification } from '@/utils/functions';
import {
  getExportAccuracyExams,
  getExportAccuracyExerciseQuestions,
  getExportAccuracyPracticeQuestions,
} from '@/services/apis';

const AccuracyExport = () => {
  const [form] = Form.useForm();
  const startDate = Form.useWatch('start_date', form);
  const endDate = Form.useWatch('end_date', form);

  const exportAccuracy = async (exportType: EQuestionType) => {
    try {
      await form.validateFields();
    } catch (error) {
      return;
    }
    const start_date = form.getFieldValue('start_date')?.format('YYYY-MM-DD');
    const end_date = form.getFieldValue('end_date')?.format('YYYY-MM-DD');
    const params = {
      end_date,
      start_date,
    };

    try {
      switch (exportType) {
        case EQuestionType.EXAM: {
          const response = await getExportAccuracyExams({ params });
          exportFile(
            response,
            `Kojiro_unkan_exam_accurate_rate_${moment().format(
              'YYYYMMDDHHmm'
            )}.xlsx`
          );
          break;
        }
        case EQuestionType.PRACTICE: {
          const response = await getExportAccuracyPracticeQuestions({ params });
          exportFile(
            response,
            `Kojiro_unkan_practice_question_accurate_rate_${moment().format(
              'YYYYMMDDHHmm'
            )}.xlsx`
          );
          break;
        }
        case EQuestionType.EXERCISE: {
          const response = await getExportAccuracyExerciseQuestions({ params });
          exportFile(
            response,
            `Kojiro_unkan_exersise_question_accurate_rate_${moment().format(
              'YYYYMMDDHHmm'
            )}.xlsx`
          );
          break;
        }
        default: {
          break;
        }
      }
    } catch (error) {
      console.log(error);
      showNotification(ETypeNotification.ERROR, EXCEPTION.ACTION_FAILURE);
    }
  };

  return (
    <div className="bg-alice-blue-light py-4 px-5">
      <Form form={form}>
        <div className="text-textGray text-sm font-bold mb-2">期間指定</div>
        <div className="flex items-center mt-2">
          <Form.Item
            name={'start_date'}
            rules={[
              { required: true, message: validationMessage.required('開始日') },
            ]}
          >
            <DatePicker
              locale={locale}
              format={'YYYY/MM/DD'}
              className="w-64"
              placeholder=""
              disabledDate={(current) => {
                const customDate = moment().add(1, 'd').format('YYYY-MM-DD');
                if (endDate) {
                  return (
                    current &&
                    current > moment(endDate, 'YYYY-MM-DD').add(1, 'd')
                  );
                }
                return current && current > moment(customDate, 'YYYY-MM-DD');
              }}
            />
          </Form.Item>
          <div className="mb-6">
            <span className="mx-4">~</span>
          </div>
          <Form.Item
            name={'end_date'}
            rules={[
              { required: true, message: validationMessage.required('終了日') },
            ]}
            initialValue={moment()}
          >
            <DatePicker
              locale={locale}
              format={'YYYY/MM/DD'}
              className="w-64"
              placeholder=""
              disabledDate={(current) => {
                const customDate = moment().add(1, 'd').format('YYYY-MM-DD');
                if (startDate) {
                  return (
                    current &&
                    (current > moment(customDate, 'YYYY-MM-DD') ||
                      current < moment(startDate, 'YYYY-MM-DD'))
                  );
                }
                return current && current > moment(customDate, 'YYYY-MM-DD');
              }}
            />
          </Form.Item>
        </div>
      </Form>
      <div className="grid grid-cols-1 gap-4 font-bold mt-2">
        <div className="flex items-center">
          <span className="w-20">練習問題</span>
          <Button
            title="ダウンロード"
            styleType={EButtonStyleType.PRIMARY}
            onClick={() => exportAccuracy(EQuestionType.EXERCISE)}
          />
        </div>
        <div className="flex items-center">
          <span className="w-20">実践問題</span>
          <Button
            title="ダウンロード"
            styleType={EButtonStyleType.PRIMARY}
            onClick={() => exportAccuracy(EQuestionType.PRACTICE)}
          />
        </div>
        <div className="flex items-center">
          <span className="w-20">模擬試験</span>
          <Button
            title="ダウンロード"
            styleType={EButtonStyleType.PRIMARY}
            onClick={() => exportAccuracy(EQuestionType.EXAM)}
          />
        </div>
      </div>
    </div>
  );
};

export default AccuracyExport;
