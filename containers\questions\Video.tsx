import { TVideo } from '@/commons/types';
import ReactPlayer from 'react-player';

const Video = ({ video }: { video: TVideo }) => {
  if (!video) {
    return null;
  }
  return (
    <div className="p-5 bg-white flex flex-col h-full rounded gap-4">
      <div className="text-left text-sm font-bold text-black flex-none">
        {video?.name}
      </div>
      <div className="flex-grow flex justify-center relative">
        <div className="absolute top-0 left-0 z-10 w-full h-full flex justify-center">
          <div className="aspect-video absolute h-full">
            {video?.video_temporary_url && (
              <ReactPlayer
                width="100%"
                height="100%"
                url={video?.video_temporary_url}
                controls
                // onStart={handleStartVideo}
                config={{
                  file: { attributes: { controlsList: 'nodownload' } },
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Video;
