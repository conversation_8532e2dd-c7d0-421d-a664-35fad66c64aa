import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function EditIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="1259 362.5 16 16"
      {...props}
    >
      <g data-name="edit (1)">
        <g data-name="Group 271">
          <g data-name="Group 270">
            <path
              d="M1272.143 370.5a.571.571 0 0 0-.572.572v5.714a.571.571 0 0 1-.571.571h-10.286a.571.571 0 0 1-.571-.571v-11.429c0-.315.256-.571.571-.571h6.857a.571.571 0 1 0 0-1.143h-6.857c-.946 0-1.714.767-1.714 1.714v11.429c0 .947.768 1.714 1.714 1.714H1271c.947 0 1.714-.767 1.714-1.714v-5.715a.571.571 0 0 0-.571-.57Z"
              fill="#3e87bf"
              fillRule="evenodd"
              data-name="Path 583"
            />
          </g>
        </g>
        <g data-name="Group 273">
          <g data-name="Group 272">
            <path
              d="M1274.36 363.14a2.187 2.187 0 0 0-3.094.001l-7.527 7.526a.578.578 0 0 0-.138.224l-1.143 3.428a.571.571 0 0 0 .723.723l3.428-1.142a.572.572 0 0 0 .224-.139l7.526-7.527a2.187 2.187 0 0 0 0-3.093Zm-.809 2.287-7.428 7.429-2.22.74.74-2.216 7.43-7.429a1.045 1.045 0 0 1 1.478 1.476Z"
              fill="#3e87bf"
              fillRule="evenodd"
              data-name="Path 584"
            />
          </g>
        </g>
      </g>
    </svg>
  );
}
