import { TListData, TVideo } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetVideosParams = {
  page: number;
  is_active?: number;
  name?: string;
};

export type TGetVideosMaterials = {
  params?: TGetVideosParams;
};

export type TGetVideosResponse = TListData<TVideo>;

export const getVideos = async (
  materials: TGetVideosMaterials
): Promise<TGetVideosResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/videos', { params });
  return response.data;
};
