import { TGetMeMaterials, TGetMeResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetMeAction {
  GET_ME = 'GET_ME',
  GET_ME_REQUEST = 'GET_ME_REQUEST',
  GET_ME_SUCCESS = 'GET_ME_SUCCESS',
  GET_ME_FAILED = 'GET_ME_FAILED',
}

// TYPES

export type TGetMeRequest = {
  type: EGetMeAction.GET_ME_REQUEST;
  payload: {
    materials: TGetMeMaterials;
    successCallback?: (response: TGetMeResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetMeSuccess = {
  type: EGetMeAction.GET_ME_SUCCESS;
  payload: { response?: TGetMeResponse };
};

export type TGetMeFailed = {
  type: EGetMeAction.GET_ME_FAILED;
};

export const getMeAction = {
  request: createActionCreator(
    EGetMeAction.GET_ME_REQUEST,
    (resolve) =>
      (
        materials: TGetMeMaterials,
        successCallback?: (response: TGetMeResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetMeRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetMeAction.GET_ME_SUCCESS,
    (resolve) =>
      (response?: TGetMeResponse): TGetMeSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetMeAction.GET_ME_FAILED,
    (resolve) =>
      (error: unknown): TGetMeFailed =>
        resolve({ error })
  ),
};
