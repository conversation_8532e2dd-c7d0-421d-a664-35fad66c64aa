import { Fragment, useEffect, useMemo, useState } from 'react';
import { PreviewQuestionModal } from '../questions';
import { TExerciseResponse, TKnowledgeBoard, TQuestion } from '@/commons/types';
import { EQuestionType } from '@/commons/enums';
import {
  getDetailExerciseAction,
  getPracticeQuestionAction,
} from '@/store/actions';
import { useDispatch } from 'react-redux';

export type TPreviewQuestionKnowledgeProps = {
  inquiry: TKnowledgeBoard;
};

const PreviewQuestionKnowledge = ({
  inquiry,
}: TPreviewQuestionKnowledgeProps) => {
  const [questionContent, setQuestionContent] = useState<
    TExerciseResponse | TQuestion
  >();
  const dispatch = useDispatch();
  const [isShowModal, setIsShowModal] = useState<boolean>(false);

  const questionId = useMemo(() => {
    return inquiry?.exercise_question?.id || inquiry?.question?.id;
  }, [inquiry]);

  useEffect(() => {
    if (!isShowModal) {
      return;
    }
    if (inquiry.type === EQuestionType.EXERCISE) {
      const params = { id: questionId };
      dispatch(
        getDetailExerciseAction.request({ params }, (response) => {
          setQuestionContent(response.data);
        })
      );
    }
    if (
      inquiry.type === EQuestionType.EXAM ||
      inquiry.type === EQuestionType.PRACTICE
    ) {
      dispatch(
        getPracticeQuestionAction.request(
          { paths: { id: questionId } },
          (response) => {
            setQuestionContent(response.data);
          }
        )
      );
    }
  }, [dispatch, inquiry.type, isShowModal, questionId]);

  const handleOpenModal = () => {
    setIsShowModal(true);
  };

  const handleCloseModal = () => {
    setIsShowModal(false);
  };

  return (
    <Fragment>
      <PreviewQuestionModal
        quesitonContent={questionContent}
        showModal={isShowModal}
        handleCloseModal={handleCloseModal}
      />
      <button onClick={handleOpenModal}>
        <span className="text-primary underline decoration-solid">
          {questionId}
        </span>
      </button>
    </Fragment>
  );
};

export default PreviewQuestionKnowledge;
