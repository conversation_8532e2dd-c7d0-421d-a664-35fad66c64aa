import {
  ArrowLeftIcon,
  ArrowRightIcon,
  Button,
  CustomerModal,
  EButtonStyleType,
  UserButton,
} from '@/components';
import { TVideoExplanationProps } from './questions.types';
import { Fragment, useMemo, useState } from 'react';
import dynamic from 'next/dynamic';
import clsx from 'clsx';
const Video = dynamic(() => import('./Video'), { ssr: false });

const VideoExplanation = ({ videos }: TVideoExplanationProps) => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [currentIdx, setCurrentIdx] = useState<number>(0);

  const disabledPrevious = useMemo(() => currentIdx <= 0, [currentIdx]);
  const disabledNext = useMemo(
    () => currentIdx >= (videos?.length || 0) - 1,
    [currentIdx, videos]
  );

  const currentVideo = useMemo(
    () => videos?.[currentIdx],
    [videos, currentIdx]
  );

  const handleShowModal = () => {
    if (videos.length > 0) {
      setShowModal(true);
      return;
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setCurrentIdx(0);
  };

  const handleNextVideo = () => {
    setCurrentIdx((idx) => idx + 1);
  };

  const handlePreviousVideo = () => {
    setCurrentIdx((idx) => idx - 1);
  };
  return (
    <Fragment>
      {videos.length > 0 && (
        <UserButton
          title="対策動画"
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          className="!w-36"
          onClick={handleShowModal}
        />
      )}
      <CustomerModal
        visible={showModal}
        onClose={handleCloseModal}
        centered
        destroyOnClose
      >
        <div className="grid grid-cols-1 gap-5 mt-4">
          <div className="bg-new-white p-4 w-full lg:w-232 h-96 rounded flex flex-col gap-3">
            <h3 className="text-base font-bold text-dark-gray text-center">
              {currentVideo?.category?.name}
            </h3>
            <div className="flex-grow">
              <Video video={currentVideo} key={currentVideo?.id} />
            </div>
          </div>
          {(!disabledPrevious || !disabledNext) && (
            <div className="w-full flex items-center justify-between gap-5 flex-col md:flex-row">
              {!disabledPrevious ? (
                <UserButton
                  title="前の動画へ"
                  className={clsx('w-38', disabledPrevious && 'invisible')}
                  styleType={EButtonStyleType.GREEN_BLUE}
                  onClick={handlePreviousVideo}
                  disabled={disabledPrevious}
                  prefixIcon={<ArrowLeftIcon strokeColor="#ffffff" />}
                />
              ) : (
                <div />
              )}
              {!disabledNext ? (
                <UserButton
                  title="次の動画へ"
                  className={clsx('w-38', disabledNext && 'invisible')}
                  styleType={EButtonStyleType.GREEN_BLUE}
                  onClick={handleNextVideo}
                  disabled={disabledNext}
                  icon={<ArrowRightIcon strokeColor="#ffffff" />}
                />
              ) : (
                <div />
              )}
            </div>
          )}
          <div className="w-full flex items-center justify-center">
            <UserButton
              title="戻る"
              className="w-38"
              styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
              prefixIcon={<ArrowLeftIcon strokeColor="#0560a6" />}
              onClick={handleCloseModal}
            />
          </div>
        </div>
      </CustomerModal>
    </Fragment>
  );
};

export default VideoExplanation;
