import { TListData, TOptionDigital } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetSelectDigitalParams = {
    page: number;
    category_id: string;
    name?: string;
    is_actives?: Array<number>;
};

export type TGetSelectDigitalMaterials = {
    params?: TGetSelectDigitalParams;
};

export type TGetSelectDigitalResponse = TListData<TOptionDigital>;

export const getSelectDigital = async (
    materials: TGetSelectDigitalMaterials
): Promise<TGetSelectDigitalResponse> => {
    const { params } = materials;
    const response = await ApiService.get('/select2/digital-texts', { params });
    return response.data;
};