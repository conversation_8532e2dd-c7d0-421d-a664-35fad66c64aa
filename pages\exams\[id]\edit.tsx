import { EIsActive, ERoleId, ETypeNotification } from '@/commons/enums';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { TExam } from '@/commons/types';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { ExamForm, TExamForm } from '@/containers';
import { getExam } from '@/services/apis';
import {
  deleteExamAction,
  getExamAction,
  putExamAction,
} from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { useHistory } from '@/utils/contexts';
import { showNotification } from '@/utils/functions';
import { Form } from 'antd';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { Fragment, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

type TEditExamsPageProps = {
  examId: string;
};

const EditExamsPage = ({ examId }: TEditExamsPageProps) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);
  const [isShowModalSubmit, setIsShowModalSubmit] = useState<boolean>(false);
  const [isVisibleDelete, setIsVisibleDelete] = useState<boolean>(false);
  const [form] = Form.useForm<TExamForm>();
  const historyManager = useHistory();
  const canGoBack = historyManager.canGoBack();

  useEffect(() => {
    if (me?.data.role_id === ERoleId.TEACHER) {
      router.push('/403');
    }
  }, [me?.data.role_id, router]);

  useEffect(() => {
    const paths = { id: examId };
    dispatch(
      getExamAction.request({ paths }, (exam) => {
        if (exam?.data) {
          form.setFieldsValue(exam.data);
        }
      })
    );
  }, [dispatch, examId, form]);

  const handleSubmit = (payload: TExamForm) => {
    dispatch(
      putExamAction.request({ paths: { id: examId }, payload }, () => {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        setTimeout(() => {
          if (canGoBack) {
            router.back();
            return;
          }
          router.push('/exams');
        });
      })
    );
  };

  const handleDelete = () => {
    dispatch(
      deleteExamAction.request({ paths: { id: examId } }, () => {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        setTimeout(() => {
          if (canGoBack) {
            router.back();
            return;
          }
          router.push('/exams');
        });
      })
    );
  };

  const handleClickToBack = () => {
    if (canGoBack) {
      router.back();
      return;
    }
    router.push('/exams');
  };

  const handleClickSubmit = () => {
    if (!form) {
      return;
    }
    form.submit();
  };

  const showModalSubmit = async () => {
    try {
      await form.validateFields();
      setIsShowModalSubmit(true);
    } catch (error) {}
  };

  const onCloseModalSubmit = () => {
    setIsShowModalSubmit(false);
  };

  const onCloseModalDelete = () => {
    setIsVisibleDelete(false);
  };

  return (
    <Fragment>
      <ConfirmModal
        visible={isShowModalSubmit}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
        onClose={onCloseModalSubmit}
        onConfirm={handleClickSubmit}
      />
      <ConfirmModal
        visible={isVisibleDelete}
        onClose={onCloseModalDelete}
        content={MODAL_MESSAGE?.CONFIRM_DELETE}
        onConfirm={handleDelete}
      />
      <div className="mx-12 my-4 grid grid-cols-1 gap-5">
        <div className="flex justify-between items-center">
          <h1 className="text-lg font-bold text-black-kj">模擬試験名編集</h1>
        </div>
        <div className="w-full px-5 pt-5 pb-1 bg-new-white">
          <div className="mb-6 text-sm">
            <h2 className="text-black-kj font-bold">
              模擬試験ID<span className="mx-3 text-primary">{examId}</span>
            </h2>
          </div>
          <ExamForm formInstance={form} handleSubmit={handleSubmit} />
        </div>
        <div className="w-full flex items-center justify-end gap-5">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="戻る"
            className="!font-normal"
            onClick={handleClickToBack}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="更新"
            className="!font-normal"
            onClick={showModalSubmit}
          />
        </div>
      </div>
    </Fragment>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  const { id } = query;
  if (!id) {
    throw new Error('missing category id');
  }

  try {
    return {
      props: { examId: id },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default EditExamsPage;
