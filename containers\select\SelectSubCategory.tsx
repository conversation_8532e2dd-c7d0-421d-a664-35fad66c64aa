import { EIsActive } from '@/commons/enums';
import { TOptionCategory } from '@/commons/types';
import { Select } from '@/components/Select';
import { getSelectSubCategories, getSubCategory } from '@/services/apis';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { TFilterSelectCategory, TSelectSubCategoryProps } from './select.types';
import { useDispatch } from 'react-redux';
import { getSelectSubCategoriesAction } from '@/store/actions';

const { Option } = Select;

const SelectSubCategory = ({
  value,
  category_id,
  onChange,
  size = 'large',
  className = 'w-full rounded input-disabled',
  placeholder = '選択してください',
  disabled,
}: TSelectSubCategoryProps) => {
  const dispatch = useDispatch();
  const [filterSelect, setFilterSelect] = useState<TFilterSelectCategory>({
    page: 1,
  });
  const [options, setOptions] = useState<Array<TOptionCategory>>([]);
  const [lastPage, setLastPage] = useState<number>(1);
  // const [lable, setLable] = useState<TOptionCategory>();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const fetchData = async (
    category_id: string,
    page: number = 1,
    name?: string
  ) => {
    try {
      const params = {
        category_id,
        page,
        name: name ? name : undefined,
        is_actives: [EIsActive.ENABLE, EIsActive.DISABLE],
      };
      if (name === undefined && value) {
        dispatch(
          getSelectSubCategoriesAction.request(
            { params },
            (response) => {
              if (response) {
                const { data, meta } = response;
                setLastPage(meta.last_page);
                if (meta.current_page === 1) {
                  setOptions(data);
                } else {
                  setOptions((current) => [...current, ...data]);
                }
              }
            },
            () => {
              setOptions([]);
            }
          )
        );
        return;
      }

      const { data, meta } = await getSelectSubCategories({ params });
      setLastPage(meta.last_page);
      if (meta.current_page === 1) {
        setOptions(data);
      } else {
        setOptions((current) => [...current, ...data]);
      }
    } catch (error) {
      setOptions([]);
    }
  };

  useEffect(() => {
    if (!category_id) {
      setOptions([]);
      return;
    }
    fetchData(category_id, filterSelect?.page, filterSelect?.name);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [category_id, filterSelect?.name, filterSelect?.page]);

  const setKeyword = (name: string) => {
    if (name !== filterSelect.name) {
      setFilterSelect({ page: 1, name });
    }
  };

  const loadMore = useCallback(() => {
    if (
      !filterSelect?.page ||
      (filterSelect?.page && filterSelect?.page >= lastPage)
    ) {
      return null;
    }
    setFilterSelect((current) => ({
      ...current,
      page: (current?.page || 0) + 1,
    }));
  }, [filterSelect?.page, lastPage]);

  useEffect(() => {
    const check = options.find(({ id }) => `${value}` === `${id}`);
    if (!check && value) {
      loadMore();
    }
  }, [loadMore, options, value]);

  const handleOnChange = (value: string) => {
    onChange?.(value);
  };

  const valueOption = useMemo(() => {
    if (!options.length) {
      return;
    }
    return value;
  }, [value, options]);

  return (
    <Select
      showSearch={true}
      onSearch={setKeyword}
      onLoadMore={loadMore}
      onChange={handleOnChange}
      size={size}
      className={className}
      value={valueOption}
      disabled={disabled}
      placeholder={placeholder}
    >
      {options.map((option) => (
        <Option key={option.id} value={option.id}>
          {`(ID:${option.id}) ${option.name}`}
        </Option>
      ))}
    </Select>
  );
};

export default SelectSubCategory;
