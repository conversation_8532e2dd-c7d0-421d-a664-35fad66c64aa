import { CustomerModal } from '@/components';
import { useMemo } from 'react';
import { TExerciseResponse, TQuestion } from '@/commons/types';
import { EIsActive } from '@/commons/enums';
import clsx from 'clsx';

type TPreviewLawsModalEditUpdateProps = {
  visiable: boolean;
  handleCloseModal: () => void;
  name?: string;
  content: string;
};

const PreviewLawsModalEditUpdate = ({
  visiable,
  content,
  name,
  handleCloseModal,
}: TPreviewLawsModalEditUpdateProps) => {
  return (
    <CustomerModal visible={visiable} onClose={handleCloseModal} centered>
      <div className="bg-new-white px-2 py-3 w-full min-w-full md:min-w-181 lg:w-232">
        <div className="bg-white px-4 py-5">
          <div className="pb-3 border-b border-blue-border border-dashed">
            <h3 className="text-base font-bold text-dark-gray">
              {name ? name : ''}
            </h3>
          </div>
          <div className="p-5 h-80 overflow-scroll scrollbar-hide">
            {content && (
              <div
                className="text-black !leading-normal text-lg html-render"
                dangerouslySetInnerHTML={{ __html: content }}
              />
            )}
          </div>
        </div>
      </div>
    </CustomerModal>
  );
};

export default PreviewLawsModalEditUpdate;
