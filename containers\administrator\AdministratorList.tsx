import { EIsActive } from '@/commons/enums';
import { TAdministrator } from '@/commons/types';
import {
  Button,
  ColumnType,
  EButtonStyleType,
  EditIcon,
  Pagination,
  Table,
} from '@/components';
import { RootState } from '@/store/configureStore';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TFilterAdministrator } from './administrator.types';
import FilterAdministrator from './FilterAdministrator';
import { getAdminsAction } from '@/store/actions';
import { isObjectEmpty, removeEmpty } from '@/utils/functions';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import { resetAdminSlice } from '@/store/slices/admins';

const AdministratorList = ({ query }: { query: TAdministrator }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { asPath } = router;
  const [filter, setFilter] = useState<TFilterAdministrator>();

  const { getAdminsResponse: admins } = useSelector(
    (state: RootState) => state.admin
  );
  const { data, meta } = admins || {};

  useEffect(() => {
    return () => {
      dispatch(resetAdminSlice());
    };
  }, [dispatch]);

  const getActions = useCallback(
    (element: TAdministrator) => {
      return (
        <div className="flex items-center gap-5">
          <Link
            href={`/admins/${element.id}/edit`}
            onClick={() => dispatch(setCallBackUrl(asPath))}
          >
            <EditIcon />
          </Link>
        </div>
      );
    },
    [asPath, dispatch]
  );

  const columns = useMemo(
    () => [
      {
        title: '管理者ID',
        dataIndex: 'id',
        key: 'id',
        render: (element) => `${element?.id}`,
      },
      {
        title: '氏名',
        dataIndex: 'username',
        key: 'username',
        render: (element) => (
          <div className="max-w-50 2xl:max-w-70 break-all">
            {element?.username}
          </div>
        ),
      },
      {
        title: '氏名カナ',
        dataIndex: 'name',
        key: 'name',
        render: (element) => (
          <div className="max-w-50 2xl:max-w-70 break-all">
            {element?.username_kana}
          </div>
        ),
      },
      {
        title: '権限',
        dataIndex: 'authority',
        key: 'authority',
        render: (element) => (
          <div className="truncate">
            {element?.role_id === 1 ? '管理者' : '講師'}
          </div>
        ),
      },
      {
        title: 'ログインID',
        dataIndex: 'loginID',
        key: 'loginID',
        render: (element) => (
          <div className="max-w-50 2xl:max-w-70  break-all">
            {element?.login_id}
          </div>
        ),
      },
      {
        title: '有効・無効',
        dataIndex: 'invalid',
        key: 'invalid',
        render: (element) => (
          <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
        ),
      },
      {
        title: 'アクション',
        dataIndex: 'action',
        key: 'action',
        className: 'w-32',
        render: (element) => element && getActions(element),
      },
    ],
    [getActions]
  ) as Array<ColumnType<TAdministrator>>;

  const setDefaultFilter = () => {
    const defaultFitler = {
      is_actives: [EIsActive.ENABLE],
    };
    setFilter(defaultFitler);
  };

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(asPath));
    router.push('/admins/create');
  };

  const fetchData = useCallback(
    (page: number, filter?: TFilterAdministrator) => {
      const username_kana =
        filter?.username_kana?.trim().toLocaleLowerCase() || undefined;
      // const is_active = filter?.is_active ? filter?.is_active : 1;
      const is_actives = filter?.is_actives as Array<number>;
      const role_id = filter?.role_id;
      const params = { page, username_kana, is_actives, role_id };
      dispatch(getAdminsAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ query }, undefined, {
        shallow: true,
      });
      fetchData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter, fetchData]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
          <FilterAdministrator
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        <div className="self-end flex items-center gap-2">
          <Button
            onClick={handleRedirectToAdd}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="新規登録"
            className="!font-normal"
          />
        </div>
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            meta?.total || 0
          }件`}</h2>
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<TAdministrator> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default AdministratorList;
