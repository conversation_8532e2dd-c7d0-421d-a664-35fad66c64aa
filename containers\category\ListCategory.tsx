import { EIsActive, EPlacement, ETypeNotification } from '@/commons/enums';
import { TCategory } from '@/commons/types';
import {
  Button,
  ColumnType,
  EditIcon,
  EnterIcon,
  Pagination,
  Table,
} from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { getExportCategories } from '@/services/apis';
import { getCategoriesAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TFilterCategory } from './category.types';
import FilterCategory from './FilterCategory';
import { EXCEPTION } from '@/commons/messages-enum';
import { ParsedUrlQueryInput } from 'querystring';
import {
  setBackObjectUrl,
  setCallBackUrl,
} from '@/store/slices/status/history';
import clsx from 'clsx';
import { resetCategoryState } from '@/store/slices/category';

const ListCategory = ({ query }: { query: TFilterCategory }) => {
  const route = useRouter();
  const dispatch = useDispatch();
  const [filter, setFilter] = useState<TFilterCategory>();
  const { getCategoriesResponse: categories } = useSelector(
    (state: RootState) => state.category
  );

  const { data, meta } = categories || {};

  useEffect(() => {
    return () => {
      dispatch(resetCategoryState());
    };
  }, [dispatch]);

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
    };
    setFilter(defaultFilter);
  };

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
  }, [query]);

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(route.asPath));
    route.push('/category/create');
  };

  const getActions = useCallback(
    (element: TCategory) => {
      return (
        <div className="flex items-center gap-8">
          <Link
            href={`/category/${element.id}/sub-categories`}
            onClick={() => {
              dispatch(setCallBackUrl(route.asPath));
              dispatch(setBackObjectUrl({ category: route.asPath }));
            }}
          >
            <EnterIcon />
          </Link>
          <Link
            href={`/category/${element.id}/edit`}
            onClick={() => dispatch(setCallBackUrl(route.asPath))}
          >
            <EditIcon />
          </Link>
        </div>
      );
    },
    [dispatch, route.asPath]
  );

  const columns = useMemo(
    () =>
      [
        {
          title: '表示順',
          dataIndex: 'show_order',
          key: 'show_order',
          render: (element) => (
            <div className="w-40">{element?.show_order}</div>
          ),
        },
        {
          title: '大項目ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => <div className="w-40">{element?.id}</div>,
        },
        {
          title: '大項目名',
          dataIndex: 'name',
          key: 'name',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-lg break-all">
              {element?.name}
            </div>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'invalid',
          key: 'invalid',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TCategory>>,
    [getActions]
  );

  const fetchData = useCallback(
    (page: number, filter?: TFilterCategory) => {
      const name = filter?.name?.trim().toLocaleLowerCase() || undefined;
      const is_actives = filter?.is_actives || undefined;
      const params = { page, name, is_actives };
      dispatch(getCategoriesAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      route.push({ pathname: '/category', query }, undefined, {
        shallow: true,
      });
      fetchData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleExportCategory = async () => {
    const name = filter?.name?.trim().toLocaleLowerCase() || undefined;
    const is_actives = filter?.is_actives || undefined;
    const params = { name, is_actives };
    try {
      const response = await getExportCategories({ params });
      const fileName = `Kojiro_unkan_categories_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-end rounded-md">
          <FilterCategory
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        <div className="self-end flex items-center gap-2">
          <Button
            onClick={handleExportCategory}
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="出力"
            className="!font-normal"
          />
          <Button
            onClick={handleRedirectToAdd}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="新規登録"
            className="!font-normal"
          />
        </div>
      </div>
      <div
        className={clsx('flex flex-col items-center gap-y-4 justify-between')}
      >
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            meta?.total || 0
          }件`}</h2>
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<TCategory> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default ListCategory;
