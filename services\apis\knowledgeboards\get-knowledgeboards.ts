import { TInquiry, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetKnowledgeBoardsParams = {
  category_id?: string;
  sub_category_id?: string;
  question_id?: number;
  types?: Array<number>;
  is_publics?: Array<number>;
  teacher_reply?: number;
  admin_reply?: number;
};

export type TGetKnowledgeBoardsMaterials = {
  params?: TGetKnowledgeBoardsParams;
};

export type TGetKnowledgeBoardsResponse = TListData<TInquiry>;

export const getKnowledgeBoards = async (
  materials: TGetKnowledgeBoardsMaterials
): Promise<TGetKnowledgeBoardsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/knowledge-boards', { params });
  return response.data;
};
