import { Input } from '../../../components/Input';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Form, Radio } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import dynamic from 'next/dynamic';
import { EIsActive, ERoleId, ETypeNotification } from '@/commons/enums';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { useRouter } from 'next/router';
import {
  deleteDigitalText,
  getDetailDigitalText,
  helpers,
  updateDigitalText,
} from '@/services/apis';
import { SelectBigCategory, SelectSubCategory } from '@/containers/select';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import { Rule } from 'antd/lib/form';
import validate from '@/utils/validate';
import { GetServerSideProps } from 'next';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import clsx from 'clsx';
import { digitalTextBaseUrl } from '@/commons/constants';

const { checkMaxString } = validate;

const EditDigitalText = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { id } = router.query;
  const [isVisible, setVisible] = useState<boolean>(false);
  const [param, setParam] = useState<any>();
  const [isLoading, setIsLoading] = useState(true);
  const [isVisibleEdit, setVisibleEdit] = useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);
  const big_category_id = Form.useWatch('category_id', form);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = await getDetailDigitalText(id);
      const paramData = data?.data;
      if (paramData) {
        setParam(paramData);
        form.setFieldsValue({
          is_active: paramData?.is_active,
          content: paramData?.content,
          name: paramData?.name,
          category_id: paramData?.category?.id,
          sub_category_id: paramData?.sub_category?.id,
        });
        setIsLoading(false);
      }
    } catch (error: any) {
      setIsLoading(false);
      showNotification(ETypeNotification?.ERROR, error?.data?.message);
    }
  }, [form, id]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-2">
        <Form.Item className={'!mb-0'} name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onBack = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router?.push('/digital-texts');
  };

  const onSubmit = async () => {
    try {
      const values = form.getFieldsValue();

      const payload = {
        name: values.name,
        is_active: values.is_active,
        content: values.content,
        category_id: values.category_id,
        sub_category_id: values.sub_category_id,
      };
      const data = await updateDigitalText(id, payload);
      if (data) {
        onBack();
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onChangeBigCategory = () => {
    form.setFieldsValue({
      sub_category_id: null,
    });
  };

  const onCloseModal = () => {
    setVisible(false);
  };

  const onOpenModal = () => {
    setVisible(true);
  };

  const onDelete = async () => {
    try {
      const data = await deleteDigitalText(id);
      showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      onBack();
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onCloseModalEdit = () => {
    setVisibleEdit(false);
  };

  const onOpenModalEdit = () => {
    setVisibleEdit(true);
  };

  const previewDigitalText = () => {
    const link = document.createElement('a');
    link.href = `${digitalTextBaseUrl}${form.getFieldValue('content')}`;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
  };

  return (
    <div className={clsx('mx-12 mt-4', isLoading && 'hidden')}>
      <span className="text-lg font-bold">デジタルテキスト編集</span>
      <Form form={form} onFinish={onOpenModalEdit}>
        <div className="bg-alice-blue-light py-4 px-5 mt-3">
          <div className="flex justify-between">
            <div className="flex flex-row">
              <span className="text-base font-bold">テクストID</span>
              <span className="ml-3 text-primary text-base font-bold">
                {param?.id}
              </span>
            </div>
            <Button
              size={'small'}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title={'プレビュー'}
              onClick={previewDigitalText}
            />
          </div>
          <div className="grid grid-cols-2 justify-between mt-6 gap-5">
            <div>
              <span className="text-textGray text-sm font-bold">大項目名</span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('大項目名'),
                  },
                ]}
                className={'!mb-0'}
                name={'category_id'}
              >
                <SelectBigCategory
                  placeholder={'選択してください'}
                  onChange={onChangeBigCategory}
                  size={'middle'}
                  className={'!mt-2'}
                />
              </Form.Item>
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">小項目名</span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('小項目名'),
                  },
                ]}
                className={'!mb-0'}
                name={'sub_category_id'}
              >
                <SelectSubCategory
                  size={'middle'}
                  className={'!mt-2'}
                  category_id={big_category_id}
                />
              </Form.Item>
            </div>
          </div>
          <div className="grid grid-cols-2 justify-between mt-6 gap-5">
            <div>
              <span className="text-textGray text-sm font-bold">
                テキスト名
              </span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('テキスト名'),
                    },
                    () => checkMaxString('テキスト名', 255),
                  ] as Rule[]
                }
                className={'!mb-0'}
                name={'name'}
              >
                <Input classNames={'!mt-2'} />
              </Form.Item>
              <div className="mt-7">
                <div className="flex flex-row">
                  <span className="text-textGray text-sm font-bold">
                    有効・無効
                  </span>
                </div>
                {ratioRender()}
              </div>
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">URL</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('URL'),
                    },
                    () => checkMaxString('URL', 255),
                  ] as Rule[]
                }
                className={'!mb-0'}
                name={'content'}
              >
                <Input classNames={'!mt-2'} />
              </Form.Item>
            </div>
          </div>
        </div>
        <ConfirmModal
          visible={isVisibleEdit}
          onClose={onCloseModalEdit}
          content={MODAL_MESSAGE?.CONFIRM_EDIT}
          onConfirm={onSubmit}
        />
        <ConfirmModal
          visible={isVisible}
          onClose={onCloseModal}
          content={MODAL_MESSAGE.CONFIRM_DELETE}
          onConfirm={onDelete}
        />
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={onBack}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            onClick={onOpenModal}
            size={'small'}
            styleType={EButtonStyleType.DANGER}
            title={'削除'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
          />
        </div>
      </Form>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default EditDigitalText;
