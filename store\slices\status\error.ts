import { getType } from 'deox';
import { AxiosError } from 'axios';
import getT from 'next-translate/getT';

import { showNotification } from 'utils/functions';
import Router from 'next/router';
import { uiActions } from '@/store/actions';
import { EResponseCode, ETypeNotification } from '@/commons/enums';

export type TErrorState = {
  [id: string]: { error: null | Error | string; code?: number } | null;
};

interface IErrorPayload {
  error: Error | string;
}

interface IErrorAction {
  type: string;
  payload?: IErrorPayload;
}

interface IResetAction {
  type: string;
  payload: {
    actionName: string;
  };
}

const getErrorMatches = (actionType: string): RegExpExecArray | null =>
  /(.*)_(REQUEST|FAILED)/.exec(actionType);

const errorReducer = (
  state: TErrorState = {},
  action: IErrorAction | IResetAction
): TErrorState => {
  if (action.type === getType(uiActions.resetActionStatus)) {
    const { actionName } = (action as IResetAction).payload;
    const { [actionName]: _, ...newState } = state;
    return newState;
  }

  const matches = getErrorMatches(action.type);

  if (!matches) {
    return state;
  }

  const [, requestName, requestState] = matches;

  let error = (action as IErrorAction).payload?.error;

  const axiosErrorStatus = (error as AxiosError)?.status;

  if (error) {
    const axiosErrorData: any =
      (error as AxiosError)?.response?.data || (error as any)?.data;

    const mapErrors = axiosErrorData?.errors;
    const errorList = mapErrors
      ? Object.keys(mapErrors)
        .map((key) => mapErrors[key].join(','))
        .join('. ')
      : undefined;

    error =
      errorList || axiosErrorData?.message || axiosErrorData?.error_description;

    if (error && axiosErrorStatus !== EResponseCode.SERVICE_UNAVAILABLE) {
      getT(Router.locale, 'common').then((t) => {
        const messageError = t(error as string);
        showNotification(ETypeNotification.ERROR, messageError);
      });
    }
  }

  return {
    ...state,
    [requestName]:
      requestState === 'FAILED' && error
        ? { error, code: axiosErrorStatus }
        : null,
  };
};

export default errorReducer;
