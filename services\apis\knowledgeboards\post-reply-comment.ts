import ApiService from '@/services/apis';

type TPostReplyCommentPaths = {
  knowledgeBoardId: string;
};
type TPostReplyCommentPayload = {
  reply_type: number;
  reply_to_id?: number;
  content: string;
};

type TPostReplyCommentMaterials = {
  paths: TPostReplyCommentPaths;
  payload: TPostReplyCommentPayload;
};

export type TPostReplyCommentResponse = unknown;

export const postReplyComment = async (
  materials: TPostReplyCommentMaterials
): Promise<TPostReplyCommentResponse> => {
  const { paths, payload } = materials;
  const response = await ApiService.post(
    `/knowledge-boards/${paths.knowledgeBoardId}/comments`,
    payload
  );
  return response.data;
};
