import { TPostExamMaterials, TPostExamResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPostExamAction {
  POST_EXAM = 'POST_EXAM',
  POST_EXAM_REQUEST = 'POST_EXAM_REQUEST',
  POST_EXAM_SUCCESS = 'POST_EXAM_SUCCESS',
  POST_EXAM_FAILED = 'POST_EXAM_FAILED',
}

// TYPES

export type TPostExamRequest = {
  type: EPostExamAction.POST_EXAM_REQUEST;
  payload: {
    materials: TPostExamMaterials;
    successCallback?: (response: TPostExamResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPostExamSuccess = {
  type: EPostExamAction.POST_EXAM_SUCCESS;
  payload: { response?: TPostExamResponse };
};

export type TPostExamFailed = {
  type: EPostExamAction.POST_EXAM_FAILED;
};

export const postExamAction = {
  request: createActionCreator(
    EPostExamAction.POST_EXAM_REQUEST,
    (resolve) =>
      (
        materials: TPostExamMaterials,
        successCallback?: (response: TPostExamResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TPostExamRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPostExamAction.POST_EXAM_SUCCESS,
    (resolve) =>
      (response?: TPostExamResponse): TPostExamSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPostExamAction.POST_EXAM_FAILED,
    (resolve) =>
      (error: unknown): TPostExamFailed =>
        resolve({ error })
  ),
};
