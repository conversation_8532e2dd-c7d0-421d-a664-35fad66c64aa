import { TOptionCategory } from '@/commons/types';
import { Select } from '@/components/Select';
import { getSelectAvailableDiscounts } from '@/services/apis';
import { useCallback, useEffect, useState } from 'react';
import {
  TFilterSelectCategory,
  TSelectAvailableDiscountProps,
} from './select.types';

const { Option } = Select;

const SelectAvailableDiscount = ({
  value,
  onChange,
  size = 'large',
  className = 'w-full rounded input-disabled',
  placeholder = '',
  disabled,
}: TSelectAvailableDiscountProps) => {
  const [filterSelect, setFilterSelect] = useState<TFilterSelectCategory>({
    page: 1,
    name: '',
  });
  const [options, setOptions] = useState<Array<TOptionCategory>>([]);
  const [lastPage, setLastPage] = useState<number>(1);

  const fetchData = async (page: number = 1, name?: string) => {
    try {
      const params = {
        page,
        name: name ? name : undefined,
      };
      const { data, last_page, current_page } =
        await getSelectAvailableDiscounts({ params });
      setLastPage(last_page);
      if (current_page === 1) {
        setOptions(data);
      } else {
        setOptions((current) => [...current, ...data]);
      }
    } catch (error) {
      console.log(error);
      setOptions([]);
    }
  };

  useEffect(() => {
    fetchData(filterSelect?.page, filterSelect?.name);
  }, [filterSelect?.name, filterSelect?.page]);

  const setKeyword = (name: string) => {
    if (name !== filterSelect.name) {
      setFilterSelect({ page: 1, name });
    }
  };

  const loadMore = useCallback(() => {
    if (
      !filterSelect?.page ||
      (filterSelect?.page && filterSelect?.page >= lastPage)
    ) {
      return null;
    }
    setFilterSelect((current) => ({
      ...current,
      page: (current?.page || 0) + 1,
    }));
  }, [filterSelect?.page, lastPage]);

  useEffect(() => {
    const check = options.find(({ id }) => `${value}` === `${id}`);
    if (!check && value) {
      loadMore();
    }
  }, [loadMore, options, value]);

  const handleOnChange = (value: string) => {
    onChange?.(value);
  };

  return (
    <Select
      showSearch={true}
      onSearch={setKeyword}
      onLoadMore={loadMore}
      onChange={handleOnChange}
      size={size}
      disabled={disabled}
      className={className}
      value={value}
      placeholder={placeholder}
    >
      {options.map((option) => (
        <Option key={option.id} value={option.id}>
          {`${option.name}`}
        </Option>
      ))}
    </Select>
  );
};

export default SelectAvailableDiscount;
