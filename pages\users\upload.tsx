import {
  Button,
  ConfirmModal,
  EButtonStyleType,
  Input,
  UploadFile,
} from '@/components';
import { EXCEPTION } from '@/commons/messages-enum';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import { PreviewUsers } from '@/containers';
import { TUploadUsersResponse, helpers, uploadUsers } from '@/services/apis';
import { showNotification } from '@/utils/functions';
import { EPlacement, ERoleId, ETypeNotification } from '@/commons/enums';
import { GetServerSideProps } from 'next';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';

const PER_PAGE = 20;
const defaultPagination = {
  current_page: 1,
  last_page: 1,
  total: 0,
};

const AddNewUserPage = () => {
  const router = useRouter();
  const [file, setFile] = useState<File>();
  const [usersUploadList, setUsersUploadList] = useState<
    Array<TUploadUsersResponse>
  >([]);
  const [usersListPagination, setUsersListPagination] = useState<
    Array<TUploadUsersResponse>
  >([]);
  const [errors, setErrors] = useState<Array<Array<string>>>([]);
  const [fileValidateMessages, setFileValidateMessages] = useState<string>();
  const [pagination, setPagination] = useState(defaultPagination);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const handleImportUsers = async () => {
    try {
      if (file) {
        const data = await uploadUsers({
          is_preview: 0,
          file,
        });
        showNotification(
          ETypeNotification.SUCCESS,
          EXCEPTION.ACTION_SUCCESS,
          EPlacement.BOTTOM_RIGHT
        );
      }
      backToList();
    } catch (error) {
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  const onHandleSubmitPreview = async () => {
    setErrors([]);
    if (!file) {
      setFileValidateMessages('ファイルは必須項目です。');
    }
    try {
      if (file) {
        const data = await uploadUsers({
          is_preview: 1,
          file,
        });
        setUsersUploadList(data);
        showNotification(
          ETypeNotification.SUCCESS,
          EXCEPTION.ACTION_SUCCESS,
          EPlacement.BOTTOM_RIGHT
        );

        const lastPage = Math.ceil(data.length / PER_PAGE);

        setPagination({
          ...defaultPagination,
          total: data.length,
          last_page: lastPage,
        });
      }
    } catch (error: any) {
      if (!error) {
        showNotification(
          ETypeNotification.ERROR,
          EXCEPTION.TRY_AGAIN
        );
        setFile(undefined);
      } else {
        setUsersUploadList([]);
        setErrors(error?.data?.errors);
        showNotification(ETypeNotification.ERROR, EXCEPTION.ACTION_FAILURE);
      }
    }
  };
  const backToList = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/users');
  };

  const handleSuccess = (file: File) => {
    setFile(file);
    // reset message errors
    setFileValidateMessages('');
  };

  const handleError = () => {
    console.log('Error!');
  };

  const onErrorSize = () => {
    setFileValidateMessages(
      'ファイルは、51200KB以下のファイルでなければいけません。'
    );
  };
  const onErrorType = () => {
    setFileValidateMessages(
      'ファイルには、以下のファイルタイプを指定してください。xlsx'
    );
  };

  const handleChangePage = (page: number) => {
    setPagination({
      ...pagination,
      current_page: page,
    });
  };

  const handleSetUserList = (pagination: any) => {
    // add line no for table data
    const usersListWithRow = usersUploadList.map((user, index) => {
      user.line_no = index + 2;
      return user;
    });
    let userListPage;
    if (pagination.current_page === 1) {
      userListPage = usersListWithRow.slice(
        0,
        PER_PAGE * pagination.current_page
      );
    } else {
      userListPage = usersListWithRow.slice(
        PER_PAGE * (pagination.current_page - 1),
        PER_PAGE * pagination.current_page
      );
    }

    setUsersListPagination(userListPage);
  };

  useEffect(() => {
    handleSetUserList(pagination);
  }, [pagination, usersUploadList]);

  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">
        ユーザー情報インポート（登録）
      </div>
      <div className="bg-alice-blue-light py-4 px-5">
        <div className="w-full">
          <div className="text-textGray text-sm font-bold mb-2">
            ユーザー情報
          </div>
          <div className="flex items-center">
            <div className="mr-8">
              <Input
                disabled
                value={file?.name}
                classNames="!w-96 !rounded !mr-8"
                size="large"
              />
              <UploadFile
                accept=".xlsx"
                type={['xlsx']}
                maxSize={52428800}
                // 50Mb = 52428800 Bytes
                setFile={handleSuccess}
                maxCount={1}
                onError={handleError}
                onErrorSize={onErrorSize}
                onErrorType={onErrorType}
              >
                <button
                  className={clsx(
                    'rounded bg-primary bg-opacity-10',
                    '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold'
                  )}
                >
                  Excelファイル選択
                </button>
              </UploadFile>
            </div>
            <div className="flex gap-4">
              <Button
                onClick={onHandleSubmitPreview}
                size={'small'}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title={'登録'}
              />
              <Button
                onClick={backToList}
                size={'small'}
                styleType={EButtonStyleType.OUTLINE_PRIMARY}
                title={'戻る'}
              />
            </div>
          </div>
          {fileValidateMessages && (
            <div style={{ color: '#ff4d4f' }}>{fileValidateMessages}</div>
          )}
        </div>
      </div>
      {usersUploadList.length > 0 && (
        <PreviewUsers
          data={usersListPagination}
          handleImportUsers={handleImportUsers}
          pagination={pagination}
          handleChangePage={handleChangePage}
        />
      )}
      <div>
        {errors &&
          errors.flat(Infinity).map((error, idx) => (
            <div key={idx} style={{ color: '#ff4d4f' }}>
              {error}
            </div>
          ))}
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default AddNewUserPage;
