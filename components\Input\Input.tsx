import React, { ChangeEvent, KeyboardEvent } from 'react';
import { Input as AntdInput } from 'antd';

import { TInputProps } from './Input.types';
// import { formatPhoneNumber } from '@/utils/functions';

const Input = ({
  classNames,
  type,
  placeholder,
  prefix,
  suffix,
  numberic,
  phoneNumber,
  disabled,
  onChange,
  onEnter,
  value,
  maxLength = 255,
  name,
  size,
}: TInputProps) => {
  const handleKeydown = (e: KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === 'Enter' || e.keyCode === 13) {
      onEnter?.();
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { value: inputValue } = e.target;
    console.log(value);
    if (numberic && !phoneNumber) {
      const reg = /^-?\d*(\d*)?$/;
      const isNumbericPass = reg.test(inputValue) || inputValue === '';
      isNumbericPass && onChange?.(inputValue);
    } else if (phoneNumber) {
      const reg = /^-?\d*((\-\d*)+)?$/;
      const isPhoneNumberPass = reg.test(inputValue) || inputValue === '';
      isPhoneNumberPass && onChange?.(inputValue);
    } else {
      onChange?.(inputValue.trimStart());
    }
  };

  const handleOnBlur = () => {
    onChange?.(value?.trimEnd() || '');
  };

  return (
    <>
      {type === 'password' ? (
        <AntdInput.Password
          className={classNames}
          type={type}
          placeholder={placeholder}
          value={value}
          prefix={prefix}
          suffix={suffix}
          onChange={handleChange}
          onKeyDown={handleKeydown}
          onBlur={handleOnBlur}
          disabled={disabled}
          size={size}
          name={name}
        />
      ) : (
        <AntdInput
          className={classNames}
          type={type}
          placeholder={placeholder}
          value={value}
          prefix={prefix}
          suffix={suffix}
          onChange={handleChange}
          onKeyDown={handleKeydown}
          onBlur={handleOnBlur}
          disabled={disabled}
          size={size}
          name={name}
        />
      )}
    </>
  );
};

export default Input;
