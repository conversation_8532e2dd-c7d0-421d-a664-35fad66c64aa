import {
  ArrowLeftIcon,
  ArrowRightIcon,
  Button,
  EButtonStyleType,
  Image,
  UserButton,
} from '@/components';
import { Fragment, useEffect, useMemo, useState } from 'react';
import CustomerModal from '@/components/Modal/CustomerModal';
import { TDigital } from '@/commons/types';
import validate from '@/utils/validate';
import DigitalTextIFrame from './DigitalTextIFrame';
import { checkLink } from '@/utils/functions';
import clsx from 'clsx';
import { TDigitalTextExplanationProps } from './questions.types';
import { digitalTextBaseUrl } from '@/commons/constants';

const DigitalTextExplanation = ({
  digital_texts,
}: TDigitalTextExplanationProps) => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [currentIdx, setCurrentIdx] = useState<number>(0);

  useEffect(() => {
    if (showModal) {
      setCurrentIdx(0);
    }
    return () => {
      setCurrentIdx(0);
    };
  }, [showModal]);

  const disabledPrevious = useMemo(() => currentIdx <= 0, [currentIdx]);

  const disabledNext = useMemo(
    () => currentIdx >= (digital_texts?.length || 0) - 1,
    [currentIdx, digital_texts]
  );
  const currentDigital = useMemo(
    () => digital_texts?.[currentIdx],
    [digital_texts, currentIdx]
  );

  const handleShowModal = () => {
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleNextDigital = () => {
    setCurrentIdx((idx) => idx + 1);
  };

  const handlePreviousDigital = () => {
    setCurrentIdx((idx) => idx - 1);
  };

  const content = useMemo(() => {
    if (!currentDigital?.content) {
      return null;
    }
    return (
      <DigitalTextIFrame
        link={`${digitalTextBaseUrl}${currentDigital?.content}`}
      />
    );
  }, [currentDigital?.content]);
  if (!digital_texts?.length) {
    return null;
  }

  return (
    <Fragment>
      <CustomerModal
        visible={showModal}
        onClose={handleCloseModal}
        centered
        // fullWidth
      >
        <div className="flex flex-col gap-5 mt-4 w-[85vw] h-[90vh]">
          <div className="w-full text-center">
            <h3 className="text-base font-bold text-dark-gray break-all">
              {currentDigital.name}
            </h3>
          </div>
          {content}
          {(!disabledPrevious || !disabledNext) && (
            <div className="w-full flex items-center justify-between flex-row">
              {!disabledPrevious ? (
                <UserButton
                  title="前のテキストへ"
                  className={clsx('w-44', disabledPrevious && 'invisible')}
                  styleType={EButtonStyleType.GREEN_BLUE}
                  onClick={handlePreviousDigital}
                  disabled={disabledPrevious}
                  prefixIcon={<ArrowLeftIcon strokeColor="#ffffff" />}
                />
              ) : (
                <div />
              )}
              {!disabledNext ? (
                <UserButton
                  title="次のテキストへ"
                  className={clsx('w-44', disabledNext && 'invisible')}
                  styleType={EButtonStyleType.GREEN_BLUE}
                  onClick={handleNextDigital}
                  disabled={disabledNext}
                  icon={<ArrowRightIcon strokeColor="#ffffff" />}
                />
              ) : (
                <div />
              )}
            </div>
          )}

          <div className="w-full flex items-center justify-center">
            <UserButton
              title="戻る"
              className="w-38"
              styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
              prefixIcon={<ArrowLeftIcon strokeColor="#0560a6" />}
              onClick={handleCloseModal}
            />
          </div>
        </div>
      </CustomerModal>
      {digital_texts.length > 0 && (
        <UserButton
          title="デジタルテキスト"
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          onClick={handleShowModal}
          className="!w-36"
        />
      )}
    </Fragment>
  );
};

export default DigitalTextExplanation;
