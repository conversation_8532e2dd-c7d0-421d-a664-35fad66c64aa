import {
  TGetSelectSubCategoriesResponse,
  getSelectSubCategories,
} from '@/services/apis';
import { getSelectSubCategoriesAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getSelectSubCategoriesSaga(
  action: ActionType<typeof getSelectSubCategoriesAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getSelectSubCategories, materials);
    const GetSelectSubCategoriesResponse =
      response as TGetSelectSubCategoriesResponse;
    yield put(
      getSelectSubCategoriesAction.success(GetSelectSubCategoriesResponse)
    );
    successCallback?.(GetSelectSubCategoriesResponse);
  } catch (err) {
    yield put(getSelectSubCategoriesAction.failure(err));
    failedCallback?.(err);
  }
}
