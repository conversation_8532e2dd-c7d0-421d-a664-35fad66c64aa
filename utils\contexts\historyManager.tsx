import { useRouter } from 'next/router';
import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from 'react';

export const HistoryManagerContext = createContext<
  ReturnType<typeof useHistoryManager>
>({ history: [], canGoBack: () => false });

export function HistoryManagerProvider({
  value,
  children,
}: {
  value: ReturnType<typeof useHistoryManager>;
  children: ReactNode;
}) {
  return (
    <HistoryManagerContext.Provider value={value}>
      {children}
    </HistoryManagerContext.Provider>
  );
}
export const useHistory = () => useContext(HistoryManagerContext);

export function useHistoryManager() {
  const router = useRouter();
  const [history, setHistory] = useState<string[]>([]);

  useEffect(() => {
    const handleRouteChange = (url: string, { shallow }: any) => {
      if (!shallow) {
        setHistory((prevState) => [...prevState, url]);
      }
    };

    router.beforePopState(() => {
      setHistory((prevState) => prevState.slice(0, -2));
      return true;
    });

    router.events.on('routeChangeStart', handleRouteChange);

    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, []);

  return { history, canGoBack: () => history.length > 1 };
}
