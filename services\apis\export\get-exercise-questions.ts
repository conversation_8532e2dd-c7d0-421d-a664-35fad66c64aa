import ApiService from '@/services/apis';

export type TGetExportExerciseQuestionsParams = {
  category_id?: number;
  sub_category_id?: number;
  content?: string;
  title?: string;
  id?: number;
  question_year?: string;
  is_actives?: Array<number>;
};

export type TGetExportExerciseQuestionsMaterials = {
  params?: TGetExportExerciseQuestionsParams;
};

export type TGetExportExerciseQuestionsResponse = Blob;

export const getExportExerciseQuestions = async (
  materials: TGetExportExerciseQuestionsMaterials
): Promise<TGetExportExerciseQuestionsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('export/exercise-questions', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
