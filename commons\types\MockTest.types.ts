import { TCategory } from '@/services/apis';
import { EIsActive, EQuestionForm, ESetingStatus } from '../enums';
import { TLaw } from './Law.types';
import { TVideo } from './Video.types';
import { TDigital } from '.';

export type TMockTest = {
  id?: number;
  category: TCategory;
  question_no: number;
  year?: number;
  name?: string;
  allocation_points?: number;
  question_form?: string;
  is_active: EIsActive;
  newQuestion_no?: number;
};

export type TExam = {
  id?: number;
  show_order: number;
  name: string;
  is_active: EIsActive;
  setting_status: ESetingStatus;
  active_questions_count?: number;
  active_questions_sum_allocation_point?: string;
};

export type TCategorySetting = {
  category_id?: number;
  question_amount?: number;
  require_correct_amount?: number;
  category?: TCategory;
};

export type TExamSetting = {
  limit_time?: number;
  pass_point?: number;
  total_point?: number;
  category_settings?: Array<TCategorySetting>;
};

export type TQuestion = {
  id?: number;
  show_order: number;
  question_year: string;
  title: string;
  type: EQuestionForm;
  question_explanation_images: Array<any>;
  question_choices: Array<TSubQuestion>;
  laws: Array<TLaw>;
  videos: Array<TVideo>;
  category: TCategory;
  allocation_point: number;
  is_active: EIsActive;
  content?: string;
  explanation?: string;
  image?: string;
  digital_texts: Array<TDigital>;
  question_no: number;
  videos_count?: number;
};

export type TSubQuestion = {
  id?: number;
  subquestion_no: number;
  choice_no: number;
  is_correct: EIsActive;
  content: string;
};

export type TCouponPlan = {
  id: number,
  price: number,
  plan_key: string,
  key: string,
  deleted_at: string | null,
  created_at: string,
  updated_at: string,
  plan: {
    id: number,
    key: string,
    type: number,
    days: number,
    price: number,
    ec_plan_id: number,
    deleted_at: string | null,
    created_at: string,
    updated_at: string
  }
};

export interface ICoupon {
  id?: string,
  name: string,
  code: string,
  start_date: string,
  end_date: string,
  training_school: string,
  discount: number,
  note: string,
  status: number,
  iap_receipts_count: number
};
