import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { QuestionForm } from '@/containers';
import { getExamAction, postQuestionAction } from '@/store/actions';
import { Form } from 'antd';
import { useRouter } from 'next/router';
import { Fragment, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import {
  EIsActive,
  ENeedCheckWithSetting,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { TOptionSub, TSubQuestion } from '@/commons/types';
import { showNotification } from '@/utils/functions';
import { GetServerSideProps } from 'next';
import { helpers } from '@/services/apis';

const CreateExamsPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [form] = Form.useForm<any>();
  const [invalidTab, setInvalidTab] = useState<{ value: string }>();
  const dispatch = useDispatch();
  const { getExamResponse: exam } = useSelector(
    (store: RootState) => store.mockTest
  );
  const [isShowModalSubmit, setIsShowModalSubmit] = useState<boolean>(false);
  const [isShowModalSetting, setIsShowModalSetting] = useState<boolean>(false);
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);
  const { callBackUrl } = useSelector((state: RootState) => state.history);
  useEffect(() => {
    if (me?.data.role_id === ERoleId.TEACHER) {
      router.push('/403');
    }
  }, [me?.data.role_id, router]);

  useEffect(() => {
    if (id && typeof id === 'string') {
      dispatch(getExamAction.request({ paths: { id } }));
    }
  }, [id, dispatch]);

  const createExamQuestion = (values: any) => {
    if (id && typeof id === 'string') {
      const paths = { exam_id: id };
      const {
        law_ids: lawOptions,
        digital_text_ids: digitalOptions,
        video_ids: videoOptions,
        sub_questions: subQuestions,
        explanation_images: explanationImages,
        image: imageContent = '',
        ...restValues
      } = values;
      const sub_questions = subQuestions?.reduce(
        (
          s: Array<Array<TSubQuestion>>,
          questions: Array<TSubQuestion>,
          mIdx: number
        ) => {
          if (
            !questions ||
            questions.every((question) => !question || !question.content)
          ) {
            return s;
          }
          const subQ = questions.reduce((s: Array<TSubQuestion>, item, idx) => {
            if (!item || !item.content) {
              return s;
            }
            const rsI = {
              subquestion_no: mIdx + 1,
              choice_no: idx + 1,
              is_correct: item?.is_correct
                ? EIsActive.ENABLE
                : EIsActive.DISABLE,
              content: item.content,
            };
            return [...s, rsI];
          }, []);
          return [...s, subQ];
        },
        []
      );
      const law_ids = lawOptions
        ?.filter((law: TOptionSub) => law)
        .map(({ id }: TOptionSub) => id);
      const digital_text_ids = digitalOptions
        ?.filter((digital: TOptionSub) => digital)
        .map(({ id }: TOptionSub) => id);
      const video_ids = videoOptions
        ?.filter((digital: TOptionSub) => digital)
        .map(({ id }: TOptionSub) => id);
      const explanation_images = explanationImages.filter(
        (i: any) => i?.explanation_image
      );
      const payload = {
        ...restValues,
        sub_questions,
        law_ids,
        digital_text_ids,
        video_ids,
        explanation_images,
        image: imageContent,
      };
      dispatch(
        postQuestionAction.request({ payload, paths }, (response: any) => {
          const { is_pass_setting } = response;
          if (is_pass_setting === false) {
            setIsShowModalSetting(true);
          } else {
            showNotification(
              ETypeNotification?.SUCCESS,
              EXCEPTION.ACTION_SUCCESS
            );
            setTimeout(() => {
              handleClickToBack();
            });
          }
        })
      );
    }
  };

  const handleSubmit = (values: any) => {
    createExamQuestion({
      ...values,
      need_check_with_setting: ENeedCheckWithSetting.NEED,
    });
  };

  const handleClickToBack = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push(`/exams/${id}/questions`);
  };

  const handleClickSubmit = () => {
    if (!form) {
      return;
    }
    form.submit();
  };

  const handleClickSubmitWithCheck = async () => {
    try {
      await form.validateFields();
      const values = form.getFieldsValue();
      createExamQuestion({
        ...values,
        need_check_with_setting: ENeedCheckWithSetting.DONT_NEED,
      });
    } catch (error) {}
  };

  const showModalSubmit = async () => {
    try {
      await form.validateFields();
      const subQuestions = form.getFieldValue('sub_questions');
      if (!subQuestions) {
        setInvalidTab({ value: 'part1' });
        return;
      }
      setIsShowModalSubmit(true);
    } catch ({ values, errorFields }: any) {
      const firstErrorTab = (errorFields as any)?.find(({ name }: any) => {
        return name.indexOf('sub_questions') !== -1;
      });
      if (firstErrorTab) {
        const { name, errors } = firstErrorTab;
        if (errors.includes('設問の項目数は、2個以上にしてください。')) {
          const { sub_questions } = values as any;
          const firstEmpty = sub_questions.findIndex(
            (questions: Array<any>) =>
              !questions ||
              questions.every((question) => !question || !question.content)
          );
          if (firstEmpty !== -1) {
            setInvalidTab({ value: `part${firstEmpty + 1}` });
          } else {
            setInvalidTab({ value: `part${sub_questions.length + 1}` });
          }
          return;
        }
        setInvalidTab({ value: `part${name[1] + 1}` });
      }
    }
  };

  const onCloseModalSubmit = () => {
    setIsShowModalSubmit(false);
  };

  const onCloseModalSetting = () => {
    setIsShowModalSetting(false);
  };

  return (
    <Fragment>
      <ConfirmModal
        visible={isShowModalSubmit}
        content={MODAL_MESSAGE.CONFIRM_CREATE}
        onClose={onCloseModalSubmit}
        onConfirm={handleClickSubmit}
      />
      <ConfirmModal
        visible={isShowModalSetting}
        content={
          <div className="max-w-lg">{MODAL_MESSAGE.CONFIRM_SETTING_EXAM}</div>
        }
        onClose={onCloseModalSetting}
        onConfirm={handleClickSubmitWithCheck}
      />
      <div className="mx-12 my-4 grid grid-cols-1 gap-5">
        <div className="flex justify-between items-center">
          <h1 className="text-lg font-bold text-black-kj flex break-all">
            <span>{`${exam?.data.name}_問題新規登録`}</span>
          </h1>
        </div>
        <div className="w-full px-5 pt-4 bg-new-white">
          <QuestionForm
            formInstance={form}
            handleSubmit={handleSubmit}
            invalidTab={invalidTab}
          />
        </div>
        <div className="w-full flex items-center justify-end gap-5">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="戻る"
            className="!font-normal"
            onClick={handleClickToBack}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="登録"
            className="!font-normal"
            onClick={showModalSubmit}
          />
        </div>
      </div>
    </Fragment>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default CreateExamsPage;
