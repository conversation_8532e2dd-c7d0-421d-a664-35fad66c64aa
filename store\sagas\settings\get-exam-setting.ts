import { TGetExamSettingResponse, getExamSetting } from '@/services/apis';
import { getExamSettingAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getExamSettingSaga(
  action: ActionType<typeof getExamSettingAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getExamSetting, materials);
    const getExamSettingResponse = response as TGetExamSettingResponse;
    yield put(getExamSettingAction.success(getExamSettingResponse));
    successCallback?.(getExamSettingResponse);
  } catch (err) {
    yield put(getExamSettingAction.failure(err));
    failedCallback?.(err);
  }
}
