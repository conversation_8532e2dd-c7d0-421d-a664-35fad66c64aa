// import { RequestCookies } from 'next/dist/server/web/spec-extension/cookies';
import { Cookies } from 'react-cookie';
import { TGetMeData } from './apis';
import { NextApiRequestCookies } from 'next/dist/server/api-utils';

// const COOKIE_DOMAIN = env.cookie.domain;
const COOKIE_ACCESS_TOKEN = 'COOKIE_ACCESS_TOKEN';
const COOKIE_REFRESH_TOKEN = 'COOKIE_REFRESH_TOKEN';
const COOKIE_ME = 'COOKIE_ME';
// const MAXIMUM_EXPIRES_TIME = 0 * 24 * 60 * 60;

const cookieSetting = {
  path: '/',
  // domain: COOKIE_DOMAIN,
  secure: true,
  // httpOnly: true,
  // maxAge: MAXIMUM_EXPIRES_TIME,
};

const cookie = new Cookies();

export const setCookie = (name: string, value: string): void =>
  cookie.set(name, value, cookieSetting);

export const getCookie = (name: string): string => cookie.get(name);

const removeCookie = (name: string): void => cookie.remove(name, cookieSetting);

class Helpers {
  getRefreshToken = (): string => getCookie(COOKIE_REFRESH_TOKEN);

  storeRefreshToken = (refreshToken: string): void =>
    setCookie(COOKIE_REFRESH_TOKEN, refreshToken);

  getAccessToken = (): string => getCookie(COOKIE_ACCESS_TOKEN);

  getMeCookie = () => {
    const me = getCookie(COOKIE_ME);
    return me;
  };

  storeAccessToken = (accessToken: string): void => {
    setCookie(COOKIE_ACCESS_TOKEN, accessToken);
  };

  storeUserInfo = (me: TGetMeData): void => {
    setCookie(COOKIE_ME, JSON.stringify(me));
  };

  clearTokens = (): void => {
    removeCookie(COOKIE_REFRESH_TOKEN);
    removeCookie(COOKIE_ACCESS_TOKEN);
    removeCookie(COOKIE_ME);
  };
  getMeFromReq = (cookies: NextApiRequestCookies) => {
    const me = cookies[COOKIE_ME];
    if (me) {
      return JSON.parse(me) as TGetMeData;
    }
    return null;
  };
}

const instance = new Helpers();

export default instance;
