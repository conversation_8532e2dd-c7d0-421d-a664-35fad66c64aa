/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './containers/**/*.{js,ts,jsx,tsx}',
    './layouts/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: '#004D8D',
        secondary: '#2DC8A8',
        'primary-variant': '#3E87BF',
        'primary-variant-2': '#93C9F6',
        warning: '#F7D975',
        'black-kj': '#333',
        'alice-blue': '#f2f9fc',
        cotton: '#7298b8',
        textGray: '#333333',
        'dark-cerulean': '#004b88',
        'alice-blue-light': '#f4f9fc',
        'dark-shade-of-gray': '#202020',
        'green-blue': '#005597',
        danger: '#ff0000',
        'dim-gray': '#707070',
        'new-white': '#f3f9fc',
        'gray-90': '#e5e5e5',
        green: '#00b050',
        'bright-orange': '#ffda48',
      },
      height: {
        7.5: '1.875rem',
        15: '3.75rem',
        'full-screen': '100vh',
      },
      width: {
        38: '9.5rem',
        50: '12.5rem',
        75: '18.75rem',
        84: '21rem',
        'full-screen': '100vw',
        4.8: '48%',
        125: '31.25rem',
        232: '58rem',
      },
      minWidth: {
        25: '100px',
        35: '140px',
        50: '200px',
        75: '300px',
        181: '45.25rem',
      },
      minHeight: {
        40: '10rem',
      },
      maxHeight: {
        182: '45.5rem',
      },

      maxWidth: {
        25: '100px',
        30: '120px',
        50: '200px',
        60: '240px',
        70: '280px',
        75: '300px',
        125: '500px',
        '8xl': '1336px',
      },
      fontFamily: {
        inherit: 'inherit',
      },
    },
  },
  plugins: [
    require('@tailwindcss/line-clamp'),
    require('tailwind-scrollbar-hide'),
  ],
};
