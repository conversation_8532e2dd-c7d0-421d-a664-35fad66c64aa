import { TGetAccountMaterials, TGetAccountResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetAccountAction {
  GET_ACCOUNT = 'GET_ACCOUNT',
  GET_ACCOUNT_REQUEST = 'GET_ACCOUNT_REQUEST',
  GET_ACCOUNT_SUCCESS = 'GET_ACCOUNT_SUCCESS',
  GET_ACCOUNT_FAILED = 'GET_ACCOUNT_FAILED',
}

// TYPES

export type TGetAccountRequest = {
  type: EGetAccountAction.GET_ACCOUNT_REQUEST;
  payload: {
    materials: TGetAccountMaterials;
    successCallback?: (response: TGetAccountResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetAccountSuccess = {
  type: EGetAccountAction.GET_ACCOUNT_SUCCESS;
  payload: { response?: TGetAccountResponse };
};

export type TGetAccountFailed = {
  type: EGetAccountAction.GET_ACCOUNT_FAILED;
};

export const getAccountAction = {
  request: createActionCreator(
    EGetAccountAction.GET_ACCOUNT_REQUEST,
    (resolve) =>
      (
        materials: TGetAccountMaterials,
        successCallback?: (response: TGetAccountResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetAccountRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetAccountAction.GET_ACCOUNT_SUCCESS,
    (resolve) =>
      (response?: TGetAccountResponse): TGetAccountSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetAccountAction.GET_ACCOUNT_FAILED,
    (resolve) =>
      (error: unknown): TGetAccountFailed =>
        resolve({ error })
  ),
};
