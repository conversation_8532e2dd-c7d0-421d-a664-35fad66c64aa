import { EIsActive, EQuestionForm } from '@/commons/enums';
import { TSubQuestion } from '@/commons/types';
import ApiService from '@/services/apis';

export type TPutQuestionPayload = {
  exam_id: number;
  category_id: number;
  show_order: number;
  question_year: string;
  title: string;
  is_active: EIsActive;
  allocation_point: number;
  content?: string;
  image?: string;
  type: EQuestionForm;
  explanation?: string;
  explanation_images?: Array<{ explanation_image: string }>;
  law_ids?: Array<number>;
  digital_text_ids?: Array<number>;
  video_ids?: Array<number>;
  sub_questions?: Array<Array<TSubQuestion>>;
};

export type TPutQuestionPaths = {
  exam_id: string;
  questionId: string;
};

export type TPutQuestionMaterials = {
  payload: TPutQuestionPayload;
  paths: TPutQuestionPaths;
};

export type TPutQuestionResponse = unknown;

export const putQuestion = async (
  materials: TPutQuestionMaterials
): Promise<TPutQuestionResponse> => {
  const { payload, paths } = materials;
  const response = await ApiService.put(
    `/exams/${paths.exam_id}/questions/${paths.questionId}`,
    payload
  );
  return response.data;
};
