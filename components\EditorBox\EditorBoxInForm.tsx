import React, { useEffect, useMemo, useRef, useState } from 'react';
import JoditEditor from 'jodit-react';

const buttonsConfig = [
  'source',
  '|',
  'bold',
  'italic',
  'underline',
  '|',
  'font',
  'fontsize',
  '|',
  '|',
  'left',
  'center',
  'right',
  'justify',
  '|',
  'hr',
  'fullsize',
  'brush',
  'link',
  '|',
  'preview',
];

const EditorBoxForm = (props: any) => {
  const { placeholder, className, value, onChange, height, disable } = props;
  const editor = useRef(null);

  useEffect(() => {
    if (value) {
    }
  }, [value]);

  const config = useMemo(
    () => ({
      defaultMode: 2,
      readonly: disable,
      height: height || '300px',
      width: '100%',
      buttons: buttonsConfig,
      uploader: { insertImageAsBase64URI: true },
      toolbarAdaptive: false,
      style: {
        fontSize: '18px',
        background: '#ffffff',
        color: '#000000',
      },
      placeholder: placeholder || '',
      limitChars: 65535,
      cleanHTML: {
        fillEmptyParagraph: false,
        removeEmptyElements: true,
      },
      showCharsCounter: false,
      autofocus: false,
    }),
    [disable, height, placeholder]
  );
  const hasValue = !!value;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const valueEditor = useMemo(() => (hasValue ? value : ''), [hasValue]);
  return (
    <div className={className}>
      {useMemo(
        () => (
          <JoditEditor
            className="text-editor"
            value={valueEditor}
            ref={editor}
            config={config}
            onChange={onChange}
          />
        ),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [valueEditor]
      )}
    </div>
  );
};

export default EditorBoxForm;
