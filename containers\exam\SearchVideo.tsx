import { Button, EButtonStyleType } from '@/components';
import { SelectBigCategory, SelectVideos } from '../select';
import { TOptionCategory, TOptionSub } from '@/commons/types';
import { useEffect, useState } from 'react';

const SearchVideo = ({
  value,
  onChangeVideo,
  onClose,
}: {
  value?: TOptionSub;
  onClose?: () => void;
  onChangeVideo: (value: TOptionSub) => void;
}) => {
  const [categoryId, setCategoryId] = useState<string>();
  const [video, setVideo] = useState<TOptionCategory>();

  useEffect(() => {
    if (!value) {
      setCategoryId(undefined);
      setVideo(undefined);
    } else {
      setVideo(value);
      setCategoryId(value.category_id);
    }
  }, [value]);

  const selectVideo = () => {
    if (video && categoryId) {
      onChangeVideo({ ...video, category_id: categoryId });
    }
  };

  const selectCategory = (id?: string) => {
    setCategoryId(id);
    setVideo(undefined);
  };

  return (
    <div className="grid grid-cols-1 gap-5 w-125 h-56">
      <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
        <p className="text-black-kj font-bold">大項目名</p>
        <SelectBigCategory onChange={selectCategory} value={categoryId} />
      </div>
      <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
        <p className="text-black-kj font-bold">動画名</p>
        <SelectVideos
          category_id={categoryId}
          value={video?.id || ''}
          onChange={setVideo}
          className="w-full"
          placeholder="選択してください"
        />
      </div>
      <div className="flex justify-center gap-5">
        <Button
          title="戻る"
          className="self-center"
          styleType={EButtonStyleType.OUTLINE_PRIMARY}
          onClick={onClose}
        />
        <Button
          disabled={!video}
          title="追加"
          className="self-center"
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          onClick={selectVideo}
        />
      </div>
    </div>
  );
};

export default SearchVideo;
