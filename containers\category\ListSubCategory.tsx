import { EIsActive } from '@/commons/enums';
import { TSubCategory } from '@/commons/types';
import { Button, ColumnType, EditIcon, Pagination, Table } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Select } from '@/components/Select';
import { getSubCategoriesAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { Checkbox, Radio } from 'antd';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TFilterCategory, TListSubCategoryProps } from './category.types';
import FilterCategory from './FilterCategory';
import FilterSubCategory from './FilterSubCategory';
import { useHistory } from '@/utils/contexts';
import { isObjectEmpty, removeEmpty } from '@/utils/functions';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import { resetSubCategorySlice } from '@/store/slices/sub-category';

const ListSubCategory = ({ category, query }: TListSubCategoryProps) => {
  const route = useRouter();
  const dispatch = useDispatch();
  const [filter, setFilter] = useState<TFilterCategory>();
  const { getSubCategoriesResponse: subCategories } = useSelector(
    (state: RootState) => state.subCategory
  );
  const { data, meta } = subCategories || {};
  const { backURL } = useSelector((state: RootState) => state.history);

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
    };
    setFilter(defaultFilter);
  };

  useEffect(() => {
    return () => {
      dispatch(resetSubCategorySlice());
    };
  }, [dispatch]);

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
  }, [query]);

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(route.asPath));
    route.push(`/category/${category.id}/sub-categories/create`);
  };

  const getActions = useCallback(
    (element: TSubCategory) => {
      return (
        <div className="flex items-center justify-center gap-5">
          <Link
            onClick={() => dispatch(setCallBackUrl(route.asPath))}
            href={`/category/${category.id}/sub-categories/${element.id}/edit`}
          >
            <EditIcon />
          </Link>
        </div>
      );
    },
    [category.id, dispatch, route.asPath]
  );

  const columns = useMemo(
    () =>
      [
        {
          title: '表示順',
          dataIndex: 'show_order',
          key: 'show_order',
          render: (element) => element?.show_order,
        },
        {
          title: '大項目名',
          dataIndex: 'category.name',
          key: 'category.name',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-lg break-all">
              {element?.category.name}
            </div>
          ),
        },
        {
          title: '小項目ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => <>{element?.id}</>,
        },
        {
          title: '小項目名',
          dataIndex: 'name',
          key: 'name',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-lg break-all">
              {element?.name}
            </div>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'invalid',
          key: 'invalid',
          className: 'w-40',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TSubCategory>>,
    [getActions]
  );

  const fetchData = useCallback(
    (page: number, filter?: TFilterCategory) => {
      if (category?.id) {
        const name = filter?.name?.trim() || undefined;
        const is_actives = filter?.is_actives as Array<number>;
        const params = { category_id: category?.id, page, name, is_actives };
        dispatch(getSubCategoriesAction.request({ params }));
      }
    },
    [category?.id, dispatch]
  );

  useEffect(() => {
    if (filter) {
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      route.push({ query: { ...query, id: category?.id } }, undefined, {
        shallow: true,
      });
      fetchData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleBackCategory = () => {
    const url = backURL?.['category'];
    if (url) {
      route.push(url);
      return;
    }
    route.push('/category');
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex-col lg:flex-row flex justify-between items-end rounded-md">
          <FilterSubCategory
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        <div className="self-end flex items-center gap-2">
          <Button
            onClick={handleBackCategory}
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="戻る"
            className="!font-normal"
          />
          <Button
            onClick={handleRedirectToAdd}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="新規登録"
            className="!font-normal"
          />
        </div>
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            meta?.total || 0
          }件`}</h2>
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<TSubCategory> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default ListSubCategory;
