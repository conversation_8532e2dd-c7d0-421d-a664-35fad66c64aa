import { TListData, TOptionVideo } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetSelectVideosParams = {
  page: number;
  category_id: string;
  is_actives?: Array<number>;
};

export type TGetSelectVideosMaterials = {
  params?: TGetSelectVideosParams;
};

export type TGetSelectVideosResponse = TListData<TOptionVideo>;

export const getSelectVideos = async (
  materials: TGetSelectVideosMaterials
): Promise<TGetSelectVideosResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/select2/videos', { params });
  return response.data;
};
