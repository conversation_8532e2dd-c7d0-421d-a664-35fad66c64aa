import ApiService from '@/services/apis';

export type TUploadUpdateQuestions = {
  need_check_with_setting: number;
  is_preview: number;
  file: File;
};
export type TUploadUpdateQuestionsResponse = any;
// export type TUploadUsersResponse = {
//   line_no?: number;
//   login_id: string;
//   username: string;
//   username_kana: string;
//   email: string;
//   password: string;
//   role_id: number;
//   plan_start_date: string;
//   plan_end_date: string;
//   plan_status: string;
//   register_type: number;
//   video_plan: number;
//   is_active: number;
// };

export const uploadUpdateQuestions = async (
  materials: TUploadUpdateQuestions
): Promise<any> => {
  const bodyFormData = new FormData();
  bodyFormData.append('_method', 'PUT');
  bodyFormData.append('need_check_with_setting', `${materials?.need_check_with_setting}`);
  bodyFormData.append('file', materials?.file);
  bodyFormData.append('is_preview', `${materials?.is_preview}`);
  const response = await ApiService.post(
    '/import/questions',
    bodyFormData,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
    }
  );
  return response.data;
};
