import { Input } from '@/components';
import { Checkbox, Form } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { useEffect, useRef, useState } from 'react';

const QuestionItem = ({
  value,
  name,
  onChange,
}: {
  value?: any;
  name: string;
  onChange?: any;
}) => {
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [choiceContent, setChoiceContent] = useState<string>();

  useEffect(() => {
    if (value?.content !== choiceContent) {
      setChoiceContent(value?.content);
    }
    if (value?.is_correct !== isCorrect) {
      setIsCorrect(value?.is_correct);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value?.content, value?.is_correct]);

  const changeIsCorrect = (e: CheckboxChangeEvent) => {
    const is_correct = e.target.checked;
    setIsCorrect(is_correct);
    onChange?.({ ...value, is_correct });
  };

  const changeChoiceContent = (content: string) => {
    setChoiceContent(content);
    if (!content) {
      setIsCorrect(false);
      onChange?.({ ...value, content, is_correct: false });
      return;
    }
    onChange?.({ ...value, content });
  };
  return (
    <div className="col-span-1 flex flex-col gap-2">
      <div className="flex items-center gap-x-7 !font-bold">
        <p className="text-black-kj">{name}</p>
        <Checkbox
          checked={isCorrect}
          onChange={changeIsCorrect}
          disabled={!choiceContent || value?.disable}
        >
          正答
        </Checkbox>
      </div>
      <Input
        value={choiceContent}
        onChange={changeChoiceContent}
        classNames="w-full !rounded !text-sm !py-[9px]"
        size="large"
      />
    </div>
  );
};

export default QuestionItem;
