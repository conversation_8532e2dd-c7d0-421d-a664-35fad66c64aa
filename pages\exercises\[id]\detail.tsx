import { Input, InputNumber } from '../../../components/Input';
import { Button, EButtonStyleType } from '@/components';
import { Checkbox, Form, Radio } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import dynamic from 'next/dynamic';
import {
  EFolderUpload,
  EIsActive,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { Rule } from 'antd/lib/form';
import { useRouter } from 'next/router';
import { SelectBigCategory, SelectSubCategory } from '@/containers/select';
import { validationMessage } from '@/commons/messages-enum';
import validate from '@/utils/validate';
import FormUploadItem from '@/containers/exam/FormUploadItem';
import Tabs from '@/containers/tabs-excercise';
import { TAnswerParam } from '@/components/excercise/Pickup.types';
import { showNotification } from '@/utils/functions';
import { useDispatch, useSelector } from 'react-redux';
import { getDetailExerciseAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import LawItemForm from '../../../containers/exam/FormLawItem';
import FormDigitalItem from '../../../containers/exam/FormDigitalItem';
import { useHistory } from '@/utils/contexts';
import { resetExerciseDetailSlice } from '@/store/slices/detailExercise';

const EditExcercise = () => {
  const defaultAnswer = Array.from({ length: 10 }, (value, index: number) => {
    return {
      choice_no: index + 1,
      content: '',
      is_correct: false,
    };
  });
  const EditorBoxForm = dynamic(
    () => import('@/components/EditorBox/EditorBoxForm'),
    { ssr: false }
  );
  const [form] = Form.useForm();
  const router = useRouter();
  const big_category_id = Form.useWatch('category_id', form);
  const { checkMaxLengthEditor, checkEmptyStringAndImage } = validate;
  const [isFisrt, setFisrt] = useState<boolean>(true);
  const [answerParam, setAnswerParam] = useState<TAnswerParam[]>(defaultAnswer);
  const { hasWhiteSpace, checkMaxLength, checkImageValidate } = validate;
  const disable = true;
  const { id } = router.query;
  const dispatch = useDispatch();
  const { getDetailExerciseResponse } = useSelector(
    (state: RootState) => state.exerciseDetail
  );
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const fetchData = async () => {
    try {
      const params = { id: id?.toString() };
      dispatch(getDetailExerciseAction.request({ params }));
    } catch (error: any) {
      showNotification(ETypeNotification?.ERROR, error?.data?.message);
    }
  };

  useEffect(() => {
    if (!getDetailExerciseResponse) return;
    const paramData = getDetailExerciseResponse?.data;

    if (paramData) {
      const countLaws = 4 - paramData?.laws?.length;
      const law_ids = paramData?.laws.concat(Array(countLaws).fill(undefined));
      const digital_text_ids = paramData?.digital_texts.concat(
        Array(4 - paramData?.digital_texts?.length).fill(undefined)
      );

      form.setFieldsValue({
        question_no: paramData?.question_no,
        is_active: paramData?.is_active,
        category_id: paramData?.category?.id,
        sub_category_id: paramData?.sub_category?.id,
        question_year: paramData?.question_year,
        title: paramData?.title,
        content: paramData?.content,
        show_order: paramData?.show_order,
        explanation: paramData?.explanation,
        upload: paramData?.image,
        upload1:
          paramData?.exercise_question_explanation_images?.[0]
            ?.explanation_image,
        upload2:
          paramData?.exercise_question_explanation_images?.[1]
            ?.explanation_image,
        law_ids,
        digital_text_ids,
      });
      if (paramData?.exercise_choices?.length > 0) {
        const newAnswerParam = [...answerParam].map(
          (obj) =>
            paramData?.exercise_choices.find(
              (items: any) => items.choice_no === obj.choice_no
            ) || obj
        );
        const changeCorrect = [...newAnswerParam]?.map((item: any) => {
          return { ...item, is_correct: item?.is_correct === 1 };
        });
        setAnswerParam(changeCorrect);
      }
    }
  }, [getDetailExerciseResponse]);

  useEffect(() => {
    fetchData();
  }, []);

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-2">
        <Form.Item name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onBack = () => {
    dispatch(resetExerciseDetailSlice());
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/exercises');
  };

  const tabsDigital = () => {
    return (
      <div>
        <div className="flex flex-row gap-5 mt-6">
          <div className="h-[300px] flex flex-grow flex-col">
            <span className="text-textGray text-sm font-bold">解説本文</span>
            <Form.Item
              rules={
                [() => checkMaxLengthEditor('解説本文', 1, 65535)] as Rule[]
              }
              className={'!mb-0 !mt-2'}
              name={'explanation'}
            >
              <EditorBoxForm disable={disable} />
            </Form.Item>
          </div>
          <div>
            <span className="text-textGray text-sm font-bold">解説画像１</span>
            <Form.Item name={'upload1'} className={'!mt-2'}>
              <FormUploadItem
                disable
                accept={'.png,.jpeg,.jpg'}
                type={['png', 'jpeg', 'jpg']}
                folder={EFolderUpload.EXERCISE_QUESTIONS_EXPLANATIONS}
              />
            </Form.Item>
          </div>
          <div>
            <span className="text-textGray text-sm font-bold">解説画像2</span>
            <Form.Item name={'upload2'} className={'!mt-2'}>
              <FormUploadItem
                disable
                accept={'.png,.jpeg,.jpg'}
                type={['png', 'jpeg', 'jpg']}
                folder={EFolderUpload.EXERCISE_QUESTIONS_EXPLANATIONS}
              />
            </Form.Item>
          </div>
        </div>
        <div className="mt-10">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-5">
            <Form.List name={'law_ids'} initialValue={Array(4).fill(undefined)}>
              {(fields) => {
                return fields.map((field, idx) => (
                  <div
                    key={field.key}
                    className="col-span-1 flex flex-col gap-2 "
                  >
                    <p className="text-black-kj !font-bold">{`法令 ${
                      idx + 1
                    }`}</p>
                    <Form.Item name={[idx]}>
                      <LawItemForm className={'h-8'} disable={true} />
                    </Form.Item>
                  </div>
                ));
              }}
            </Form.List>
          </div>
        </div>
        <div className="mt-7">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-5">
            <Form.List
              name={'digital_text_ids'}
              initialValue={Array(4).fill(undefined)}
            >
              {(fields) => {
                return fields.map((field, idx) => (
                  <div
                    key={field.key}
                    className="col-span-1 flex flex-col gap-2 "
                  >
                    <p className="text-black-kj !font-bold">{`デジタルテキスト ${
                      idx + 1
                    }`}</p>
                    <Form.Item name={[idx]}>
                      <FormDigitalItem className={'h-8'} disable={true} />
                    </Form.Item>
                  </div>
                ));
              }}
            </Form.List>
          </div>
        </div>
      </div>
    );
  };

  const tabsAnswer = () => {
    return (
      <div>
        {answerParam &&
          answerParam?.map((item: TAnswerParam, index: number) => {
            return (
              <div key={item?.choice_no} className="mt-4">
                <div className="flex flex-row items-center gap-7">
                  <span className="font-bold text-sm">{`選択肢${
                    index + 1
                  }`}</span>
                  <Checkbox disabled={disable} checked={item?.is_correct}>
                    <span className="font-bold text-sm">正答</span>
                  </Checkbox>
                </div>
                <Input
                  disabled={true}
                  value={item?.content}
                  classNames={'!mt-2 input-disabled'}
                />
              </div>
            );
          })}
      </div>
    );
  };

  const directScreenEdit = () => {
    dispatch(resetExerciseDetailSlice());
    router.push(`/exercises/${id}/edit`);
  };

  const rulesQuestionNo = useMemo(() => {
    const checkLength = () => checkMaxLength('問題番号', 10);
    const checkSpave = () => hasWhiteSpace('問題番号');
    return [
      {
        required: true,
        message: validationMessage.required('問題番号'),
      },
      checkLength,
      checkSpave,
    ];
  }, [checkMaxLength, hasWhiteSpace]) as Array<Rule>;

  return (
    <div className="mx-12 mt-4">
      <span className="text-lg font-bold">練習問題詳細</span>
      <Form disabled={disable} form={form}>
        <div className="bg-alice-blue-light py-4 px-5 mt-3 flex flex-col">
          <div className="flex flex-row">
            <span className="text-base font-bold">問題ID</span>
            <span className="ml-3 text-primary text-base font-bold">{id}</span>
          </div>
          <div className="grid grid-cols-2 gap-x-5 gap-y-2 w-full mt-3">
            <div className="col-span-2 grid grid-cols-1 xl:grid-cols-2 gap-x-5">
              <div className="col-span-1 grid-cols-1 grid gap-2">
                <p className="text-black-kj font-bold">問題番号</p>
                <Form.Item name={'question_no'} rules={rulesQuestionNo}>
                  <InputNumber
                    classNames="!w-full !rounded !text-sm input-disabled"
                    hideControls
                  />
                </Form.Item>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
              <span className="text-textGray text-sm font-bold">大項目名</span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('大項目名'),
                  },
                ]}
                name={'category_id'}
              >
                <SelectBigCategory
                  placeholder={'選択してください'}
                  size={'middle'}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
              <span className="text-textGray text-sm font-bold">小項目名</span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('小項目名'),
                  },
                ]}
                name={'sub_category_id'}
              >
                <SelectSubCategory
                  size={'middle'}
                  category_id={big_category_id}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
              <span className="text-textGray text-sm font-bold">出題No.</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('出題No.'),
                    },
                    checkMaxLength('出題No', 4),
                    hasWhiteSpace('出題No'),
                  ] as Rule[]
                }
                name={'show_order'}
              >
                <InputNumber
                  hideControls
                  classNames={'!w-full input-disabled'}
                  min={0}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
              <span className="text-textGray text-sm font-bold">出題年度</span>
              <Form.Item name={'question_year'}>
                <Input maxLength={50} classNames={'input-disabled'} />
              </Form.Item>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-5 justify-between mt-3">
            <div>
              <span className="text-textGray text-sm font-bold">
                問題タイトル
              </span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('問題タイトル'),
                  },
                ]}
                name={'title'}
              >
                <Input classNames={'!mt-2 input-disabled'} />
              </Form.Item>
            </div>
          </div>
          <div className="flex flex-row gap-5 mt-3">
            <div className="h-[300px] flex flex-grow flex-col">
              <span className="text-textGray text-sm font-bold">問題本文</span>
              <Form.Item
                rules={
                  [
                    checkEmptyStringAndImage(isFisrt, '問題本文'),
                    () => checkMaxLengthEditor('問題本文', 1, 65535),
                  ] as Rule[]
                }
                className={'!mb-0 !mt-2'}
                name={'content'}
              >
                <EditorBoxForm
                  disable={disable}
                  onChange={() => setFisrt(false)}
                />
              </Form.Item>
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">画像</span>
              <Form.Item
                rules={[checkImageValidate('画像')] as Rule[]}
                className={'!mt-2'}
                name={'upload'}
              >
                <FormUploadItem
                  disable
                  accept={'.png,.jpeg,.jpg,.PNG'}
                  type={['png', 'jpeg', 'jpg', 'PNG']}
                  folder={EFolderUpload.EXERCISE_QUESTIONS}
                />
              </Form.Item>
            </div>
          </div>
          <div className="grid grid-cols-2 justify-between mt-3 gap-5">
            <div className="mt-3">
              <div className="flex flex-row">
                <span className="text-textGray text-sm font-bold">
                  有効・無効
                </span>
              </div>
              {ratioRender()}
            </div>
          </div>
        </div>
        <Tabs tabsDigital={tabsDigital} tabsAnswer={tabsAnswer} />
      </Form>
      <div className="flex flex-row justify-end mt-2.5 gap-4 mb-2.5">
        <Button
          onClick={onBack}
          size={'small'}
          styleType={EButtonStyleType.OUTLINE_PRIMARY}
          title={'戻る'}
        />
        {me?.data?.role_id !== ERoleId?.TEACHER && (
          <Button
            onClick={directScreenEdit}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'編集'}
          />
        )}
      </div>
    </div>
  );
};

export default EditExcercise;
