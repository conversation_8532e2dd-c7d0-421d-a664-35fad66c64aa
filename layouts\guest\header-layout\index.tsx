import { Button, CloseIcon, Image, MenubarIcon } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { TGetMeResponse, helpers } from '@/services/apis';
import { useRouter } from 'next/router';
import { useMemo } from 'react';
import logo from '@/components/Icon/LogoIcon.svg';

type THeaderProps = {
  account?: TGetMeResponse;
  showSidebar: boolean;
  handleChangeSidebar: () => void;
};

const Header = ({
  account,
  showSidebar,
  handleChangeSidebar,
}: THeaderProps) => {
  const router = useRouter();
  const iconMenu = useMemo(
    () => (showSidebar ? <CloseIcon /> : <MenubarIcon />),
    [showSidebar]
  );
  const handleLogout = () => {
    helpers.clearTokens();
    setTimeout(() => {
      window.location.href = '/login';
    }, 0);
  };
  return (
    <div className="flex justify-between items-center bg-primary">
      <div className="w-50 flex items-center border-b border-white border-opacity-10 h-14">
        <button
          className="w-9 flex items-center justify-center text-white"
          onClick={handleChangeSidebar}
        >
          {iconMenu}
        </button>
        <Image src={logo} classnames="w-36 h-8" alt="" />
      </div>
      <div className="py-3 px-1.5 flex-grow flex justify-end items-center bg-alice-blue gap-3.5 text-sm">
        <div className="flex gap-2 items-center h-8">
          <span className="text-black">{account?.data.username}</span>
        </div>
        <Button
          title="ログアウト"
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          className="rounded"
          onClick={handleLogout}
        />
      </div>
    </div>
  );
};

export default Header;
