import { TGetVideoResponse, getVideo } from '@/services/apis';
import { getVideoAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getVideoSaga(
  action: ActionType<typeof getVideoAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getVideo, materials);
    const getVideoResponse = response as TGetVideoResponse;
    yield put(getVideoAction.success(getVideoResponse));
    successCallback?.(getVideoResponse);
  } catch (err) {
    yield put(getVideoAction.failure(err));
    failedCallback?.(err);
  }
}
