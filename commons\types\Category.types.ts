import { EIsActive } from '../enums';

export interface TCategory {
  id?: string;
  name: string;
  is_active: EIsActive;
  show_order: number;
}

export interface TSubCategory {
  id?: string;
  name: string;
  is_active: EIsActive;
  show_order: number;
  category: TCategory;
}

export interface DataSubCategory {
  id?: string;
  name: string;
  is_active: EIsActive;
  show_order: number;
}

export type TOptionCategory = {
  id: number;
  name: string;
};

export type TOptionSub = {
  id: number;
  name: string;
  category_id: string;
};
