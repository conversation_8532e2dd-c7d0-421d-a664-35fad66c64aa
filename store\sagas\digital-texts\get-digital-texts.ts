import { TGetDigitalTextsResponse, getDigitalTexts } from '@/services/apis';
import { getDigitalTextsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getDigitalTextsSaga(
  action: ActionType<typeof getDigitalTextsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getDigitalTexts, materials);
    const getDigitalTextsResponse = response as TGetDigitalTextsResponse;
    yield put(getDigitalTextsAction.success(getDigitalTextsResponse));
    successCallback?.(getDigitalTextsResponse);
  } catch (err) {
    yield put(getDigitalTextsAction.failure(err));
    failedCallback?.(err);
  }
}
