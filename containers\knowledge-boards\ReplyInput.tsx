import { ERoleId } from '@/commons/enums';
import {
  ArrowDownIcon,
  Button,
  Close,
  CloseIcon,
  EButtonStyleType,
} from '@/components';
import { RootState } from '@/store/configureStore';
import validate from '@/utils/validate';
import { Form } from 'antd';
import autosize from 'autosize';
import clsx from 'clsx';
import { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';

const ReplyInput = ({
  repliedComment,
  handleReplyComment,
  handleRemoveReplyComment,
}: any) => {
  const [commentForm] = Form.useForm();
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  const onHandleReplyComment = async () => {
    await handleReplyComment(commentForm.getFieldValue('content'));
    commentForm.setFieldValue('content', '');
  };

  const autoSizeHeight = () => {
    const refTextArea = document.querySelector('textarea');
    if (refTextArea) autosize(refTextArea);
  };

  const onExitReplyMode = () => {
    commentForm.setFieldValue('content', '');
    handleRemoveReplyComment();
  };

  useEffect(() => {
    commentForm.setFieldValue('content', '');
  }, [repliedComment]);

  console.log(me?.data.role_id === ERoleId.TEACHER && !repliedComment);
  return (
    <Form form={commentForm} onFinish={onHandleReplyComment}>
      {/* Reply input */}
      <div className="flex justify-between items-end font-bold border rounded-md bg-white mt-4 py-4 px-5">
        <div
          className={clsx(
            'grow border rounded-md py-2 px-4 mr-4',
            me?.data.role_id === ERoleId.TEACHER &&
              !repliedComment &&
              'bg-gray-100'
          )}
        >
          {repliedComment && (
            <div className="flex justify-between items-center text-primary">
              <div>
                {`＞${
                  repliedComment.comment_no ? repliedComment.comment_no : '1'
                }. ${repliedComment.user.username} (ID:${
                  repliedComment.user.id
                })`}
              </div>
              <div
                className="hover:scale-125 cursor-pointer"
                onClick={onExitReplyMode}
              >
                <Close className="w-6 h-6" />
              </div>
            </div>
          )}
          <Form.Item
            name="content"
            className={clsx('w-full')}
            rules={[() => validate.checkRequiredContentComment('本文', 65535)]}
          >
            <textarea
              onFocus={autoSizeHeight}
              rows={2}
              className={clsx(
                'w-full resize-none box-border',
                'max-h-60 min-h-8',
                'focus:outline-none'
              )}
              disabled={me?.data.role_id === ERoleId.TEACHER && !repliedComment}
            />
          </Form.Item>
        </div>
        <Button
          htmlType={'submit'}
          size={'small'}
          styleType={EButtonStyleType.PRIMARY}
          title={'返信'}
          disabled={me?.data.role_id === ERoleId.TEACHER && !repliedComment}
        />
      </div>
    </Form>
  );
};

export default ReplyInput;
