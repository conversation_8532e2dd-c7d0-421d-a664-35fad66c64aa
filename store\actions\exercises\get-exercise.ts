import { TGetExerciseMaterials, TGetExerciseResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetExersiceAction {
  GET_EXERCISE = 'GET_EXERCISE',
  GET_EXERCISE_REQUEST = 'GET_EXERCISE_REQUEST',
  GET_EXERCISE_SUCCESS = 'GET_EXERCISE_SUCCESS',
  GET_EXERCISE_FAILED = 'GET_EXERCISE_FAILED',
}

// TYPES

export type TGetExerciseRequest = {
  type: EGetExersiceAction.GET_EXERCISE_REQUEST;
  payload: {
    materials: TGetExerciseMaterials;
    successCallback?: (response: TGetExerciseResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetExcerciseSuccess = {
  type: EGetExersiceAction.GET_EXERCISE_SUCCESS;
  payload: { response?: TGetExerciseResponse };
};

export type TGetExerciseFailed = {
  type: EGetExersiceAction.GET_EXERCISE_FAILED;
};

export const getExerciseAction = {
  request: createActionCreator(
    EGetExersiceAction.GET_EXERCISE_REQUEST,
    (resolve) =>
      (
        materials: TGetExerciseMaterials,
        successCallback?: (response: TGetExerciseResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetExerciseRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetExersiceAction.GET_EXERCISE_SUCCESS,
    (resolve) =>
      (response?: TGetExerciseResponse): TGetExcerciseSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetExersiceAction.GET_EXERCISE_FAILED,
    (resolve) =>
      (error: unknown): TGetExerciseFailed =>
        resolve({ error })
  ),
};
