import { CheckboxValueType } from 'antd/lib/checkbox/Group';

export type TFilterVideo = {
  category_id?: string;
  name?: string;
  is_actives?: Array<CheckboxValueType>;
};

export type TFilterVideoProps = {
  defaultFilter?: TFilterVideo;
  setFilter?: (value?: TFilterVideo) => void;
  clearFilter?: () => void;
};

export type TFormUploadVideoProps = {
  value?: any;
  onChange?: (value?: any) => void;
  accept?: string;
  type?: string[];
  maxSize?: number;
  onErrorType?: (error?: string) => void;
  onErrorSize?: (error?: string) => void;
};
