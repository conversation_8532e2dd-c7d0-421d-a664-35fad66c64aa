import { Input } from '@/components';
import { DatePicker, Form, FormInstance } from 'antd';
import locale from 'antd/lib/date-picker/locale/ja_JP';
import { validationMessage } from '@/commons/messages-enum';
import clsx from 'clsx';
import validate from '@/utils/validate';
import TextArea from 'antd/lib/input/TextArea';
import { useMemo, useState } from 'react';
import { Rule } from 'antd/lib/form';
import { SelectAvailableDiscount } from '../select';

const {
  checkMaxLength,
  checkMaxLengthString,
  validateStartDateByDay,
  checkLetterAndNumber,
  hasDot,
  validateStartEndDateByDay,
} = validate;

export type TVoucherFormProps = {
  isDisableDiscount?: boolean;
  formInstance: FormInstance;
  handleSubmit: (values: any) => void;
};

const VoucherForm = ({
  isDisableDiscount,
  formInstance,
  handleSubmit,
}: TVoucherFormProps) => {
  const [isFirstStartDate, setFirstStartDate] = useState<boolean>(true);
  const [isFirstEndDate, setFirstEndDate] = useState<boolean>(true);

  const onSubmit = (values: any) => {
    handleSubmit(values);
  };

  const rulesCouponName = useMemo(() => {
    const checkLength = () => checkMaxLength('クーポン名', 255);
    return [
      {
        required: true,
        message: validationMessage.required('クーポン名'),
      },
      checkLength,
    ];
  }, []) as Array<Rule>;

  const rulescouponCode = useMemo(() => {
    const checkLength = () => checkMaxLengthString('クーポンコード', 6, 12, 2);
    const letterAndNumber = () => checkLetterAndNumber('クーポンコード', 2);
    return [
      {
        required: true,
        message: validationMessage.required('クーポンコード'),
      },
      checkLength,
      letterAndNumber,
    ];
  }, []) as Array<Rule>;

  const rulesTrainingSchool = useMemo(() => {
    const checkLength = () => checkMaxLength('教習所名', 255);
    return [
      {
        required: true,
        message: validationMessage.required('教習所名'),
      },
      checkLength,
    ];
  }, []) as Array<Rule>;

  const rulesDiscount = useMemo(() => {
    const checkLength = () => hasDot('値引き', 255);
    return [
      {
        required: true,
        message: validationMessage.required('値引き'),
      },
      checkLength,
    ];
  }, []) as Array<Rule>;

  const rulesNote = useMemo(() => {
    const checkLength = () => checkMaxLength('備考', 65535);
    return [checkLength];
  }, []) as Array<Rule>;

  return (
    <Form
      className="grid grid-cols-2 gap-x-8 gap-y-0"
      form={formInstance}
      onFinish={onSubmit}
    >
      <div className="col-span-1 grid-cols-1 grid gap-2">
        <p className="text-black-kj font-bold">クーポン名</p>
        <Form.Item name={'name'} rules={rulesCouponName}>
          <Input
            classNames="w-full !rounded !text-sm"
            maxLength={255}
            size="middle"
          />
        </Form.Item>
      </div>
      <div className="col-span-1 grid-cols-1 grid gap-2">
        <p className="text-black-kj font-bold">クーポンコード</p>
        <Form.Item name={'code'} rules={rulescouponCode}>
          <Input
            classNames="w-full !rounded !text-sm"
            maxLength={12}
            size="middle"
          />
        </Form.Item>
      </div>
      <div className="col-span-1 grid-cols-1 grid gap-2">
        <p className="text-black-kj font-bold">有効期限</p>
        <div className="grid grid-cols-2 gap-5">
          <Form.Item
            name={'plan_start_date'}
            rules={
              [
                {
                  required: true,
                  message: validationMessage.required('有効期限'),
                },
                validateStartDateByDay(isFirstStartDate),
              ] as Rule[]
            }
          >
            <DatePicker
              inputReadOnly
              locale={locale}
              onChange={() => setFirstStartDate(false)}
              format={'YYYY/MM/DD'}
              placeholder=""
              size="middle"
              className="w-full"
            />
          </Form.Item>

          <Form.Item
            name={'plan_end_date'}
            rules={
              [
                {
                  required: true,
                  message: validationMessage.required('有効期限'),
                },
                validateStartEndDateByDay(isFirstEndDate, false),
              ] as Rule[]
            }
          >
            <DatePicker
              inputReadOnly
              onChange={() => setFirstEndDate(false)}
              locale={locale}
              format={'YYYY/MM/DD'}
              placeholder=""
              size="middle"
              className="w-full"
            />
          </Form.Item>
        </div>
      </div>
      <div className="col-span-1 grid-cols-1 grid gap-2">
        <p className="text-black-kj font-bold">教習所名</p>
        <Form.Item name={'training_school'} rules={rulesTrainingSchool}>
          <Input
            classNames="w-full !rounded !text-sm"
            maxLength={255}
            size="middle"
          />
        </Form.Item>
      </div>
      <div className="col-span-1 flex flex-col gap-2 items-start">
        <p className="text-black-kj font-bold">値引き</p>
        <Form.Item name={'discount'} className="w-full" rules={rulesDiscount}>
          <SelectAvailableDiscount
            disabled={isDisableDiscount}
            className="!w-full !rounded !text-sm discount"
            size="middle"
          />
        </Form.Item>
      </div>
      <div className="col-span-1 grid-cols-1 grid gap-2">
        <p className="text-black-kj font-bold">備考</p>
        <Form.Item name="note" className="w-full" rules={rulesNote}>
          <TextArea
            rows={8}
            className={clsx(
              'w-full resize-none !text-sm',
              'max-h-60 focus:outline-none'
            )}
          />
        </Form.Item>
      </div>
    </Form>
  );
};

export default VoucherForm;
