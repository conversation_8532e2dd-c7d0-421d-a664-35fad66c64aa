import { Button } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { useEffect, useState } from 'react';
import { TQuestionChoiceProps } from './questions.types';

const QuestionChoice = ({
  choice,
  disabled,
  onClick,
  isFail,
  isCorrect,
  isSelected,
  isCorrectNotChoice,
}: TQuestionChoiceProps) => {
  const [styleType, setStyleType] = useState<EButtonStyleType>(
    EButtonStyleType.OUTLINE_GREEN_BLUE
  );
  useEffect(() => {
    // if (isFail) {
    //   setStyleType(EButtonStyleType.LIGHT_GRAY);
    //   return;
    // }
    // if (isCorrectNotChoice) {
    //   setStyleType(EButtonStyleType.SECONDARY);
    //   return;
    // }
    if (isCorrect) {
      setStyleType(EButtonStyleType.BRIGHT_ORANGE);
      return;
    }
    if (isSelected) {
      setStyleType(EButtonStyleType.PRIMARY_VARIANT_2);
      return;
    }
    setStyleType(EButtonStyleType.OUTLINE_GREEN_BLUE);
  }, [isFail, isCorrect, isSelected, isCorrectNotChoice]);

  const onMouseOver = () => {
    if (disabled) {
      return;
    }
    setStyleType(EButtonStyleType.PRIMARY_VARIANT_2);
  };
  const onMouseOut = () => {
    if (disabled) {
      return;
    }
    if (isSelected) {
      setStyleType(EButtonStyleType.PRIMARY_VARIANT_2);
      return;
    }
    setStyleType(EButtonStyleType.OUTLINE_GREEN_BLUE);
  };
  return (
    <div
      className="!min-w-20 max-w-full"
      onMouseOver={onMouseOver}
      onMouseOut={onMouseOut}
    >
      <Button
        disabled={disabled}
        title={choice.content}
        styleType={styleType}
        onClick={onClick}
        className="px-2.5 !rounded-md max-w-full !font-bold !whitespace-normal"
      />
    </div>
  );
};

export default QuestionChoice;
