import { TGetVideoMaterials, TGetVideoResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetVideoAction {
  GET_VIDEO = 'GET_VIDEO',
  GET_VIDEO_REQUEST = 'GET_VIDEO_REQUEST',
  GET_VIDEO_SUCCESS = 'GET_VIDEO_SUCCESS',
  GET_VIDEO_FAILED = 'GET_VIDEO_FAILED',
}

// TYPES

export type TGetVideoRequest = {
  type: EGetVideoAction.GET_VIDEO_REQUEST;
  payload: {
    materials: TGetVideoMaterials;
    successCallback?: (response: TGetVideoResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetVideoSuccess = {
  type: EGetVideoAction.GET_VIDEO_SUCCESS;
  payload: { response?: TGetVideoResponse };
};

export type TGetVideoFailed = {
  type: EGetVideoAction.GET_VIDEO_FAILED;
};

export const getVideoAction = {
  request: createActionCreator(
    EGetVideoAction.GET_VIDEO_REQUEST,
    (resolve) =>
      (
        materials: TGetVideoMaterials,
        successCallback?: (response: TGetVideoResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetVideoRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetVideoAction.GET_VIDEO_SUCCESS,
    (resolve) =>
      (response?: TGetVideoResponse): TGetVideoSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetVideoAction.GET_VIDEO_FAILED,
    (resolve) =>
      (error: unknown): TGetVideoFailed =>
        resolve({ error })
  ),
};
