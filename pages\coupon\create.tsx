import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { Form } from 'antd';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { useRouter } from 'next/router';
import moment from 'moment-timezone';
import { Fragment, useState } from 'react';
import { showNotification } from '@/utils/functions';
import { EPlacement, ETypeNotification } from '@/commons/enums';
import { GetServerSideProps } from 'next';
import helpers from '@/services/helpers';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import NotificationErrors from '@/components/NotificationErrors';
import { CouponForm } from '@/containers';
import { postCoupon } from '@/services/apis';

const CreateVoucherPage = () => {
  moment.tz.setDefault('Asia/Tokyo');
  const [form] = Form.useForm();
  const router = useRouter();
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const onHandleOpenModal = async () => {
    try {
      await form.validateFields();
      setIsShowModal(true);
    } catch (error) {}
  };

  const onHandleCloseModal = () => {
    setIsShowModal(false);
  };

  const onHandleConfirmSubmit = async () => {
    const values = form.getFieldsValue();
    const { plan_end_date, plan_start_date, ...restValues } = values;
    const payload = {
      ...restValues,
      start_date: plan_start_date?.format('YYYY-MM-DD'),
      end_date: plan_end_date?.format('YYYY-MM-DD'),
    };
    try {
      await postCoupon({ payload });
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      backToList();
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };
  const backToList = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/coupon');
  };

  return (
    <Fragment>
      <ConfirmModal
        visible={isShowModal}
        onClose={onHandleCloseModal}
        onConfirm={onHandleConfirmSubmit}
        content={MODAL_MESSAGE.CONFIRM_CREATE}
      />
      <div className="mx-12 my-4 grid grid-cols-1 gap-3">
        <div className="text-lg font-bold">クーポン新規登録</div>
        <div className="bg-alice-blue-light py-4 px-5">
          <CouponForm
            formInstance={form}
            handleSubmit={onHandleConfirmSubmit}
          />
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={backToList}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'登録'}
            onClick={onHandleOpenModal}
          />
        </div>
      </div>
    </Fragment>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default CreateVoucherPage;
