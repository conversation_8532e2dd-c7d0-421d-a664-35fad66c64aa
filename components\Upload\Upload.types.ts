import { ReactNode } from 'react';

export type TUploadFileProps = {
  maxSize?: number;
  accept?: string;
  type?: string | Array<string>;
  maxCount?: number;
  setLoading?: (loading: boolean) => void;
  setFile?: (file: File) => void;
  onError?: (error?: string) => void;
  onErrorType?: (error?: string) => void;
  onErrorSize?: (error?: string) => void;
  children?: ReactNode;
  className?: string;
  disabled?: boolean;
};
