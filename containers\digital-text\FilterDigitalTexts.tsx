import { EIsActive } from '@/commons/enums';
import { Button, Input } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Select } from '@/components/Select';
import { Checkbox } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { useEffect, useState } from 'react';
import { SelectBigCategory, SelectSubCategory } from '../select';
import { TFilterDigital, TFilterDigitalTextsProps } from './digital.types';

const FilterDigitalTexts = ({
  defaultFilter,
  setFilter,
  clearFilter,
}: TFilterDigitalTextsProps) => {
  const [filterDigital, setFilterDigital] = useState<TFilterDigital>();

  useEffect(() => {
    setFilterDigital(defaultFilter);
  }, [defaultFilter]);

  const handleChangeBigProjectName = (category_id: string) => {
    setFilterDigital((current) => ({ ...current, category_id, sub_category_id: undefined }));
  };

  const handleChangeSmallProjectName = (sub_category_id: string) => {
    setFilterDigital((current) => ({ ...current, sub_category_id }));
  };

  const handleChangetextName = (name: string) => {
    setFilterDigital((current) => ({ ...current, name }));
  };

  const changeValid = (is_actives: Array<CheckboxValueType>) => {
    setFilterDigital((current) => ({ ...current, is_actives }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterDigital);
  };

  return (
    <div className="grid grid-cols-1 gap-5 w-full">
      <div className="grid grid-cols-3 gap-x-5">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">大項目名</p>
          <SelectBigCategory
            value={filterDigital?.category_id}
            onChange={handleChangeBigProjectName}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">小項目名</p>
          <SelectSubCategory
            category_id={filterDigital?.category_id}
            value={filterDigital?.sub_category_id}
            onChange={handleChangeSmallProjectName}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">テキスト名</p>
          <Input
            value={filterDigital?.name}
            size="large"
            classNames="w-full !rounded"
            onChange={handleChangetextName}
          />
        </div>
      </div>
      <div className="flex items-center justify-between">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
          <p className="text-black-kj">有効・無効</p>
          <div className="flex flex-row items-center mt-3">
            <Checkbox.Group
              onChange={changeValid}
              value={filterDigital?.is_actives}
            >
              <Checkbox value={EIsActive.ENABLE}>有効</Checkbox>
              <Checkbox value={EIsActive.DISABLE}>無効</Checkbox>
            </Checkbox.Group>
          </div>
        </div>
        <div className="flex items-end flex-none gap-2.5">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="クリア"
            className="!font-normal"
            onClick={clearFilter}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="検索"
            className="!font-normal"
            onClick={handleUpdateFilter}
          />
        </div>
      </div>
    </div>
  );
};

export default FilterDigitalTexts;
