import { TLaw } from '@/commons/types';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

export type TFilterOrdinace = {
  category_id?: string;
  name?: string;
  is_actives?: Array<CheckboxValueType>;
};

export type TFilterOrdinaceProps = {
  defaultFilter?: TFilterOrdinace;
  setFilter?: (value?: TFilterOrdinace) => void;
  clearFilter?: () => void;
};

export type TPreviewLawModalProps = {
  showModal: boolean;
  handleCloseModal: () => void;
  law?: TLaw;
};
