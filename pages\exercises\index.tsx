import { ListExcercise } from '@/containers';
import { TExerciseFilter } from '@/containers/excercise/excercise.type';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';

const ExcercireList = ({ filter }: { filter: TExerciseFilter }) => {
  return (
    <div className="mx-12 my-4">
      <div className="flex justify-between items-center">
        <h1 className="text-lg font-bold text-black-kj">練習問題一覧</h1>
      </div>
      <div className="mt-3 w-full">
        <ListExcercise query={filter} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  try {
    const is_actives =
      query?.is_actives
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const category_id = query?.category_id
      ? Number(query?.category_id)
      : undefined;
    const sub_category_id = query?.sub_category_id
      ? Number(query?.sub_category_id)
      : undefined;
    const filter = removeEmpty({
      ...query,
      is_actives,
      category_id,
      sub_category_id,
    });
    return {
      props: { filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};
export default ExcercireList;
