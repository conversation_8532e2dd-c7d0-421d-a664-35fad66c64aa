import { ERoleId } from '@/commons/enums';
import { ListVideos } from '@/containers';
import { TFilterVideo } from '@/containers/video/video.types';
import helpers from '@/services/helpers';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';

const VideoListPage = ({ query }: { query: TFilterVideo }) => {
  return (
    <div className="mx-12 my-4">
      <h1 className="text-lg font-bold text-black-kj">動画一覧</h1>
      <div className="mt-3 w-full">
        <ListVideos query={query} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    const { id, ...restQuery } = query;
    const is_actives =
      restQuery?.is_actives
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const category_id = restQuery?.category_id
      ? Number(restQuery?.category_id)
      : null;
    const filter = removeEmpty({
      ...restQuery,
      is_actives,
      category_id,
    });
    return {
      props: { query: filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default VideoListPage;
