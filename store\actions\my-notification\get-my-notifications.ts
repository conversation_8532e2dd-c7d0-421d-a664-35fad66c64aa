import {
  TGetMyNotificationsMaterials,
  TGetMyNotificationsResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

export enum EGetMyNotificationsAction {
  GET_MY_NOTIFICATIONS = 'GET_MY_NOTIFICATIONS',
  GET_MY_NOTIFICATIONS_REQUEST = 'GET_MY_NOTIFICATIONS_REQUEST',
  GET_MY_NOTIFICATIONS_SUCCESS = 'GET_MY_NOTIFICATIONS_SUCCESS',
  GET_MY_NOTIFICATIONS_FAILED = 'GET_MY_NOTIFICATIONS_FAILED',
}

export type TGetMyNotificationsRequest = {
  type: EGetMyNotificationsAction.GET_MY_NOTIFICATIONS_REQUEST;
  payload: {
    materials: TGetMyNotificationsMaterials;
    successCallback?: (response: TGetMyNotificationsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetMyNotificationsSuccess = {
  type: EGetMyNotificationsAction.GET_MY_NOTIFICATIONS_SUCCESS;
  payload: { response?: TGetMyNotificationsResponse };
};

export type TGetMyNotificationsFailed = {
  type: EGetMyNotificationsAction.GET_MY_NOTIFICATIONS_FAILED;
};

export const getMyNotificationsAction = {
  request: createActionCreator(
    EGetMyNotificationsAction.GET_MY_NOTIFICATIONS_REQUEST,
    (resolve) =>
      (
        materials: TGetMyNotificationsMaterials,
        successCallback?: (response: TGetMyNotificationsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetMyNotificationsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetMyNotificationsAction.GET_MY_NOTIFICATIONS_SUCCESS,
    (resolve) =>
      (response?: TGetMyNotificationsResponse): TGetMyNotificationsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetMyNotificationsAction.GET_MY_NOTIFICATIONS_FAILED,
    (resolve) =>
      (error: unknown): TGetMyNotificationsFailed =>
        resolve({ error })
  ),
};
