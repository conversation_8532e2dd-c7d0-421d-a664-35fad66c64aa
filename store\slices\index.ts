import { combineReducers } from 'redux';
import { accountReducer as account } from './account';
import { notificationReducer as notification } from './notification';
import { categoryReducer as category } from './category';
import { subCategoryReducer as subCategory } from './sub-category';
import { lawReducer as law } from './laws';
import { digitalTextsReducer as digitalTexts } from './digital-texts';
import { myNotificationReducer as myNotification } from './my-notification';
import { authReducer as auth } from './auth';
import { mockTestsReducer as mockTest } from './mock-tests';
import { settingReducer as setting } from './settings';
import { userReducer as user } from './users';
import { excerciseReducer as exercise } from './exercises';
import { adminReducer as admin } from './admins';
import { questionsReducer as question } from './questions';
import { practiceQuestionsReducer as practiceQuestion } from './practice-questions';
import { videoReducer as video } from './videos';
import { excerciseDetailReducer as exerciseDetail } from './detailExercise';
import { knowledgeBoardsReducer as knowledgeBoard } from './knowledge-board';

import {
  errorReducer as error,
  historyReducer as history,
  loadingReducer as loading,
  successReducer as success,
} from './status';

const rootReducer = combineReducers({
  auth,
  account,
  notification,
  category,
  law,
  error,
  loading,
  success,
  subCategory,
  digitalTexts,
  myNotification,
  mockTest,
  setting,
  user,
  exercise,
  admin,
  question,
  video,
  exerciseDetail,
  practiceQuestion,
  history,
  knowledgeBoard,
});

export { rootReducer };
