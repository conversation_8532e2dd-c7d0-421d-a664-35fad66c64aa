import { browserConfig } from '@/commons/constants';
import ApiService from '@/services/apis';

export type TGetCommonMaintainMaterials = {};

export type TGetCommonMaintainResponse = {
  data: { content: string };
};

export const getCommonMaintain = async (
  materials: TGetCommonMaintainMaterials
): Promise<TGetCommonMaintainResponse> => {
  const url = browserConfig.apiServiceBaseUrl.replace('/admin', '');
  const response = await ApiService.get(`${url}/common/maintain`);
  return response.data;
};
