import {
  EIsActive,
  EPlacement,
  EQuestionForm,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { TMockTest, TQuestion } from '@/commons/types';
import {
  Button,
  ColumnType,
  ConfirmModal,
  EButtonStyleType,
  EditIcon,
  InputNumber,
  Pagination,
  PresentationIcon,
  PreviewIcon,
  Table,
} from '@/components';
import {
  getMockTestsAction,
  getQuestionsAction,
  postUpdateQuestionsAction,
} from '@/store/actions';
import { RootState } from '@/store/configureStore';
import Link from 'next/link';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FilterMockTests from './FilterMockTests';
import { TMockTestsFilter } from './mock-tests.types';
import Router, { useRouter } from 'next/router';
import { getExportExamsQuestions, postUpdateQuestions } from '@/services/apis';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import clsx from 'clsx';
import moment from 'moment';
import {
  resetGetQuestionsResponse,
  resetQuestionsSlice,
} from '@/store/slices/questions';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import { PreviewQuestionModal } from '../questions';

const MAX_SHOW_ORDER = 9999999999;
const MIN_SHOW_ORDER = 1;

const ListMockTests = ({
  exam_id,
  query,
}: {
  exam_id: number;
  query: TMockTestsFilter;
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [filter, setFilter] = useState<TMockTestsFilter>();
  const [dataSource, setDataSource] = useState<Array<TQuestion>>([]);
  const [isShowModalSubmit, setIsShowModalSubmit] = useState<boolean>(false);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [questionPreivew, setQuestionPreivew] = useState<TQuestion>();

  const { getQuestionsResponse: questions } = useSelector(
    (state: RootState) => state.question
  );
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  const { data, meta } = useMemo(
    () => questions || { data: [], meta: undefined },
    [questions]
  );

  useEffect(() => {
    setDataSource(data);
  }, [data]);

  useEffect(() => {
    return () => {
      dispatch(resetGetQuestionsResponse());
    };
  }, [dispatch]);

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
      types: [EQuestionForm.SINGLE, EQuestionForm.MULTIPLE],
    };
    setFilter(defaultFilter);
  };

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const handleUpdateQuestionNo = (mock: TQuestion) => {
    setDataSource((current) =>
      current.map((item) => {
        if (item.id === mock.id) {
          return mock;
        }
        return item;
      })
    );
  };

  const getActions = useCallback(
    (element: TQuestion) => {
      const handleClickRedirect = () => {
        dispatch(resetQuestionsSlice());
        dispatch(setCallBackUrl(router.asPath));
        setTimeout(
          () => router.push(`/exams/${exam_id}/questions/${element.id}/view`),
          0
        );
      };

      const handlePreviewQuestion = () => {
        setOpenModal(true);
        setQuestionPreivew(element);
      };

      return (
        <div className="flex items-center justify-center gap-5">
          <div className="cursor-pointer" onClick={handlePreviewQuestion}>
            <PreviewIcon />
          </div>
          <button onClick={handleClickRedirect}>
            <PresentationIcon />
          </button>
        </div>
      );
    },
    [dispatch, exam_id, router]
  );

  const checkErrorQuestionNo = useCallback(
    (question: TQuestion) => {
      if (question.is_active === EIsActive.DISABLE) {
        return false;
      }
      if (!question.show_order) {
        return true;
      }

      if (
        question.show_order < MIN_SHOW_ORDER ||
        question.show_order > MAX_SHOW_ORDER
      ) {
        return true;
      }
      const duplicates = dataSource.filter(
        ({ id, show_order }) =>
          id !== question.id && show_order === question.show_order
      );
      return !!duplicates.length;
    },
    [dataSource]
  );

  const isEdited = useCallback(
    (question: TQuestion) => {
      const question_edited = dataSource.filter(
        (item, idx) =>
          item.show_order !== data?.[idx]?.show_order &&
          !(data?.[idx]?.show_order === null && isNaN(item.show_order))
      );

      const include = question_edited.filter(
        ({ id }) => id === question.id
      );
      return !!include.length;
    },
    [data, dataSource]
  );

  const columns = useMemo(
    () =>
      [
        {
          title: '問題ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => element?.id,
        },
        {
          title: '出題No.',
          dataIndex: 'show_order',
          key: 'show_order',
          render: (element) => {
            const updateQuestionNo = (show_order: string) => {
              if (element) {
                handleUpdateQuestionNo({
                  ...element,
                  show_order: Number.parseFloat(show_order),
                });
              }
            };

            const checkColor = (element: TQuestion) => {
              if (checkErrorQuestionNo(element)) return '!border-danger';
              if (isEdited(element)) return '!border-green';
              return '';
            };
            return (
              <InputNumber
                key={element?.id}
                defaultValue={element?.show_order}
                onChange={updateQuestionNo}
                size="middle"
                hideControls
                classNames={clsx(
                  '!w-16 !text-xs !rounded',
                  element && checkColor(element)
                )}
                disabled={
                  me?.data.role_id !== ERoleId.ADMIN ||
                  element?.is_active !== EIsActive.ENABLE
                }
                stringMode
              />
            );
          },
        },
        {
          title: '問題番号',
          dataIndex: 'question_no',
          key: 'question_no',
          render: (element) => {
            return <div className="w-12 break-all">{element?.question_no}</div>;
          },
        },
        {
          title: '出題年度',
          dataIndex: 'year',
          key: 'year',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-lg break-all">
              {element?.question_year}
            </div>
          ),
        },
        {
          title: '大項目名',
          dataIndex: 'category.name',
          key: 'category.name',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-lg break-all">
              {element?.category.name}
            </div>
          ),
        },
        {
          title: 'タイトル',
          dataIndex: 'name',
          key: 'name',
          render: (element) => (
            <div className="max-w-[175px] 2xl:max-w-lg break-all">
              {element?.title}
            </div>
          ),
        },
        {
          title: '配点',
          dataIndex: 'allocation_points',
          key: 'allocation_points',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-lg break-all">
              {element?.allocation_point}
            </div>
          ),
        },
        {
          title: '出題形式',
          dataIndex: 'question_form',
          key: 'question_form',
          render: (element) => (
            <div className="max-w-50 2xl:max-w-lg break-all">
              {element?.type === EQuestionForm.SINGLE ? '単一' : '複数'}
            </div>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'invalid',
          key: 'invalid',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TQuestion>>,
    [checkErrorQuestionNo, getActions, me?.data.role_id]
  );

  const fetchData = useCallback(
    (page: number, filter?: TMockTestsFilter) => {
      const {
        is_actives: filterIsActives,
        types: filterTypes,
        question_year: questionYear,
        title: titleFilter,
        content: contentFilter,
        ...restFilter
      } = filter || {};

      const is_actives = filterIsActives as Array<number>;
      const types = filterTypes as Array<number>;
      const question_year = questionYear ? questionYear : undefined;
      const title = titleFilter ? titleFilter : undefined;
      const content = contentFilter ? contentFilter : undefined;
      const paths = { exam_id };
      const params = {
        page,
        is_actives,
        types,
        content,
        title,
        question_year,
        ...restFilter,
      };
      dispatch(getQuestionsAction.request({ params, paths }));
    },
    [dispatch, exam_id]
  );

  useEffect(() => {
    if (filter) {
      const questionFilter = { ...filter, question_id: filter.id };
      const query = removeEmpty(questionFilter) as ParsedUrlQueryInput;
      router.push({ query: { ...query, id: exam_id } }, undefined, {
        shallow: true,
      });
      fetchData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleRenewQuestion = () => {
    const questions = dataSource
      .filter((question, idx) => question.show_order !== data?.[idx].show_order)
      .map(({ id = 0, show_order }) => ({ question_id: id, show_order }));
    const paths = { exam_id };
    const payload = { questions };
    dispatch(
      postUpdateQuestionsAction.request({ paths, payload }, () => {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        fetchData(meta?.current_page || 1, filter);
        onCloseModalSubmit();
      })
    );
  };

  const handleClickAddQuestion = () => {
    dispatch(setCallBackUrl(router.asPath));
    setTimeout(() => {
      router.push(`/exams/${exam_id}/questions/create`);
    }, 0);
  };

  const handleClickSubmit = () => {
    handleRenewQuestion();
  };
  const onCloseModalSubmit = () => {
    setIsShowModalSubmit(false);
  };
  const onShowModalSubmit = () => {
    let errorMinMax = false;
    // check min max show order
    for (let idx = 0; idx < dataSource.length; idx++) {
      if (
        dataSource[idx].show_order < MIN_SHOW_ORDER ||
        dataSource[idx].show_order > MAX_SHOW_ORDER
      ) {
        errorMinMax = true;
        break;
      }
    }

    if (errorMinMax) {
      showNotification(
        ETypeNotification.ERROR,
        validationMessage.betweenNumber(
          '出題No',
          MIN_SHOW_ORDER,
          MAX_SHOW_ORDER
        )
      );
      return;
    }

    const empty = dataSource
      .map(({ show_order }) => show_order)
      .filter((item) => item);
    const unique = empty.filter(
      (value, index, array) => array.indexOf(value) === index
    );
    if (empty.length !== dataSource.length) {
      showNotification(
        ETypeNotification.ERROR,
        validationMessage.betweenNumber(
          '出題No.',
          MIN_SHOW_ORDER,
          MAX_SHOW_ORDER
        )
      );
      return;
    }
    if (unique.length !== dataSource.length) {
      showNotification(
        ETypeNotification.ERROR,
        validationMessage.unique('出題No')
      );
      return;
    }
    setIsShowModalSubmit(true);
  };

  const handleExportData = async () => {
    const category_id = filter?.category_id
      ? Number(filter?.category_id)
      : undefined;
    const show_order = filter?.show_order || undefined;
    const content = filter?.content?.trim() || undefined;
    const title = filter?.title?.trim() || undefined;
    const id = filter?.id || undefined;
    const question_year = filter?.question_year || undefined;
    const types = filter?.types as Array<number>;
    const is_actives = filter?.is_actives as Array<number>;
    const params = {
      category_id,
      show_order,
      content,
      title,
      id,
      question_year,
      types,
      is_actives,
    };

    try {
      const response = await getExportExamsQuestions({ params, exam_id });
      const fileName = `Kojiro_unkan_questions_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  return (
    <Fragment>
      <PreviewQuestionModal
        showModal={openModal}
        handleCloseModal={() => {
          setOpenModal(false);
          setQuestionPreivew(undefined);
        }}
        quesitonContent={questionPreivew}
      />
      <ConfirmModal
        visible={isShowModalSubmit}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
        onClose={onCloseModalSubmit}
        onConfirm={handleClickSubmit}
      />
      <div className="grid grid-cols-1 gap-2.5">
        <div className="flex flex-col gap-y-4">
          <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
            <FilterMockTests
              defaultFilter={filter}
              setFilter={setFilter}
              clearFilter={setDefaultFilter}
            />
          </div>
          {me?.data.role_id === ERoleId.ADMIN && (
            <div className="self-end flex items-center gap-2">
              <Button
                onClick={handleExportData}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="出力"
                className="!font-normal"
              />
              {/* <Button
                onClick={goToUploadPage}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="アップロード"
                className="!font-normal"
              /> */}
              <Button
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="新規登録"
                className="!font-normal"
                onClick={handleClickAddQuestion}
              />
              <Button
                onClick={onShowModalSubmit}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="更新"
                className="!font-normal"
              />
            </div>
          )}
        </div>
        <div className="flex flex-col items-center gap-y-4 justify-between">
          <div className="w-full flex justify-between items-center">
            <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
              meta?.total || 0
            }件`}</h2>
            {meta?.total !== 0 && (
              <Pagination
                current={meta?.current_page || 0}
                total={meta?.last_page || 0}
                onChange={handleChangePage}
              />
            )}
          </div>
          <Table<TQuestion>
            columns={columns}
            dataSource={questions ? dataSource : undefined}
          />
          {meta?.total !== 0 && (
            <Pagination
              current={meta?.current_page || 0}
              total={meta?.last_page || 0}
              onChange={handleChangePage}
            />
          )}
        </div>
      </div>
    </Fragment>
  );
};

export default ListMockTests;
