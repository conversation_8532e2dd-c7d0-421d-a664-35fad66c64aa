import {
  TPutExamSettingMaterials,
  TPutExamSettingResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPutExamSettingAction {
  PUT_EXAM_SETTING = 'PUT_EXAM_SETTING',
  PUT_EXAM_SETTING_REQUEST = 'PUT_EXAM_SETTING_REQUEST',
  PUT_EXAM_SETTING_SUCCESS = 'PUT_EXAM_SETTING_SUCCESS',
  PUT_EXAM_SETTING_FAILED = 'PUT_EXAM_SETTING_FAILED',
}

// TYPES

export type TPutExamSettingRequest = {
  type: EPutExamSettingAction.PUT_EXAM_SETTING_REQUEST;
  payload: {
    materials: TPutExamSettingMaterials;
    successCallback?: (response: TPutExamSettingResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPutExamSettingSuccess = {
  type: EPutExamSettingAction.PUT_EXAM_SETTING_SUCCESS;
  payload: { response?: TPutExamSettingResponse };
};

export type TPutExamSettingFailed = {
  type: EPutExamSettingAction.PUT_EXAM_SETTING_FAILED;
};

export const putExamSettingAction = {
  request: createActionCreator(
    EPutExamSettingAction.PUT_EXAM_SETTING_REQUEST,
    (resolve) =>
      (
        materials: TPutExamSettingMaterials,
        successCallback?: (response: TPutExamSettingResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TPutExamSettingRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPutExamSettingAction.PUT_EXAM_SETTING_SUCCESS,
    (resolve) =>
      (response?: TPutExamSettingResponse): TPutExamSettingSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPutExamSettingAction.PUT_EXAM_SETTING_FAILED,
    (resolve) =>
      (error: unknown): TPutExamSettingFailed =>
        resolve({ error })
  ),
};
