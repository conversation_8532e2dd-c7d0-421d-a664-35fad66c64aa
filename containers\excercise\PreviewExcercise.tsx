import { EIsActive } from '@/commons/enums';
import { ColumnType, Pagination, Table } from '@/components';
import { useMemo } from 'react';
import {
  TUploadCreateExerciseQuestionsResponse,
  TUploadUpdateExerciseQuestionsResponse,
} from '@/services/apis';

type PreviewProps = {
  data: Array<
    | TUploadCreateExerciseQuestionsResponse
    | TUploadUpdateExerciseQuestionsResponse
  >;
  pagination: any;
  handleChangePage: (page: number) => void;
  previewType?: number;
};

const PreviewExercise = ({
  data,
  pagination,
  handleChangePage,
  previewType,
}: PreviewProps) => {
  const columns = useMemo(
    () =>
      [
        {
          title: '行数',
          dataIndex: 'line_no',
          key: 'line_no',
          render: (element) => element?.line_no,
        },
        {
          title: '問題番号',
          dataIndex: 'question_no',
          key: 'question_no',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 truncate">
              {element?.question_no}
            </div>
          ),
        },
        {
          title: '大項目名',
          dataIndex: 'category',
          key: 'category',
          render: (element) => (
            <div className="min-w-25 max-w-25 2xl:max-w-50">
              {`(${element.category.id}) ${element.category.name}`}
            </div>
          ),
        },
        {
          title: '小項目名',
          dataIndex: 'sub_category',
          key: 'sub_category',
          render: (element) => (
            <div className="min-w-25 max-w-25 2xl:max-w-50">
              {`(${element.sub_category.id}) ${element.sub_category.name}`}
            </div>
          ),
        },
        {
          title: '出題No.',
          dataIndex: 'show_order',
          key: 'show_order',
          render: (element) => element?.show_order,
        },
        {
          title: '出題年度',
          dataIndex: 'question_year',
          key: 'question_year',
          render: (element) => element?.question_year,
        },
        {
          title: 'タイトル',
          dataIndex: 'title',
          key: 'title',
          render: (element) => (
            <div className="min-w-25 max-w-25 2xl:max-w-50 break-all">
              {element?.title}
            </div>
          ),
        },
        {
          title: '問題本文',
          dataIndex: 'content',
          key: 'content',
          render: (element) => (
            <div
              className="min-w-75 max-w-75 break-all !leading-normal html-render"
              dangerouslySetInnerHTML={{ __html: element?.content }}
            ></div>
          ),
        },
        {
          title: '問題画像',
          dataIndex: 'image',
          key: 'image',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 break-all">
              {element?.image}
            </div>
          ),
        },
        {
          title: '解説本文',
          dataIndex: 'explanation',
          key: 'explanation',
          render: (element) => (
            <div
              className="min-w-75 max-w-75 !leading-normal html-render"
              dangerouslySetInnerHTML={{ __html: element?.explanation }}
            ></div>
          ),
        },
        {
          title: '解説画像',
          dataIndex: 'explanation_images',
          key: 'explanation_images',
          render: (element) => (
            <>
              {element?.explanation_images?.map((image: any, index: any) => (
                <div
                  key={`explain_image_${index}`}
                  className="max-w-25 2xl:max-w-50 truncate"
                >
                  {image.explanation_image}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '設問',
          dataIndex: 'exercise_choices',
          key: 'exercise_choices',
          render: (element) => (
            <>
              {element?.exercise_choices?.map((choice: any, index: any) => (
                <div
                  key={`choice_${index}`}
                  className="max-w-25 2xl:max-w-50 truncate"
                >
                  {choice?.is_correct === 1 ? '○' : '×'} : {choice?.content}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '法令名',
          dataIndex: 'laws',
          key: 'laws',
          render: (element) => (
            <>
              {element?.laws?.map((law: any, index: any) => (
                <div
                  key={`law_${index}`}
                  className="min-w-25 max-w-25 xl:max-w-50 break-all"
                >
                  {`(${law.id}) ${law.name}`}
                </div>
              ))}
            </>
          ),
        },
        {
          title: 'テキスト名',
          dataIndex: 'digital_texts',
          key: 'digital_texts',
          render: (element) => (
            <>
              {element?.digital_texts?.map((digital_text: any, index: any) => (
                <div
                  key={`digital_${index}`}
                  className="max-w-25 min-w-25 2xl:max-w-50 break-all"
                >
                  {`(${digital_text.id}) ${digital_text.name}`}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'is_active',
          key: 'is_active',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
      ] as Array<
        ColumnType<
          | TUploadCreateExerciseQuestionsResponse
          | TUploadUpdateExerciseQuestionsResponse
        >
      >,
    []
  );

  return (
    <div className="grid grid-cols-1 mt-8 bg-alice-blue py-8 px-5">
      <div className="flex flex-col items-center gap-y-4 justify-between -mt-4">
        <div className="w-full text-left">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">
            {previewType && previewType === 1 ? '登録' : '更新'}
            <span className="text-primary ml-5">
              {pagination?.total ? pagination?.total : 0}件
            </span>
          </h2>
        </div>
        <Table<
          | TUploadCreateExerciseQuestionsResponse
          | TUploadUpdateExerciseQuestionsResponse
        >
          columns={columns}
          dataSource={data}
          isPreview={true}
        />
        {data.length !== 0 && (
          <div className="w-full flex justify-center relative">
            <Pagination
              current={pagination.current_page || 0}
              total={pagination?.last_page || 0}
              onChange={handleChangePage}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PreviewExercise;
