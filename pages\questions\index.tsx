import { ListQuestions } from '@/containers';
import { TQuestionsFilter } from '@/containers/questions/questions.types';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';

const ExamsListPage = ({ query }: { query: TQuestionsFilter }) => {
  return (
    <div className="mx-12 my-4">
      <div className="flex justify-between items-center">
        <h1 className="text-lg font-bold text-black-kj">実践問題一覧</h1>
      </div>
      <div className="mt-3 w-full">
        <ListQuestions query={query} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  try {
    const is_actives =
      query?.is_actives
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const types =
      query?.types
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const category_id = query?.category_id ? Number(query?.category_id) : null;

    const question_id = query?.id ? Number(query?.id) : null;
    const filter = removeEmpty({
      ...query,
      is_actives,
      types,
      category_id,
      id: question_id,
    });

    return {
      props: { query: filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};
export default ExamsListPage;
