import {
  TGetCouponMaterials,
  TGetCouponResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetCouponAction {
  GET_COUPON = 'GET_COUPON',
  GET_COUPON_REQUEST = 'GET_COUPON_REQUEST',
  GET_COUPON_SUCCESS = 'GET_COUPON_SUCCESS',
  GET_COUPON_FAILED = 'GET_COUPON_FAILED',
}

// TYPES

export type TGetCouponRequest = {
  type: EGetCouponAction.GET_COUPON_REQUEST;
  payload: {
    materials: any;
    successCallback?: (response: any) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetCouponSuccess = {
  type: EGetCouponAction.GET_COUPON_SUCCESS;
  payload: { response?: any };
};

export type TGetCouponFailed = {
  type: EGetCouponAction.GET_COUPON_FAILED;
};

export const getCouponAction = {
  request: createActionCreator(
    EGetCouponAction.GET_COUPON_REQUEST,
    (resolve) =>
      (
        materials: TGetCouponMaterials,
        successCallback?: (response: TGetCouponResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetCouponRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetCouponAction.GET_COUPON_SUCCESS,
    (resolve) =>
      (response?: any): TGetCouponSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetCouponAction.GET_COUPON_FAILED,
    (resolve) =>
      (error: unknown): TGetCouponFailed =>
        resolve({ error })
  ),
};
