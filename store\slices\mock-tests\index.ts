import {
  TGetExamResponse,
  TGetExamsResponse,
  TGetMockTestsResponse,
} from '@/services/apis';
import {
  EGetExamAction,
  EGetExamsAction,
  EGetMockTestsAction,
  TGetExamSuccess,
  TGetExamsSuccess,
  TGetMockTestsSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TMockTestsState {
  getMockTestsResponse?: TGetMockTestsResponse;
  getExamResponse?: TGetExamResponse;
  getExamsResponse?: TGetExamsResponse;
}

const initialState: TMockTestsState = {
  getMockTestsResponse: undefined,
  getExamsResponse: undefined,
  getExamResponse: undefined,
};

const mockTestsSlice = createSlice({
  name: 'mockTestss-slice',
  initialState,
  reducers: {
    resetGetExamsResponse: (state) => {
      return { ...state, getExamsResponse: undefined };
    },
  },
  extraReducers: {
    [EGetMockTestsAction.GET_MOCK_TESTS_SUCCESS]: (
      state: TMockTestsState,
      action: TGetMockTestsSuccess
    ): TMockTestsState => ({
      ...state,
      getMockTestsResponse: action.payload?.response,
    }),
    [EGetExamsAction.GET_EXAMS_SUCCESS]: (
      state: TMockTestsState,
      action: TGetExamsSuccess
    ): TMockTestsState => ({
      ...state,
      getExamsResponse: action.payload?.response,
    }),
    [EGetExamAction.GET_EXAM_SUCCESS]: (
      state: TMockTestsState,
      action: TGetExamSuccess
    ): TMockTestsState => ({
      ...state,
      getExamResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: mockTestsReducer,
  actions: { resetGetExamsResponse },
} = mockTestsSlice;

export { mockTestsReducer, resetGetExamsResponse };
