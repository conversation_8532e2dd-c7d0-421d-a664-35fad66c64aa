import { TRoute } from '@/commons/types';
import clsx from 'clsx';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback } from 'react';

const SiderBar = ({ routes }: { routes: Array<TRoute> }) => {
  const router = useRouter();

  const getClassActice = useCallback(
    (link: string) => {
      const path = router.asPath || '';
      if (path === link && link === '/') {
        return 'text-white';
      }
      if (link !== '/' && path.indexOf(link) === 0) {
        return 'text-white';
      }
      return 'text-cotton';
    },
    [router]
  );

  return (
    <div className="flex justify-start flex-col gap-2.5">
      {routes.map(({ title, link }, idx) => (
        <Link href={link} key={idx}>
          <span className={clsx('font-bold', getClassActice(link))}>
            {title}
          </span>
        </Link>
      ))}
    </div>
  );
};

export default SiderBar;
