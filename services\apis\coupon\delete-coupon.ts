import ApiService from '@/services/apis';

export type TDeleteCouponPath = {
  id: string;
};

export type TDeleteCouponMaterials = {
  paths?: TDeleteCouponPath;
};

export type TDeleteCouponResponse = unknown;

export const deleteCoupon = async (
  materials: TDeleteCouponMaterials
): Promise<TDeleteCouponResponse> => {
  const { paths } = materials;
  const response = await ApiService.delete(`/coupons/${paths?.id}`);
  return response.data;
};
