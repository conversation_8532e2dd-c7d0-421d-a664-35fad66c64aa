import { TCategory } from '@/services/apis';
import { EIsActive } from '../enums';
import { TSubCategory } from './Category.types';

export interface TDigital {
  id?: number;
  category: TCategory;
  sub_category: TSubCategory;
  name: string;
  content: string;
  image: string | null;
  is_active: EIsActive;
}

export interface DigitalText {
  name: string;
  is_active: number;
  content: string;
  category_id: string | number;
  sub_category_id: string | number;
}

export type TOptionDigital = {
  id: number;
  name: string;
};
