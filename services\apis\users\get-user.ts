import ApiService from '@/services/apis';

export type TGetCreateUser = {
  id: any;
};

export type TGetUserResponse = {
  login_id: string;
  username: string;
  username_kana: string;
  email: string;
  email_verified_at: string | null;
  is_active: number;
  role_id: number;
  register_type: number;
  video_plan: number;
  is_accepted_term: number;
  plan_start_date: string;
  plan_end_date: string;
  created_by: number;
  updated_by: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  plan: 1 | 2;
  last_login_at: string;
};

export const getUser = async (
  payload: TGetCreateUser
): Promise<TGetUserResponse> => {
  const response = await ApiService.get(`/users/${payload.id}`);
  return response.data.data;
};
