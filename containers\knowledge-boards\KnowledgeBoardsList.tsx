import {
  TExerciseResponse,
  TInquiry,
  TKnowledgeBoardsFilter,
} from '@/commons/types';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  ColumnType,
  EditIcon,
  Pagination,
  Table,
} from '@/components';
import { getKnowledgeBoardsAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { formatDateTime, isObjectEmpty, removeEmpty } from '@/utils/functions';
import { ParsedUrlQueryInput } from 'querystring';
import KnowledgeBoardsFilter from './KnowledgeBoardsFilter';
import { setCallBackUrl } from '@/store/slices/status/history';
import {
  EInquiryIsPublic,
  EQuestionType,
  ERoleId,
  ESortType,
} from '@/commons/enums';
import { resetKnowlegdeBoardsSlice } from '@/store/slices/knowledge-board';

const KnowlegdeBoardsList = ({ query }: { query: TKnowledgeBoardsFilter }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [filter, setFilter] = useState<TKnowledgeBoardsFilter>();
  const { getKnowledgeBoardsResponse: KnowledgeBoards } = useSelector(
    (state: RootState) => state.knowledgeBoard
  );
  const { data, meta } = KnowledgeBoards || {};
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    return () => {
      dispatch(resetKnowlegdeBoardsSlice());
    };
  }, [dispatch]);

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_publics: [EInquiryIsPublic.PUBLIC],
      types: [
        EQuestionType.EXAM,
        EQuestionType.EXERCISE,
        EQuestionType.PRACTICE,
      ],
      order_by_last_updated_at: ESortType.DESC,
    };
    setFilter(defaultFilter);
  };

  const handleChangeOderLastUpdate = () => {
    setFilter({
      ...filter,
      order_by_last_updated_at:
        filter?.order_by_last_updated_at === ESortType.ASC
          ? ESortType.DESC
          : ESortType.ASC,
    });
  };

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      // add order_by_last_updated_at if query doesn't have
      let order_by_last_updated_at = query.order_by_last_updated_at;
      const sortTypes = [ESortType.ASC, ESortType.DESC];
      if (
        !order_by_last_updated_at ||
        !sortTypes.includes(order_by_last_updated_at as ESortType)
      ) {
        order_by_last_updated_at = ESortType.DESC;
      }
      setFilter({
        ...query,
        order_by_last_updated_at,
      });
      return;
    }
    setDefaultFilter();
  }, [query]);

  const getActions = useCallback(
    (element: TInquiry) => {
      const handleRedirect = () => {
        dispatch(setCallBackUrl(router.asPath));
        router.push(`/knowledge-boards/${element.id}/edit`);
      };
      return (
        <div className="flex items-center">
          {!(
            me?.data.role_id === ERoleId.TEACHER &&
            element.is_public === EInquiryIsPublic.PRIVATE
          ) && (
            <button onClick={handleRedirect}>
              <EditIcon />
            </button>
          )}
        </div>
      );
    },
    [router, me]
  );

  const columns = useMemo(() => {
    const cols = [
      {
        title: '問題ID',
        dataIndex: 'question_id',
        key: 'question_id',
        render: (element) => (
          <div>
            {element?.question && element?.question.id}
            {element?.exercise_question && element?.exercise_question.id}
            {element?.practice_question && element?.practice_question.id}
          </div>
        ),
      },
      {
        title: '大項目名',
        dataIndex: 'category.name',
        key: 'category.name',
        render: (element) => {
          return (
            <div className="max-w-25 xl:max-w-30 break-all">
              {element?.question && element?.question.category.name}
              {element?.exercise_question &&
                element?.exercise_question.category.name}
              {element?.practice_question &&
                element?.practice_question.category.name}
            </div>
          );
        },
      },
      {
        title: '小項目名',
        dataIndex: 'sub_category.name',
        key: 'sub_category.name',
        render: (element) => (
          <div className="max-w-25 xl:max-w-30 break-all">
            {element?.question && element?.question.sub_category?.name}
            {element?.exercise_question &&
              element?.exercise_question.sub_category?.name}
            {element?.practice_question &&
              element?.practice_question.sub_category?.name}
          </div>
        ),
      },
      {
        title: 'タイプ',
        dataIndex: 'type',
        key: 'type',
        render: (element) => (
          <div className="w-12">
            {element?.type === 1 && '練習問題'}
            {element?.type === 2 && '実践問題'}
            {element?.type === 3 && '模擬試験'}
          </div>
        ),
      },
      {
        title: '質問',
        dataIndex: 'inquiry',
        key: 'inquiry',
        render: (element) => (
          <div className="max-w-50 2xl:max-w-70 line-clamp-3">
            {element?.inquiry}
          </div>
        ),
      },
      {
        title: 'コメント',
        dataIndex: 'comment',
        key: 'comment',
        render: (element) => <div className="">{element?.comments_count}</div>,
      },
      {
        title: (
          <div
            onClick={handleChangeOderLastUpdate}
            className="flex items-center cursor-pointer"
          >
            <div>最終コメント日時</div>
            <div className="h-4 w-4 ml-2">
              {filter?.order_by_last_updated_at === ESortType.ASC ? (
                <ArrowDownIcon />
              ) : (
                <ArrowUpIcon />
              )}
            </div>
          </div>
        ),
        dataIndex: 'last_update_at',
        key: 'last_update_at',
        render: (element) => (
          <div className="">
            {element &&
              element.last_updated_at &&
              formatDateTime(element.last_updated_at)}
          </div>
        ),
      },
      {
        title: '講師未返信',
        dataIndex: 'teacher_reply',
        key: 'teacher_reply',
        render: (element) => (
          <div className="max-w-50 2xl:max-w-50 truncate">
            {element?.teacher_reply_status === 1 ? 'あり' : 'なし'}
          </div>
        ),
      },
      {
        title: '管理者未返信',
        dataIndex: 'admin_reply',
        key: 'admin_reply',
        render: (element) => (
          <div className="max-w-50 2xl:max-w-50 truncate">
            {element?.admin_reply_status === 1 ? 'あり' : 'なし'}
          </div>
        ),
      },
      {
        title: '公開・非公開',
        dataIndex: 'invalid',
        key: 'invalid',
        render: (element) => (
          <>
            {element?.is_public === EInquiryIsPublic.PUBLIC ? '公開' : '非公開'}
          </>
        ),
      },
      {
        title: 'アクション',
        dataIndex: 'action',
        key: 'action',
        className: 'w-32',
        render: getActions,
      },
    ] as Array<ColumnType<TInquiry>>;

    if (me?.data.role_id === ERoleId.TEACHER) {
      return cols.filter((item) => {
        return item.key !== 'admin_reply';
      });
    }

    return cols;
  }, [getActions, me, filter?.order_by_last_updated_at]);

  const fetchData = useCallback(
    (page: number, filter?: TKnowledgeBoardsFilter) => {
      const category_id = filter?.category_id;
      const sub_category_id = filter?.sub_category_id;
      const question_id = filter?.question_id;
      const types = filter?.types as Array<number>;
      const admin_reply = filter?.admin_reply?.[0] as number;
      const teacher_reply = filter?.teacher_reply?.[0] as number;
      const isPublic = filter?.is_publics as Array<number>;
      const order_by_last_updated_at = filter?.order_by_last_updated_at;
      const params = {
        page,
        category_id,
        sub_category_id,
        question_id,
        types,
        teacher_reply,
        admin_reply,
        is_publics: isPublic,
        order_by_last_updated_at,
      };
      dispatch(getKnowledgeBoardsAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ pathname: '/knowledge-boards', query }, undefined, {
        shallow: true,
      });
      fetchData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md mb-4">
          <KnowledgeBoardsFilter
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
            me={me}
          />
        </div>
      </div>
      <div className="w-full flex justify-between items-center">
        <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
          meta?.total || 0
        }件`}</h2>
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <Table<TInquiry> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default KnowlegdeBoardsList;
