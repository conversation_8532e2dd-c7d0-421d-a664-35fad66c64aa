import ApiService from '@/services/apis';

export type TGetExportPracticeQuestionsParams = {
  category_id?: number;
  show_order?: number;
  content?: string;
  title?: string;
  id?: number;
  question_year?: string;
  types?: Array<number>;
  is_actives?: Array<number>;
};

export type TGetExportPracticeQuestionsMaterials = {
  params?: TGetExportPracticeQuestionsParams;
};

export type TGetExportPracticeQuestionsResponse = Blob;

export const getExportPracticeQuestions = async (
  materials: TGetExportPracticeQuestionsMaterials
): Promise<TGetExportPracticeQuestionsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/export/practice-questions', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
