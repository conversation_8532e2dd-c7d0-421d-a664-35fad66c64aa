import KnowledgeBoardComment from './Comment';
import { useEffect, useMemo, useState } from 'react';
import ReplyInput from './ReplyInput';
import EditInput from './EditInput';
import { useRouter } from 'next/router';
import { getKnowledgeBoard } from '@/services/apis/knowledgeboards/get-knowledge-board';
import { TComment, TInquiry, TKnowledgeBoard } from '@/commons/types';
import Inquiry from './Inquiry';
import { getComments } from '@/services/apis/knowledgeboards/get-comments';
import {
  TPutEditCommentMaterials,
  deleteComments,
  postReplyComment,
  putEditComment,
} from '@/services/apis';
import { showNotification } from '@/utils/functions';
import { EReplyType, ETypeNotification } from '@/commons/enums';
import { EXCEPTION } from '@/commons/messages-enum';
import NotificationErrors from '@/components/NotificationErrors';
import {
  TPutUpdateKnowledgeBoardMaterials,
  putUpdateKnowledgeBoard,
} from '@/services/apis/knowledgeboards/put-update-knowledge-board';

const KnowledgeBoardDetail = () => {
  const [knowledgeBoard, setKnowledgeBoard] = useState<TKnowledgeBoard>();
  const [comments, setComments] = useState<Array<TComment>>([]);
  const [repliedComment, setRepliedComment] = useState<TComment | TInquiry>();
  const [editComment, setEditComment] = useState<TComment>();
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const router = useRouter();
  const { id } = router.query;

  const fetchKnowledgeBoard = async () => {
    const paths = {
      id: `${id}`,
    };
    const response = await getKnowledgeBoard({
      paths,
    });
    return response.data;
  };

  const fetchComments = async () => {
    const paths = {
      knowledgeBoardId: `${id}`,
    };
    const response = await getComments({
      paths,
    });
    return response.data;
  };

  const refetchComments = async () => {
    const paths = {
      knowledgeBoardId: `${id}`,
    };
    try {
      const response = await getComments({
        paths,
      });
      setComments(response.data);
    } catch (error) {
      console.log(error);
    }
  };

  const fetchData = async () => {
    setIsFetching(true);
    try {
      const [responseKnowlistBoard, responseComments] = await Promise.all([
        fetchKnowledgeBoard(),
        fetchComments(),
      ]);

      setKnowledgeBoard(responseKnowlistBoard);
      setComments(responseComments);
    } catch (error) {}
    setIsFetching(false);
  };

  const refetchKnowledgeBoard = async () => {
    const paths = {
      id: `${id}`,
    };
    try {
      const response = await getKnowledgeBoard({
        paths,
      });
      setKnowledgeBoard(response.data);
    } catch (error) {
      console.log(error);
    }
  };

  const handleReplyComment = async (content: string) => {
    if (!content) return;
    let reply_type = EReplyType.NO_REPLY;

    if (repliedComment && (repliedComment as TComment).knowledge_board_id) {
      reply_type = EReplyType.REPLY_COMMENT;
    }
    if (repliedComment && (repliedComment as TInquiry).inquiry) {
      reply_type = EReplyType.REPLY_INQUIRY;
    }
    try {
      const materials = {
        paths: {
          knowledgeBoardId: `${id}`,
        },
        payload: {
          reply_type,
          reply_to_id:
            reply_type === EReplyType.NO_REPLY ? undefined : repliedComment?.id,
          content,
        },
      };
      const response = await postReplyComment(materials);
      showNotification(ETypeNotification.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      handleRemoveReplyComment();
      refetchComments();
    } catch (error: any) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />
      );
    }
  };

  const handleRemoveReplyComment = () => {
    setRepliedComment(undefined);
  };

  const handleUpdateComment = async (materials: TPutEditCommentMaterials) => {
    try {
      await putEditComment(materials);
      showNotification(ETypeNotification.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      refetchComments();
      if (materials.payload.content) {
        setEditComment(undefined);
      }
    } catch (error: any) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />
      );
    }
  };

  const handleSeenComment = async (materials: TPutEditCommentMaterials) => {
    try {
      await putEditComment(materials);
      showNotification(ETypeNotification.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      refetchComments();
    } catch (error) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />
      );
    }
  };

  const handleChangeIsPublicInquiry = async (
    materials: TPutUpdateKnowledgeBoardMaterials
  ) => {
    try {
      await putUpdateKnowledgeBoard(materials);
      showNotification(ETypeNotification.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      refetchKnowledgeBoard();
    } catch (error: any) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />
      );
    }
  };

  const handleGetRepliedComment = (comment: any) => {
    setEditComment(undefined);
    setRepliedComment(comment);
  };

  const handleGetEditComment = (comment: TComment) => {
    setEditComment(comment);
  };

  const handleDeletePrivateComment = async (comment: TComment) => {
    const paths = { commentId: comment.id };
    try {
      const response = await deleteComments({ paths });
      refetchComments();
      showNotification(ETypeNotification.SUCCESS, EXCEPTION.ACTION_SUCCESS);
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />
      );
    }
  };

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  return (
    <>
      {!isFetching && (
        <div className="bg-alice-blue-light py-4 px-5 mb-5 rounded-md">
          {knowledgeBoard && (
            <Inquiry
              inquiry={knowledgeBoard}
              handleGetRepliedComment={handleGetRepliedComment}
              handleChangeIsPublicInquiry={handleChangeIsPublicInquiry}
            />
          )}
          <div className="grid grid-cols-1">
            {comments &&
              comments.length > 0 &&
              comments.map((comment: any) => (
                <KnowledgeBoardComment
                  comment={comment}
                  key={comment.id}
                  handleGetRepliedComment={handleGetRepliedComment}
                  handleGetEditComment={handleGetEditComment}
                  handleUpdateComment={handleUpdateComment}
                  handleDeletePrivateComment={handleDeletePrivateComment}
                  handleSeenComment={handleSeenComment}
                />
              ))}

            {editComment ? (
              <EditInput
                editComment={editComment}
                setEditComment={setEditComment}
                handleUpdateComment={handleUpdateComment}
              />
            ) : (
              <div>
                {
                  <ReplyInput
                    repliedComment={repliedComment}
                    handleReplyComment={handleReplyComment}
                    handleRemoveReplyComment={handleRemoveReplyComment}
                  />
                }
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default KnowledgeBoardDetail;
