import {
  EAdminReplyStatus,
  EInquiryIsPublic,
  EReplyType,
  ERoleId,
  ESendToAdmin,
  ESendToTeacher,
  ETeacherReplyStatus,
  EUserRoleId,
} from '@/commons/enums';
import { MODAL_MESSAGE } from '@/commons/messages-enum';
import { TComment } from '@/commons/types';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { TPutEditCommentMaterials } from '@/services/apis';
import { RootState } from '@/store/configureStore';
import { formatDateTime } from '@/utils/functions';
import clsx from 'clsx';
import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

type TKnowledgeBoardCommentProps = {
  comment: TComment;
  handleGetRepliedComment: any;
  handleGetEditComment: any;
  handleUpdateComment: (materials: TPutEditCommentMaterials) => void;
  handleDeletePrivateComment: (comment: TComment) => void;
  handleSeenComment: (materials: TPutEditCommentMaterials) => void;
};

const KnowledgeBoardComment = ({
  comment,
  handleGetRepliedComment,
  handleGetEditComment,
  handleUpdateComment,
  handleDeletePrivateComment,
  handleSeenComment,
}: TKnowledgeBoardCommentProps) => {
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const [isShowModalDelete, setIsShowModalDelete] = useState<boolean>(false);
  const [isShowModalSeenComment, setIsShowModalSeenComment] =
    useState<boolean>(false);
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  const onChangePublic = () => {
    const isPublicValue =
      comment.is_public === EInquiryIsPublic.PRIVATE
        ? EInquiryIsPublic.PUBLIC
        : EInquiryIsPublic.PRIVATE;

    const materials = {
      paths: {
        id: comment.id.toString(),
      },
      payload: {
        is_public: isPublicValue,
        _method: 'PUT',
      },
    };

    handleUpdateComment(materials);
  };

  const onOpenModal = () => {
    setIsShowModal(true);
  };

  const onCloseModal = () => {
    setIsShowModal(false);
  };

  const isShowComment = useMemo(() => {
    const isCommentOfTeacher =
      (comment.user && comment.user.role_id === EUserRoleId.TEACHER) ||
      (comment.admin && comment.admin.role_id === ERoleId.TEACHER);
    if (
      !isCommentOfTeacher &&
      me?.data.role_id === ERoleId.TEACHER &&
      comment.is_public === EInquiryIsPublic.PRIVATE
    )
      return false;
    return true;
  }, [comment, me]);

  const bgColor = useMemo(() => {
    if (comment.is_public === EInquiryIsPublic.PRIVATE) {
      return 'bg-gray-200';
    }
    if (comment.teacher_reply_status === ETeacherReplyStatus.NO_REPLY) {
      return 'bg-yellow-200';
    }
    return 'bg-white';
  }, [comment]);

  const handleOpenConfirmSeenComment = () => {
    if (
      // teacher and admin can click into comment send to teacher
      (comment.is_public !== EInquiryIsPublic.PRIVATE &&
        comment.teacher_reply_status === ETeacherReplyStatus.NO_REPLY) ||
      // only admin can click into comment send to admin
      (me?.data?.role_id === ERoleId.ADMIN && comment.admin_reply_status === EAdminReplyStatus.NO_REPLY)
    ) {
      setIsShowModalSeenComment(true);
    }
  };

  const handleConfirmSeenComment = () => {
    // teacher and admin can click into comment send to teacher
    if (
      comment.is_public !== EInquiryIsPublic.PRIVATE &&
      comment.teacher_reply_status === ETeacherReplyStatus.NO_REPLY
    ) {
      const materials = {
        paths: {
          id: comment.id.toString(),
        },
        payload: {
          send_to_teacher: ESendToTeacher.NO,
          _method: 'PUT',
        },
      };
      handleSeenComment(materials);
      // only admin can click into comment send to admin
    } else if (
      me?.data?.role_id === ERoleId.ADMIN &&
      comment.admin_reply_status === EAdminReplyStatus.NO_REPLY
    ) {
      const materials = {
        paths: {
          id: comment.id.toString(),
        },
        payload: {
          send_to_admin: ESendToAdmin.NO,
          _method: 'PUT',
        },
      };
      handleSeenComment(materials);
    }
  };

  return (
    <div className="grid grid-cols-1 mt-4">
      {/* Comment component */}
      <ConfirmModal
        visible={isShowModal}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
        onClose={onCloseModal}
        onConfirm={onChangePublic}
      />
      <ConfirmModal
        visible={isShowModalDelete}
        content={MODAL_MESSAGE.CONFIRM_DELETE}
        onClose={() => setIsShowModalDelete(false)}
        onConfirm={() => {
          handleDeletePrivateComment(comment);
        }}
      />
      <ConfirmModal
        visible={isShowModalSeenComment}
        content={MODAL_MESSAGE.CONFIRM_SEEN_COMMENT}
        onClose={() => setIsShowModalSeenComment(false)}
        onConfirm={handleConfirmSeenComment}
      />
      <div
        className={clsx(
          'flex justify-between border rounded-md py-4 px-5',
          bgColor
        )}
      >
        {!isShowComment ? (
          <div className="w-full flex justify-center ">
            このコメントは削除されました
          </div>
        ) : (
          <>
            <div
              className={clsx(
                'grow py-2 px-4 mr-4 font-bold',
                ((comment.is_public !== EInquiryIsPublic.PRIVATE &&
                  comment.teacher_reply_status ===
                    ETeacherReplyStatus.NO_REPLY) ||
                  (me?.data?.role_id === ERoleId.ADMIN && comment.admin_reply_status === EAdminReplyStatus.NO_REPLY)) &&
                  'cursor-pointer'
              )}
              onClick={() => handleOpenConfirmSeenComment()}
            >
              <div className="flex items-center mb-1">
                <span className="mr-6">
                  {formatDateTime(comment.created_at)}
                </span>
                {comment.user && comment.user.role_id === EUserRoleId.USER && (
                  <span className="border rounded-md px-[6px] py-[6px] bg-white">
                    {`${comment.user.username} (ID: ${comment.user.id})`}
                  </span>
                )}
                {((comment.user &&
                  comment.user.role_id === EUserRoleId.TEACHER) ||
                  comment.admin) && (
                  <div className="border rounded-full min-w-[96px] bg-[#ff0000] py-[4px] text-white text-center">
                    {comment.admin && comment.admin.role_id === ERoleId.ADMIN
                      ? '管理者'
                      : '講師'}
                  </div>
                )}
              </div>
              {comment.send_to_teacher === ESendToTeacher.YES ? (
                <div className="mb-1">
                  <span className="mr-2">宛先：</span>講師
                </div>
              ) : (
                <div>
                  {/* Show reply info of inquiry */}
                  {comment.reply_type === EReplyType.REPLY_INQUIRY && (
                    <div className="mb-1">
                      <span className="mr-2">返信：</span>{' '}
                      {`1. ${comment.reply_to_knowledge_board.user?.username}`}
                    </div>
                  )}
                  {/* Show reply info of commment */}
                  {comment.reply_type === EReplyType.REPLY_COMMENT &&
                    //  if replied comment doesn't have ID, hide it
                    comment.reply_to_comment?.id && (
                    <div className="mb-1">
                      <span className="mr-2">返信：</span>{' '}
                      {comment.reply_to_comment.user
                        ? `${comment.reply_to_comment.comment_no}. ${comment.reply_to_comment.user.username}`
                        : '管理者'}
                    </div>
                  )}
                </div>
              )}
              <div className="flex justify-start">
                <div className="mr-2">{`${comment.comment_no}.`}</div>
                <p className="whitespace-pre-line break-all">
                  {comment.content}
                </p>
              </div>
            </div>
            <div className="flex flex-col py-2 gap-2">
              {me?.data.role_id === ERoleId.ADMIN && (
                <Button
                  onClick={onOpenModal}
                  size={'small'}
                  styleType={
                    comment.is_public === EInquiryIsPublic.PUBLIC
                      ? EButtonStyleType.OUTLINE_PRIMARY
                      : EButtonStyleType.GREEN
                  }
                  title={
                    comment.is_public === EInquiryIsPublic.PUBLIC
                      ? '非公開'
                      : '公開'
                  }
                />
              )}
              {comment.user && comment.user.role_id === EUserRoleId.USER && (
                <Button
                  onClick={() => handleGetRepliedComment(comment)}
                  size={'small'}
                  styleType={EButtonStyleType.PRIMARY}
                  title={'返信'}
                />
              )}
              {((comment.user &&
                comment.user.role_id === EUserRoleId.TEACHER) ||
                (comment.admin && me?.data.role_id === ERoleId.ADMIN) ||
                (comment.admin?.role_id === ERoleId.TEACHER &&
                  me?.data.role_id === ERoleId.TEACHER)) && (
                <Button
                  onClick={() => handleGetEditComment(comment)}
                  size={'small'}
                  styleType={EButtonStyleType.PRIMARY}
                  title={'編集'}
                />
              )}
              {comment.is_public === EInquiryIsPublic.PRIVATE &&
                me?.data.role_id === ERoleId.ADMIN && (
                <Button
                  onClick={() => setIsShowModalDelete(true)}
                  size={'small'}
                  styleType={EButtonStyleType.DANGER}
                  title={'削除'}
                />
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default KnowledgeBoardComment;
