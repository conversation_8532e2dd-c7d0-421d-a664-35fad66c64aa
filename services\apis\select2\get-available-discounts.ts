import ApiService from '@/services/apis';

export type TGetSelectAvailableDiscountsParams = {
  page: number;
  name?: string;
};

export type TGetSelectAvailableDiscountsMaterials = {
  params?: TGetSelectAvailableDiscountsParams;
};

export type TGetSelectAvailableDiscountsResponse = any;

export const getSelectAvailableDiscounts = async (
  materials: TGetSelectAvailableDiscountsMaterials
): Promise<TGetSelectAvailableDiscountsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/select2/available-discounts', { params });
  return response.data;
};
