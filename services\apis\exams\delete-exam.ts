import { TExam } from '@/commons/types';
import ApiService from '@/services/apis';

export type TDeleteExamPath = {
  id: string;
};

export type TDeleteExamMaterials = {
  paths?: TDeleteExamPath;
};

export type TDeleteExamResponse = { data: TExam };

export const deleteExam = async (
  materials: TDeleteExamMaterials
): Promise<TDeleteExamResponse> => {
  const { paths } = materials;
  const response = await ApiService.delete(`/exams/${paths?.id}`);
  return response.data;
};
