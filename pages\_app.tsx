import '@/styles/globals.less';
import '@/styles/tailwind.css';
import type { AppProps } from 'next/app';
import { Fragment, useEffect, useState } from 'react';
import Head from 'next/dist/shared/lib/head';
import { NextComponentType } from 'next/types';
import Router from 'next/router';
import { Provider } from 'react-redux';
import { store } from '@/store/configureStore';
import dynamic from 'next/dynamic';
const DynamicMaintain = dynamic(
  () => import('@/containers/maintain/Maintain'),
  {
    ssr: false,
  }
);
// import { SessionProvider, signIn, useSession } from 'next-auth/react';

import { GuestLayout } from '@/layouts';
import { HistoryManagerProvider, useHistoryManager } from '@/utils/contexts';
interface CustomAppProps extends AppProps {
  Component: NextPageWithLayout;
}

export type NextPageWithLayout = NextComponentType & {
  Layout?: any;
};
function MyApp({ Component, pageProps }: CustomAppProps) {
  // const [loading, setLoading] = useState(false);
  const Layout = Component.Layout || GuestLayout;
  const historyManager = useHistoryManager();
  // useEffect(() => {
  //   const start = () => setLoading(true);
  //   const end = () => setLoading(false);

  //   Router.events.on('routeChangeStart', start);
  //   Router.events.on('routeChangeComplete', end);
  //   Router.events.on('routeChangeError', end);
  //   return () => {
  //     Router.events.off('routeChangeStart', start);
  //     Router.events.off('routeChangeComplete', end);
  //     Router.events.off('routeChangeError', end);
  //   };
  // }, []);

  return (
    <Fragment>
      <Head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1"
        ></meta>
        <title>Kojiro</title>
        <link rel="icon" href="/icon.png" />
      </Head>
      <Provider store={store}>
        <HistoryManagerProvider value={historyManager}>
          <Layout>
            <Fragment>
              <Component {...pageProps} />
              <DynamicMaintain />
            </Fragment>
          </Layout>
        </HistoryManagerProvider>
      </Provider>
    </Fragment>
  );
}

export default MyApp;
