@tailwind base;
@tailwind components;
@tailwind utilities;

/* @font-face {
  font-family: NotoSansCJKjp;
  src: url("/font/NotoSansJP-Medium.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: NotoSansCJKjp;
  src: url("/font/NotoSansJP-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: NotoSansCJKjp;
  src: url("/font/NotoSansJP-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
}


@font-face {
  font-family: NotoSansCJKjp;
  src: url("/font/NotoSansJP-Thin.otf") format("opentype");
  font-weight: 100;
  font-style: normal;
} */

@font-face {
  font-family: Meiryo;
  src: url("/font/meiryo.ttc") format("opentype");
}

input[type="text"]:disabled {
  background: #ffffff !important;
  color: black !important;
}

input[type="number"]:disabled {
  background: #ffffff !important;
  color: black !important;
}

.ant-input-number-disabled .ant-input-number-input {
  background: #ffffff !important;
  color: black !important;
}

.discount.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: #f5f5f5 !important;
}

.ant-radio-disabled .ant-radio-inner {
  background-color: #fff !important;
  border-color: #1890ff !important;
}

.ant-radio-inner::after {
  background-color: #1890ff !important;
}

.ant-radio-disabled + span {
  color: black !important;
}
.input-disabled {
  color: black !important;
}

.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background: #ffffff !important;
  color: black !important;
}

.input-disabled .ant-select-selection-item {
  color: black;
}

.jodit-wysiwyg {
  color: black !important;
}

@layer base {
  * {
    font-family: Meiryo;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p {
    margin: 0 !important;
  }
  .question_tab .ant-tabs-nav {
    margin-bottom: theme("spacing.0") !important;
  }
  .question_tab .ant-tabs-tab {
    @apply !bg-primary !text-white font-bold min-w-[136px] flex justify-center !text-xs !py-3 !rounded-t-xl !rounded-b-none;
  }
  .question_tab .ant-tabs-tab:hover {
    @apply opacity-80;
  }
  .question_tab .ant-tabs-tab.ant-tabs-tab-active {
    @apply !bg-primary-variant;
  }
  .question_tab .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    @apply !text-white;
  }

  .html-render p {
    display: inline;
  }

  .question-editor {
    color: #000000;
    line-height: 1.5715 !important;
    word-break: break-word !important;
  }
}

.jodit-wysiwyg p {
  display: inline;
}

.CustomCollapse {
  border: none !important;
}

.jodit-react-container {
  word-break: break-all;
}

.jodit-dialog__content .jodit__preview-box.jodit-context p {
  display: inline;
  color: #000000;
  line-height: 1.5715 !important;
  word-break: break-word !important;
  font-size: 18px;
}

.CustomCollapse .ant-collapse-content {
  border: none !important;
}

.CustomCollapse .ant-collapse-header {
  background-color: #f3f9fc !important;
}

.CustomCollapse .ant-collapse-item {
  border: none !important;
}

.CustomCollapse .ant-collapse-content-box {
  background-color: #f3f9fc !important;
}
.UploadForm {
  display: block !important;
}
.UploadForm .ant-upload {
  display: block !important;
}
.ant-picker-time-panel .ant-picker-header {
  visibility: hidden;
}
/* .ant-picker-now {
  display: none !important;
} */

.ant-picker-time-panel-column:nth-child(2n) .ant-picker-time-panel-cell:nth-child(10n + 1) {
  display: block !important;
}

.ant-picker-time-panel-column:nth-child(2n) .ant-picker-time-panel-cell {
  display: none !important;
}
