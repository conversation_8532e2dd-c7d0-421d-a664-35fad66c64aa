import { Input } from '../../components/Input';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { DatePicker, Form, Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import validate from '@/utils/validate';
import dynamic from 'next/dynamic';
import { createNotification, helpers } from '@/services/apis';
import locale from 'antd/lib/date-picker/locale/ja_JP';
import moment from 'moment-timezone';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import {
  EIsActive,
  EPlacement,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { Rule } from 'antd/lib/form';
import { useRouter } from 'next/router';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import type { Moment } from 'moment';
import DatePickerNoti from './[id]/DatePickerNoti';
import { GetServerSideProps } from 'next';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';

const RegisterNotification = () => {
  moment.tz.setDefault('Asia/Tokyo');
  const {
    validateStartEndDate,
    validateStartDate,
    disabledDateBeforNow,
    checkEmptyString,
    checkMaxLengthEditor,
    checkMaxLength,
  } = validate;
  const EditorBoxForm = dynamic(
    () => import('@/components/EditorBox/EditorBoxForm'),
    { ssr: false }
  );
  const [isFisrt, setFisrt] = useState<boolean>(true);
  const [isVisible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const router = useRouter();
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  useEffect(() => {
    form.setFieldsValue({
      is_active: EIsActive.ENABLE,
    });
  }, []);

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-2">
        <Form.Item className={'!mb-0'} name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onBack = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router?.push('/notifications/');
  };

  const onSubmit = async () => {
    try {
      const values = form.getFieldsValue();
      const payload = {
        title: values?.title,
        content: values?.content,
        is_active: String(values?.is_active),
        start_time: values?.start_time?.format('YYYY-MM-DD HH:mm:00'),
        end_time: values?.end_time?.format('YYYY-MM-DD HH:mm:00'),
      };
      const data = await createNotification(payload);
      if (data) {
        onBack();
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onValuesChange = () => {
    const values = form.getFieldsValue();
    if (values?.start_time && values?.end_time) {
      form.validateFields(['start_time', 'end_time']);
    }
  };

  const onCloseModal = () => {
    setVisible(false);
  };

  const onOpenModal = () => {
    setVisible(true);
  };

  return (
    <div className="mx-12 mt-4">
      <span className="text-lg font-bold">お知らせ新規登録</span>
      <Form onValuesChange={onValuesChange} form={form} onFinish={onOpenModal}>
        <div className="bg-alice-blue-light py-4 px-5 mt-3">
          <div className="grid grid-cols-2 justify-between gap-5">
            <div>
              <div>
                <span className="text-textGray text-sm font-bold">
                  タイトル
                </span>
                <Form.Item
                  rules={
                    [
                      {
                        required: true,
                        message: validationMessage.required('タイトル'),
                      },
                      () => checkMaxLength('タイトル', 255),
                    ] as Rule[]
                  }
                  className={'!mb-0'}
                  name={'title'}
                >
                  <Input maxLength={255} classNames={'!mt-2'} />
                </Form.Item>
              </div>
              <div className="mt-7">
                <span className="text-textGray text-sm font-bold">
                  公開期間
                </span>
                <div className="grid grid-cols-2 gap-5 mt-2">
                  <Form.Item
                    rules={
                      [
                        {
                          required: true,
                          message: validationMessage.required('開始日'),
                        },
                        validateStartDate(false),
                      ] as Rule[]
                    }
                    className={'!mb-0'}
                    name={'start_time'}
                  >
                    <DatePickerNoti
                      disabledDate={disabledDateBeforNow}
                      onChange={(date: Moment | null) => {
                        form.setFieldsValue({
                          start_time: date,
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    rules={
                      [
                        {
                          required: true,
                          message: validationMessage.required('終了日'),
                        },
                        validateStartEndDate(false),
                      ] as Rule[]
                    }
                    className={'!mb-0'}
                    name={'end_time'}
                  >
                    <DatePickerNoti
                      disabledDate={disabledDateBeforNow}
                      onChange={(date: Moment | null) => {
                        form.setFieldsValue({
                          end_time: date,
                        });
                      }}
                    />
                  </Form.Item>
                </div>
                <div className="mt-7">
                  <div className="flex flex-row">
                    <span className="text-textGray text-sm font-bold">
                      有効・無効
                    </span>
                  </div>
                  {ratioRender()}
                </div>
              </div>
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">本文</span>
              <Form.Item
                rules={
                  [
                    checkEmptyString(isFisrt),
                    () => checkMaxLengthEditor('本文', 1, 65535),
                  ] as Rule[]
                }
                className={'!mb-0 !mt-2'}
                name={'content'}
              >
                <EditorBoxForm onChange={() => setFisrt(false)} />
              </Form.Item>
            </div>
          </div>
        </div>
        <ConfirmModal
          visible={isVisible}
          onClose={onCloseModal}
          content={MODAL_MESSAGE.CONFIRM_CREATE}
          onConfirm={onSubmit}
        />
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={onBack}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'登録'}
          />
        </div>
      </Form>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default RegisterNotification;
