import { TGetExamMaterials, TGetExamResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetExamAction {
  GET_EXAM = 'GET_EXAM',
  GET_EXAM_REQUEST = 'GET_EXAM_REQUEST',
  GET_EXAM_SUCCESS = 'GET_EXAM_SUCCESS',
  GET_EXAM_FAILED = 'GET_EXAM_FAILED',
}

// TYPES

export type TGetExamRequest = {
  type: EGetExamAction.GET_EXAM_REQUEST;
  payload: {
    materials: TGetExamMaterials;
    successCallback?: (response: TGetExamResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetExamSuccess = {
  type: EGetExamAction.GET_EXAM_SUCCESS;
  payload: { response?: TGetExamResponse };
};

export type TGetExamFailed = {
  type: EGetExamAction.GET_EXAM_FAILED;
};

export const getExamAction = {
  request: createActionCreator(
    EGetExamAction.GET_EXAM_REQUEST,
    (resolve) =>
      (
        materials: TGetExamMaterials,
        successCallback?: (response: TGetExamResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetExamRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetExamAction.GET_EXAM_SUCCESS,
    (resolve) =>
      (response?: TGetExamResponse): TGetExamSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetExamAction.GET_EXAM_FAILED,
    (resolve) =>
      (error: unknown): TGetExamFailed =>
        resolve({ error })
  ),
};
