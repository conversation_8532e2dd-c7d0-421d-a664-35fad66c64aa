import { EIsActive, EVideoPlan } from '@/commons/enums';
import { Button, EButtonStyleType, Input } from '@/components';
import { Select } from '@/components/Select';
import { Checkbox } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { useEffect, useState } from 'react';
import { TFilterUser, TFilterUserProps } from './user.types';

const { Option } = Select;

const FilterUser = ({
  defaultFilter,
  clearFilter,
  setFilter,
}: TFilterUserProps) => {
  const [filterUser, setFilterUser] = useState<TFilterUser>();
  const handleUpdateFilter = () => {
    setFilter?.(filterUser);
  };

  useEffect(() => {
    console.log({ defaultFilter });
    setFilterUser(defaultFilter);
  }, [defaultFilter]);

  const changeIsActive = (checkedValue: Array<CheckboxValueType>) => {
    const is_actives = checkedValue as Array<number>;
    setFilterUser((current) => ({ ...current, is_actives }));
  };

  const changeRegisterType = (e: CheckboxChangeEvent) => {
    const register_type = e.target.checked;
    setFilterUser((current) => ({ ...current, register_type }));
  };

  const handleChangeUserName = (username: string) => {
    setFilterUser((filterUser) => ({ ...filterUser, username }));
  };
  const hanldeChangeEmail = (email: string) => {
    setFilterUser((filterUser) => ({ ...filterUser, email }));
  };

  const handleChangeVideoPlan = (checkedValue: Array<CheckboxValueType>) => {
    // setFilterUser((current) => ({ ...current, video_plan }));
    const video_plans = checkedValue as Array<number>;
    setFilterUser((current) => ({ ...current, video_plans }));
  };

  const handleChangePlan = (checkedValue: Array<CheckboxValueType>) => {
    // setFilterUser((current) => ({ ...current, video_plan }));
    const plans = checkedValue as Array<number>;
    setFilterUser((current) => ({ ...current, plans }));
  };

  const handleChangeRegisterType = (checkedValue: Array<CheckboxValueType>) => {
    const register_types = checkedValue as Array<number>;
    setFilterUser((current) => ({ ...current, register_types }));
  };

  const handleChangeStatus = (plan_status: string) => {
    setFilterUser((current) => ({ ...current, plan_status }));
  };

  const handleChangeRole = (role_id: string) => {
    setFilterUser((current) => ({ ...current, role_id }));
  };

  return (
    <div className="grid grid-cols-1 gap-y-4 w-full">
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5 gap-y-4">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">氏名</p>
          <Input
            size="large"
            classNames="w-full !rounded"
            name="username"
            onChange={handleChangeUserName}
            value={filterUser?.username}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">ステータス</p>
          <Select
            className="w-full rounded"
            size="large"
            onChange={handleChangeStatus}
            value={filterUser?.plan_status}
            placeholder="選択してください"
          >
            <Option value={1}>利用前</Option>
            <Option value={2}>利用中</Option>
            <Option value={3}>利用終了</Option>
          </Select>
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">メールアドレス</p>
          <Input
            size="large"
            classNames="w-full !rounded"
            name="email"
            onChange={hanldeChangeEmail}
            value={filterUser?.email}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">権限</p>
          <Select
            className="w-full rounded"
            size="large"
            onChange={handleChangeRole}
            value={filterUser?.role_id}
            placeholder="選択してください"
          >
            <Option value={1}>ユーザー</Option>
            <Option value={2}>講師</Option>
          </Select>
        </div>
        <div className="grid grid-cols-2 gap-2">
          <div className="flex flex-col gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj font-bold">対策動画</p>
            <div className="flex flex-row items-center">
              <Checkbox.Group
                onChange={handleChangeVideoPlan}
                value={filterUser?.video_plans}
              >
                <Checkbox value={EVideoPlan.HAVE_VIDEO}>あり</Checkbox>
                <Checkbox value={EVideoPlan.NOT_VIDEO}>なし</Checkbox>{' '}
              </Checkbox.Group>
            </div>
          </div>
          <div className="flex flex-col gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj">有効・無効</p>
            <div className="flex flex-row items-center">
              <Checkbox.Group
                onChange={changeIsActive}
                value={filterUser?.is_actives}
              >
                <Checkbox value={EIsActive.ENABLE}>有効</Checkbox>
                <Checkbox value={EIsActive.DISABLE}>無効</Checkbox>
              </Checkbox.Group>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-2 !text-sm col-span-1 !font-bold">
          <div className="flex flex-col gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj">利用タイプ</p>
            <div className="flex flex-row items-center gap-4">
              <Checkbox.Group
                onChange={handleChangePlan}
                value={filterUser?.plans}
              >
                <Checkbox value={2}>有料</Checkbox>
                <Checkbox value={1}>無料</Checkbox>
              </Checkbox.Group>
            </div>
          </div>
          <div className="flex flex-col gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj">購入方法</p>
            <div className="flex flex-row items-center">
              <Checkbox.Group
                onChange={handleChangeRegisterType}
                value={filterUser?.register_types}
              >
                <Checkbox value={1}>EC</Checkbox>
                <Checkbox value={3}>アプリ</Checkbox>
                <Checkbox value={2}>管理</Checkbox>
              </Checkbox.Group>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-2.5">
        <Button
          styleType={EButtonStyleType.OUTLINE_PRIMARY}
          title="クリア"
          className="!font-medium"
          onClick={clearFilter}
        />
        <Button
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          title="検索"
          className="!font-medium"
          onClick={handleUpdateFilter}
        />
      </div>
    </div>
  );
};

export default FilterUser;
