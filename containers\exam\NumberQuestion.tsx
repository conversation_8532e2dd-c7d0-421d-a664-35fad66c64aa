import {
  Button,
  DeleteIcon,
  EButtonStyleType,
  Input,
  InputNumber,
} from '@/components';
import { Form } from 'antd';
import { TNumberQuestionProps } from './mock-tests.types';
import { SelectBigCategory } from '../select';
import { Rule } from 'antd/lib/form';
import validate from '@/utils/validate';
import { validationMessage } from '@/commons/messages-enum';
import { useMemo } from 'react';

const {
  checkMaxLength,
  hasWhiteSpace,
  checkUnique,
  checkNumberQuestions,
  checkBetweenNumberValue,
} = validate;

const NumberQuestion = ({ index, remove }: TNumberQuestionProps) => {
  const rulesQuestionAmount = useMemo(() => {
    const checkLength = () => checkMaxLength('必須正解数', 2);
    const checkSpave = () => hasWhiteSpace('必須正解数');
    const checkNumber = checkNumberQuestions();
    return [
      {
        required: true,
        message: validationMessage.required('必須正解数'),
      },
      checkLength,
      checkSpave,
      checkNumber,
    ] as Array<Rule>;
  }, []);

  const rulesQuestionNumber = useMemo(() => {
    const checkLength = () => checkBetweenNumberValue('出題数', 1, 99);
    const checkSpave = () => hasWhiteSpace('出題数');
    return [
      {
        required: true,
        message: validationMessage.required('出題数'),
      },
      checkLength,
      checkSpave,
    ] as Array<Rule>;
  }, []);

  const rulesCategory = useMemo(() => {
    const checkUniqueCategory = checkUnique('大項目名');
    return [
      {
        required: true,
        message: validationMessage.required('大項目名'),
      },
      checkUniqueCategory,
    ] as Array<Rule>;
  }, []);

  return (
    <div className="flex w-full items-end gap-3">
      <div className="flex-grow grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5 items-start">
        <div className="col-span-1 grid-cols-1 grid gap-2 -mb-5">
          <p className="text-black-kj font-bold">大項目名</p>
          <Form.Item
            name={[index, 'category_id']}
            className="!mb-5"
            rules={rulesCategory}
          >
            <SelectBigCategory placeholder={'選択してください'} />
          </Form.Item>
        </div>
        <div className="col-span-1 grid-cols-1 grid gap-2 -mb-5">
          <p className="text-black-kj font-bold">出題数</p>
          <Form.Item
            name={[index, 'question_amount']}
            className="!mb-5"
            rules={rulesQuestionNumber}
          >
            <InputNumber
              classNames="!w-full !rounded !text-sm"
              size="large"
              hideControls
              maxLength={2}
            />
          </Form.Item>
        </div>
        <div className="col-span-1 grid-cols-1 grid gap-2 -mb-5">
          <p className="text-black-kj font-bold">必須正解数</p>
          <Form.Item
            name={[index, 'require_correct_amount']}
            className="!mb-5"
            rules={rulesQuestionAmount}
          >
            <InputNumber
              classNames="!w-full !rounded !text-sm"
              size="large"
              hideControls
              maxLength={2}
              min={0}
            />
          </Form.Item>
        </div>
      </div>
      <div onClick={() => remove(index)} className="mb-3 cursor-pointer">
        <DeleteIcon />
      </div>
      {/* <Button
        className="!font-normal flex-none mb-1"
      /> */}
    </div>
  );
};

export default NumberQuestion;
