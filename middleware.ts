import { NextRequest, NextResponse } from 'next/server';
// import { TGetMeData } from './services/apis';
// import { ERoleId } from './commons/enums';
// import { teacherAccess } from './commons/routes';
// import { helpers } from './services/apis';
// import { getToken } from 'next-auth/jwt';

export const config = {
  matcher: ['/((?!api|_next/static|favicon.ico|font).*)', '/'],
};

export async function middleware(req: NextRequest) {
  const pathname = req.nextUrl.pathname;
  const unAuthPaths = ['/login', '/404', '/503', '/403'];
  const isUnAuthPath = unAuthPaths?.some((path) => pathname === path);
  const accessToken = req.cookies.get('COOKIE_ACCESS_TOKEN')?.value;

  if (!accessToken) {
    if (isUnAuthPath) {
      return NextResponse.next();
    }
    const url = new URL('/login', req.url);
    return NextResponse.redirect(url);
  } else if (req.nextUrl.pathname === '/login') {
    const url = new URL('/', req.url);
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

// export { default } from 'next-auth/middleware';
