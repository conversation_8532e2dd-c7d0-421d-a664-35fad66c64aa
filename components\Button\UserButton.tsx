import { Button as AntdButton } from 'antd';
import clsx from 'clsx';
import styles from './styles.module.less';
import { TButtonProps } from './Button.types';
import { useMemo } from 'react';

const Button = ({
  id,
  type,
  title,
  size,
  styleType,
  className,
  icon,
  htmlType,
  disabled,
  prefixIcon,
  onClick,
}: TButtonProps) => {
  const content = useMemo(() => {
    if (icon || prefixIcon) {
      return (
        <div className="flex items-center justify-between w-full font-normal">
          <div className={clsx('flex-none', !prefixIcon && 'invisible')}>
            {prefixIcon}
          </div>
          <span className="flex-grow my-1">{title}</span>
          <div className={clsx('flex-none', !icon && 'invisible')}>{icon}</div>
        </div>
      );
    }
    return <span className="flex-grow my-1 font-normal">{title}</span>;
  }, [icon, title, prefixIcon]);

  return (
    <div className={clsx(styles.button, 'Button')}>
      <AntdButton
        id={id}
        size={size}
        type={type}
        htmlType={htmlType}
        className={clsx(styleType, className)}
        onClick={onClick}
        disabled={disabled}
      >
        {content}
      </AntdButton>
    </div>
  );
};

export default Button;
