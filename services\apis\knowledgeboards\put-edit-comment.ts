import { EInquiryIsPublic, ESendToAdmin, ESendToTeacher } from '@/commons/enums';
import ApiService from '@/services/apis';

type TPutEditCommentPaths = {
  id: string;
};
type TPutEditCommentPayload = {
  send_to_admin?: ESendToAdmin;
  send_to_teacher?: ESendToTeacher;
  is_public?: EInquiryIsPublic;
  content?: string;
  _method: string;
};

export type TPutEditCommentMaterials = {
  paths: TPutEditCommentPaths;
  payload: TPutEditCommentPayload;
};

export type TPutEditCommentResponse = unknown;

export const putEditComment = async (
  materials: TPutEditCommentMaterials
): Promise<TPutEditCommentResponse> => {
  const { paths, payload } = materials;
  const response = await ApiService.post(`/comments/${paths.id}`, payload);
  return response.data;
};
