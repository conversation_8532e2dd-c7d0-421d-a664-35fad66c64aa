import { useEffect, useMemo, useState } from 'react';
import { Form, Radio, Tabs } from 'antd';
import { SelectBigCategory, SelectSubCategory } from '../select';
import { Rule } from 'antd/lib/form';
import validate from '@/utils/validate';
import { validationMessage } from '@/commons/messages-enum';
import { TExamSettingFormProps, TQuestionFormProps } from './mock-tests.types';
import { Input, InputNumber } from '@/components';
import { EFolderUpload, EIsActive, EQuestionForm } from '@/commons/enums';
import dynamic from 'next/dynamic';
const EditorBoxForm = dynamic(
  () => import('@/components/EditorBox/EditorBoxInForm'),
  {
    ssr: false,
  }
);
import FormUploadItem from './FormUploadItem';
import QuestionOtherTab from './QuestionOtherTab';
import QuestionMultipleChoice from './QuestionMultipleChoice';
import QuestionSingleChoice from './QuestionSingleChoice';
import { usePrevious } from '@/utils/hooks';

const {
  checkMaxLength,
  hasWhiteSpace,
  checkMaxLengthEditor,
  checkEmptyTextEditorExam,
  extractContent,
  checkBetweenNumberValue,
} = validate;

const QuestionForm = ({
  formInstance,
  invalidTab,
  disable,
  handleSubmit,
}: TQuestionFormProps) => {
  const type = Form.useWatch('type', formInstance);
  const image = Form.useWatch('image', formInstance);
  const content = Form.useWatch('content', formInstance);
  const is_active = Form.useWatch('is_active', formInstance);
  const show_order = Form.useWatch('show_order', formInstance);
  const [activeTab, setActiveTab] = useState<string>('other');
  const previousOrder = usePrevious<number | null>(show_order);
  useEffect(() => {
    if (invalidTab?.value) {
      setActiveTab(invalidTab?.value);
    }
  }, [invalidTab]);

  const rulesCategory = useMemo(() => {
    return [
      {
        required: true,
        message: validationMessage.required('大項目名'),
      },
    ] as Array<Rule>;
  }, []);

  const rulesIsActive = useMemo(
    () => [
      {
        required: true,
        message: validationMessage.required('表示順'),
      },
    ],
    []
  );

  const rulesOrder = useMemo(() => {
    const checkLength = () => checkBetweenNumberValue('出題No.', 1, 9999999999);
    return [
      {
        required: is_active === EIsActive.ENABLE,
        message: validationMessage.required('出題No.'),
      },
      checkLength,
    ];
  }, [is_active]) as Array<Rule>;

  const rulesQuestionNo = useMemo(() => {
    const checkLength = () => checkMaxLength('問題番号', 10);
    const checkSpave = () => hasWhiteSpace('問題番号');
    return [
      {
        required: true,
        message: validationMessage.required('問題番号'),
      },
      checkLength,
      checkSpave,
    ];
  }, []) as Array<Rule>;

  const rulesAllocationPoint = useMemo(() => {
    const checkLength = () => checkMaxLength('配点', 3);
    const checkSpave = () => hasWhiteSpace('配点');
    return [
      {
        required: true,
        message: validationMessage.required('配点'),
      },
      checkLength,
      checkSpave,
    ];
  }, []) as Array<Rule>;

  const rulesQuestionYear = useMemo(() => {
    const checkLength = () => checkMaxLength('出題年度', 50);
    return [checkLength];
  }, []) as Array<Rule>;

  const rulesQuestionTitle = useMemo(() => {
    const checkLength = () => checkMaxLength('問題タイトル', 255);
    return [
      {
        required: true,
        message: validationMessage.required('問題タイトル'),
      },
      checkLength,
    ];
  }, []) as Array<Rule>;

  const rulescContent = useMemo(() => {
    const checkLength = () => checkMaxLengthEditor('問題本文', 1, 65535);
    const checkEmpty = () => checkEmptyTextEditorExam('問題本文');
    if (image) {
      return [checkLength];
    }
    return [checkEmpty, checkLength];
  }, [image]) as Array<Rule>;

  const rulesImage = useMemo(() => {
    if (typeof window === 'undefined' || !window.document) {
      return [];
    }
    const extract = content && extractContent(content);
    return [
      {
        required: !extract,
        message: validationMessage.required_content_or_images(),
      },
    ];
  }, [content]) as Array<Rule>;

  const onValuesChange = (changedValues: any) => {
    const keys = Object.keys(changedValues);
    if (keys.includes('type')) {
      const { type } = formInstance.getFieldsValue();
      const root_type = formInstance.getFieldValue('root_type');
      const sub_questions_edit =
        formInstance.getFieldValue('sub_questions_edit');
      setActiveTab('other');
      if (root_type && sub_questions_edit && type === root_type) {
        formInstance.setFieldValue('sub_questions', sub_questions_edit);
      } else {
        formInstance.setFieldValue('sub_questions', undefined);
      }
    }
    if (keys.includes('image') || keys.includes('content')) {
      setTimeout(() => {
        formInstance.validateFields(['image', 'content']);
      }, 0);
    }
    if (keys.includes('is_active')) {
      if (changedValues?.is_active === EIsActive.DISABLE) {
        formInstance.setFieldValue('show_order', null);
      } else if (previousOrder) {
        formInstance.setFieldValue('show_order', previousOrder);
      }
    }
    if (keys.includes('law_ids')) {
      const { law_ids } = formInstance.getFieldsValue();
      const countDuplicate = law_ids?.reduce(
        (s: Array<number>, law: any, idx: number) => {
          if (!law?.id) {
            return s;
          }
          return [...s, ['law_ids', idx]];
        },
        []
      );
      formInstance.validateFields(countDuplicate);
    }
    if (keys.includes('digital_text_ids')) {
      const { digital_text_ids } = formInstance.getFieldsValue();
      const countDuplicate = digital_text_ids?.reduce(
        (s: Array<number>, law: any, idx: number) => {
          if (!law?.id) {
            return s;
          }
          return [...s, ['digital_text_ids', idx]];
        },
        []
      );
      formInstance.validateFields(countDuplicate);
    }
    if (keys.includes('video_ids')) {
      const { video_ids } = formInstance.getFieldsValue();
      const countDuplicate = video_ids?.reduce(
        (s: Array<number>, law: any, idx: number) => {
          if (!law?.id) {
            return s;
          }
          return [...s, ['video_ids', idx]];
        },
        []
      );
      formInstance.validateFields(countDuplicate);
    }
  };

  const onSubmit = (values: any) => {
    handleSubmit?.(values);
  };

  const tabs = useMemo(() => {
    if (!type) {
      return [];
    }
    if (type === EQuestionForm.SINGLE) {
      return [
        {
          label: '解説・そのた',
          key: 'other',
          children: <QuestionOtherTab disable={disable} />,
        },
        {
          label: '設間',
          key: 'part1',
          children: <QuestionSingleChoice name={['sub_questions', 0]} />,
        },
      ];
    }
    return [
      {
        label: '解説・そのた',
        key: 'other',
        children: <QuestionOtherTab disable={disable} />,
      },
      {
        label: '設間1',
        key: 'part1',
        children: <QuestionMultipleChoice name={['sub_questions', 0]} />,
      },
      {
        label: '設間2',
        key: 'part2',
        children: <QuestionMultipleChoice name={['sub_questions', 1]} />,
      },
      {
        label: '設間3',
        key: 'part3',
        children: <QuestionMultipleChoice name={['sub_questions', 2]} />,
      },
      {
        label: '設間4',
        key: 'part4',
        children: <QuestionMultipleChoice name={['sub_questions', 3]} />,
      },
    ];
  }, [disable, type]);

  return (
    <Form
      className="grid grid-cols-1 gap-5 !-mx-5 bg-white"
      disabled={disable}
      form={formInstance}
      onFinish={onSubmit}
      onValuesChange={onValuesChange}
    >
      <div className=" bg-new-white px-5">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5">
          <div className="col-span-2 grid grid-cols-1 xl:grid-cols-2 gap-x-5">
            <div className="col-span-1 grid-cols-1 grid gap-2">
              <p className="text-black-kj font-bold">問題番号</p>
              <Form.Item name={'question_no'} rules={rulesQuestionNo}>
                <InputNumber
                  classNames="!w-full !rounded !text-sm"
                  size="large"
                  maxLength={10}
                  hideControls
                />
              </Form.Item>
            </div>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">大項目名</p>
            <Form.Item name={'category_id'} rules={rulesCategory}>
              <SelectBigCategory placeholder={'選択してください'} />
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">出題No.</p>
            <Form.Item name={'show_order'} rules={rulesOrder}>
              <InputNumber
                classNames="!w-full !rounded !text-sm"
                size="large"
                maxLength={4}
                hideControls
                disabled={is_active !== EIsActive.ENABLE || disable}
                stringMode
              />
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">出題年度</p>
            <Form.Item name={'question_year'} rules={rulesQuestionYear}>
              <Input
                classNames="!w-full !rounded !text-sm !py-[9px]"
                size="large"
                maxLength={50}
              />
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">配点</p>
            <Form.Item name={'allocation_point'} rules={rulesAllocationPoint}>
              <InputNumber
                classNames="!w-full !rounded !text-sm"
                size="large"
                maxLength={3}
                hideControls
              />
            </Form.Item>
          </div>
        </div>
        <div className="col-span-1 grid-cols-1 grid gap-2">
          <p className="text-black-kj font-bold">問題タイトル</p>
          <Form.Item name={'title'} rules={rulesQuestionTitle}>
            <Input
              classNames="w-full !rounded !text-sm !py-[9px]"
              size="large"
              maxLength={255}
            />
          </Form.Item>
        </div>
        <div className="flex gap-5 items-start justify-center flex-col xl:flex-row">
          <div className="flex-grow grid-cols-1 grid gap-2">
            <p className="text-black-kj !font-bold">問題本文</p>
            <Form.Item name={'content'} rules={rulescContent}>
              <EditorBoxForm disable={disable} />
            </Form.Item>
          </div>
          <div className="flex-none grid-cols-1 grid gap-2">
            <p className="text-black-kj !font-bold">アップロード</p>
            <Form.Item name={'image'} rules={rulesImage} className="w-75">
              <FormUploadItem
                accept={'.png,.jpeg,.jpg'}
                type={['png', 'jpeg', 'jpg']}
                name="image"
                label="アップロード"
                disable={disable}
                folder={EFolderUpload.QUESTIONS}
              />
            </Form.Item>
          </div>
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5">
          <div className="col-span-1 grid-cols-1 grid gap-2 !font-bold">
            <p className="text-black-kj">有効・無効</p>
            <Form.Item
              name="is_active"
              rules={rulesIsActive}
              initialValue={EIsActive.ENABLE}
            >
              <Radio.Group>
                <Radio className="font-bold" value={EIsActive.ENABLE}>
                  有効
                </Radio>
                <Radio className="font-bold" value={EIsActive.DISABLE}>
                  無効
                </Radio>
              </Radio.Group>
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2 !font-bold">
            <p className="text-black-kj">出題形式</p>
            <Form.Item
              name="type"
              rules={rulesIsActive}
              initialValue={EQuestionForm.SINGLE}
            >
              <Radio.Group>
                <Radio className="font-bold" value={EQuestionForm.SINGLE}>
                  単一設問
                </Radio>
                <Radio className="font-bold" value={EQuestionForm.MULTIPLE}>
                  複数設問
                </Radio>
              </Radio.Group>
            </Form.Item>
          </div>
        </div>
      </div>
      <div className="w-full">
        <Tabs
          type="card"
          items={tabs}
          activeKey={activeTab}
          className="question_tab"
          onChange={setActiveTab}
        />
      </div>
    </Form>
  );
};

export default QuestionForm;
