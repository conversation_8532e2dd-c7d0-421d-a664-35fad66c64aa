import { EIsCreating } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPostUploadExerciseQuestionsExplainationImagesPayload = {
  file_zip: File;
  is_creating: EIsCreating;
};

export type TPostUploadExerciseQuestionsExplainationImagesMaterials = {
  payload: TPostUploadExerciseQuestionsExplainationImagesPayload;
};

export type TPostUploadExerciseQuestionsExplainationImagesResponse = unknown;

export const postUploadExerciseQuestionsExplainationImages = async (
  materials: TPostUploadExerciseQuestionsExplainationImagesMaterials
): Promise<TPostUploadExerciseQuestionsExplainationImagesResponse> => {
  const { payload } = materials;
  const bodyFormData = new FormData();
  bodyFormData.append('file_zip', payload?.file_zip);
  bodyFormData.append('is_creating', `${payload?.is_creating}`);
  const response = await ApiService.post('/upload/zip-image/exercise-question-explanation-images', bodyFormData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
};
