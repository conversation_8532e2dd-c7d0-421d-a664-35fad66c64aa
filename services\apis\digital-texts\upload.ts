import { EIsCreating } from '@/commons/enums';
import { TDigital, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TPostUploadDigitalTextsPayload = {
  file_zip: File;
  is_creating: EIsCreating;
};

export type TPostUploadDigitalTextsMaterials = {
  payload: TPostUploadDigitalTextsPayload;
};

export type TPostUploadDigitalTextsResponse = TListData<TDigital>;

export const postUploadDigitalTexts = async (
  materials: TPostUploadDigitalTextsMaterials
): Promise<TPostUploadDigitalTextsResponse> => {
  const { payload } = materials;
  const bodyFormData = new FormData();
  bodyFormData.append('file_zip', payload?.file_zip);
  bodyFormData.append('is_creating', `${payload?.is_creating}`);
  const response = await ApiService.post('/upload/zip-image/digital-text-images', bodyFormData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
};
