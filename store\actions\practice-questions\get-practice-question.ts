import {
  TGetPracticeQuestionMaterials,
  TGetPracticeQuestionResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetPracticeQuestionAction {
  GET_PRACTICE_QUESTION = 'GET_PRACTICE_QUESTION',
  GET_PRACTICE_QUESTION_REQUEST = 'GET_PRACTICE_QUESTION_REQUEST',
  GET_PRACTICE_QUESTION_SUCCESS = 'GET_PRACTICE_QUESTION_SUCCESS',
  GET_PRACTICE_QUESTION_FAILED = 'GET_PRACTICE_QUESTION_FAILED',
}

// TYPES

export type TGetPracticeQuestionRequest = {
  type: EGetPracticeQuestionAction.GET_PRACTICE_QUESTION_REQUEST;
  payload: {
    materials: TGetPracticeQuestionMaterials;
    successCallback?: (response: TGetPracticeQuestionResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetPracticeQuestionSuccess = {
  type: EGetPracticeQuestionAction.GET_PRACTICE_QUESTION_SUCCESS;
  payload: { response?: TGetPracticeQuestionResponse };
};

export type TGetPracticeQuestionFailed = {
  type: EGetPracticeQuestionAction.GET_PRACTICE_QUESTION_FAILED;
};

export const getPracticeQuestionAction = {
  request: createActionCreator(
    EGetPracticeQuestionAction.GET_PRACTICE_QUESTION_REQUEST,
    (resolve) =>
      (
        materials: TGetPracticeQuestionMaterials,
        successCallback?: (response: TGetPracticeQuestionResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetPracticeQuestionRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetPracticeQuestionAction.GET_PRACTICE_QUESTION_SUCCESS,
    (resolve) =>
      (response?: TGetPracticeQuestionResponse): TGetPracticeQuestionSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetPracticeQuestionAction.GET_PRACTICE_QUESTION_FAILED,
    (resolve) =>
      (error: unknown): TGetPracticeQuestionFailed =>
        resolve({ error })
  ),
};
