import { TGetVideosMaterials, TGetVideosResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetVideosAction {
  GET_VIDEOS = 'GET_VIDEOS',
  GET_VIDEOS_REQUEST = 'GET_VIDEOS_REQUEST',
  GET_VIDEOS_SUCCESS = 'GET_VIDEOS_SUCCESS',
  GET_VIDEOS_FAILED = 'GET_VIDEOS_FAILED',
}

// TYPES

export type TGetVideosRequest = {
  type: EGetVideosAction.GET_VIDEOS_REQUEST;
  payload: {
    materials: TGetVideosMaterials;
    successCallback?: (response: TGetVideosResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetVideosSuccess = {
  type: EGetVideosAction.GET_VIDEOS_SUCCESS;
  payload: { response?: TGetVideosResponse };
};

export type TGetVideosFailed = {
  type: EGetVideosAction.GET_VIDEOS_FAILED;
};

export const getVideosAction = {
  request: createActionCreator(
    EGetVideosAction.GET_VIDEOS_REQUEST,
    (resolve) =>
      (
        materials: TGetVideosMaterials,
        successCallback?: (response: TGetVideosResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetVideosRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetVideosAction.GET_VIDEOS_SUCCESS,
    (resolve) =>
      (response?: TGetVideosResponse): TGetVideosSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetVideosAction.GET_VIDEOS_FAILED,
    (resolve) =>
      (error: unknown): TGetVideosFailed =>
        resolve({ error })
  ),
};
