import { TGetDetailExerciseResponse, getDetailExercises } from '@/services/apis';
import { getDetailExerciseAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getDetailExercisesSaga(
    action: ActionType<typeof getDetailExerciseAction.request>
): Generator {
    const { materials, successCallback, failedCallback } = action.payload;
    try {
        const response = yield call(getDetailExercises, materials);
        const detailExerciseResponse = response as TGetDetailExerciseResponse;
        yield put(getDetailExerciseAction.success(detailExerciseResponse));
        successCallback?.(detailExerciseResponse);
    } catch (err) {
        yield put(getDetailExerciseAction.failure(err));
        failedCallback?.(err);
    }
}
