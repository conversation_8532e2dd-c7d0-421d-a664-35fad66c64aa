export enum EResponseCode {
  OK = 200,
  CREATED = 201,
  ACCEPTED = 202,
  NON_AUTHORITATIVE_INFORMATION = 203,
  NO_CONTENT = 204,
  RESET_CONTENT = 205,
  PARTIAL_CONTENT = 206,
  MULTI_STATUS = 207,
  ALREADY_REPORTED = 208,
  I_AM_USED = 226,

  MULTIPLE_CHOICE = 300,
  MOVED_PERMANENTLY = 301,
  FOUND = 302,
  SEE_OTHER = 303,
  NOT_MODIFIED = 304,
  TEMPORARY_REDIRECT = 307,
  PERMANENT_REDIRECT = 308,

  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  PAYMENT_REQUIRED = 402,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  NOT_ACCEPTABLE = 406,
  PROXY_AUTHENTICATION_REQUIRED = 407,
  REQUEST_TIMEOUT = 408,
  CONFLICT = 409,
  GONE = 410,
  LENGTH_REQUIRED = 411,
  PRECONDITION_FAILED = 412,
  PAYLOAD_TOO_LARGE = 413,
  URI_TOO_LONG = 414,
  UNSUPPORTED_MEDIA_TYPE = 415,
  RANGE_NOT_SATISFIABLE = 416,
  EXPECTATION_FAILED = 417,
  I_AM_A_TEAPOT = 418,
  MISDIRECTED_REQUEST = 421,
  UNPROCESSABLE_ENTITY = 422,
  LOCKED = 423,
  FAILED_DEPENDENCY = 424,
  TOO_EARLY = 425,
  UPGRADE_REQUIRED = 426,
  PRECONDITION_REQUIRED = 428,
  TOO_MANY_REQUESTS = 429,
  REQUEST_HEADER_FIELDS_TOO_LARGE = 431,
  UNAVAILABLE_FOR_LEGAL_REASONS = 451,

  INTERNAL_SERVER_ERROR = 500,
  NOT_IMPLEMENTED = 501,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
  HTTP_VERSION_NOT_SUPPORTED = 505,
  VARIANT_ALSO_NEGOTIATES = 506,
  INSUFFICIENT_STORAGE = 507,
  LOOP_DETECTED = 508,
  NOT_EXTENDED = 510,
  NETWORK_AUTHENTICATION_REQUIRED = 511,
}

export enum ETypeNotification {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  INFO = 'info',
}

export enum EPlacement {
  TOP = 'top',
  LEFT = 'left',
  RIGHT = 'right',
  BOTTOM = 'bottom',
  TOP_LEFT = 'topLeft',
  TOP_RIGHT = 'topRight',
  BOTTOM_LEFT = 'bottomLeft',
  BOTTOM_RIGHT = 'bottomRight',
  LEFT_TOP = 'leftTop',
  LEFT_BOTTOM = 'leftBottom',
  RIGHT_TOP = 'rightTop',
  RIGHT_BOTTOM = 'rightBottom',
}

export enum ESize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export enum EIsActive {
  ENABLE = 1,
  DISABLE = 2,
}

export enum EVideoPlan {
  NOT_VIDEO = 1,
  HAVE_VIDEO = 2,
}

export enum EQuestionForm {
  SINGLE = 1,
  MULTIPLE = 2,
}

export enum ETimeoutDebounce {
  SEARCH = 200,
}

export enum EFormatDateTime {
  YYYY_MM_DD_HH_MM = 'YYYY/MM/DD HH:mm',
  YYYY_MM_DD = 'YYYY/MM/DD',
}

export enum ERoleId {
  ADMIN = 1,
  TEACHER = 2,
}

export enum EIsCreating {
  ADD = 1,
  EDIT = 0,
}

export enum ESetingStatus {
  DONE = 1,
  DOING = 2,
}

export enum ENeedCheckWithSetting {
  NEED = 1,
  DONT_NEED = 0,
}

export enum EFolderUpload {
  QUESTIONS = 'questions',
  QUESTIONS_EXPLANATIONS = 'question-explanations',
  EXERCISE_QUESTIONS = 'exercise-questions',
  EXERCISE_QUESTIONS_EXPLANATIONS = 'exercise-question-explanations',
}

export enum EInquiryIsPublic {
  PUBLIC = 1,
  PRIVATE = 2,
}

export enum ESendToTeacher {
  NO = 1,
  YES = 2,
}

export enum ESendToAdmin {
  NO = 1,
  YES = 2,
}

export enum ETeacherReplyStatus {
  NO_REPLY = 1,
  REPLIED = 2,
  NOT_RELATE = 3,
}

export enum EAdminReplyStatus {
  NO_REPLY = 1,
  REPLIED = 2,
  NOT_RELATE = 3,
}

export enum EUserRoleId {
  USER = 1,
  TEACHER = 2,
}

export enum EReplyType {
  REPLY_INQUIRY = 1,
  REPLY_COMMENT = 2,
  NO_REPLY = 3,
}

export enum EQuestionType {
  EXERCISE = 1,
  PRACTICE = 2,
  EXAM = 3,
}

export enum ESortType {
  DESC = 'desc',
  ASC = 'asc',
}

export enum ENumberQuestion {
  SINGLE = 1,
  MULTIPLE = 4,
}

export enum ENumberChoice {
  SINGLE = 10,
  MULTIPLE = 10,
}

export enum ERegisterType {
  EC_SITE = 1,
  ADMIN_CREATE = 2,
}
