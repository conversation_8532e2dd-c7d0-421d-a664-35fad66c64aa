import { ERoleId } from '@/commons/enums';
import { AccuracyExport } from '@/containers';
import helpers from '@/services/helpers';
import { GetServerSideProps } from 'next';

const AccuracyPage = () => {
  return (
    <div className="mx-12 my-4">
      <h1 className="text-lg font-bold text-black-kj">問題別正解率出力</h1>
      <div className="bg-alice-blue-light mt-4 py-4 px-5 rounded-md">
        <AccuracyExport />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default AccuracyPage;
