import { TListData, TOptionCategory } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetSelectSubCategoriesParams = {
  page: number;
  category_id: string;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetSelectSubCategoriesMaterials = {
  params?: TGetSelectSubCategoriesParams;
};

export type TGetSelectSubCategoriesResponse = TListData<TOptionCategory>;

export const getSelectSubCategories = async (
  materials: TGetSelectSubCategoriesMaterials
): Promise<TGetSelectSubCategoriesResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/select2/sub-categories', { params });
  return response.data;
};
