import { TGetExerciseResponse } from '@/services/apis';
import { EGetExersiceAction, TGetExcerciseSuccess } from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TExerciseState {
  getExerciseResponse?: TGetExerciseResponse;
}

const initialState: TExerciseState = {
  getExerciseResponse: undefined,
};

const exerciseSlice = createSlice({
  name: 'exercise-slice',
  initialState,
  reducers: {
    resetExerciseSlice: () => initialState,
  },
  extraReducers: {
    [EGetExersiceAction.GET_EXERCISE_SUCCESS]: (
      state: TExerciseState,
      action: TGetExcerciseSuccess
    ): TExerciseState => ({
      ...state,
      getExerciseResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: excerciseReducer,
  actions: { resetExerciseSlice },
} = exerciseSlice;

export { excerciseReducer, resetExerciseSlice };
