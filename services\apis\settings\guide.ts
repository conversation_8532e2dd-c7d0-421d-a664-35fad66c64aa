import ApiService from '@/services/apis';

export type TPostPayloadGuidanceSettingsMaterials = {
  description: string;
  is_public: number;
};

export const postGuidanceSettings = async (
  payload: TPostPayloadGuidanceSettingsMaterials
) => {
  const response = await ApiService.post('/settings/guide', payload);
  return response.data;
};

export const getGuidanceSettings = async () => {
  const response = await ApiService.get('/settings/guide');
  return response.data;
};
