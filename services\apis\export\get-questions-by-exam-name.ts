import ApiService from '@/services/apis';

export type TGetExportQuestionsByExamNameParams = {
  name?: string;
  is_actives?: Array<number>;
};

export type TGetExportQuestionsByExamNameMaterials = {
  params?: TGetExportQuestionsByExamNameParams;
};

export type TGetExportQuestionsByExamNameResponse = Blob;

export const getExportQuestionsByExamName = async (
  materials: TGetExportQuestionsByExamNameMaterials
): Promise<TGetExportQuestionsByExamNameResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/export/exams', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
