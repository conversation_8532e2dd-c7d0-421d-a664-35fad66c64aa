import { EIsCreating } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPostUploadQuestionsImagesPayload = {
  file_zip: File;
  is_creating: EIsCreating;
};

export type TPostUploadQuestionsImagesMaterials = {
  payload: TPostUploadQuestionsImagesPayload;
};

export type TPostUploadQuestionsImagesResponse = unknown;

export const postUploadQuestionsImages = async (
  materials: TPostUploadQuestionsImagesMaterials
): Promise<TPostUploadQuestionsImagesResponse> => {
  const { payload } = materials;
  const bodyFormData = new FormData();
  bodyFormData.append('file_zip', payload?.file_zip);
  bodyFormData.append('is_creating', `${payload?.is_creating}`);
  const response = await ApiService.post('/upload/zip-image/question-images', bodyFormData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
};
