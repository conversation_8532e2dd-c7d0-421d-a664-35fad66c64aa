import ApiService from '@/services/apis';

export type TDataUpdate = {
  question_id: number;
  show_order: number;
};
export type TPostUpdateQuestionsPayload = {
  questions: Array<TDataUpdate>;
};

export type TPostUpdateQuestionsPaths = {
  exam_id: number;
};

export type TPostUpdateQuestionsMaterials = {
  payload: TPostUpdateQuestionsPayload;
  paths: TPostUpdateQuestionsPaths;
};

export type TPostUpdateQuestionsResponse = unknown;

export const postUpdateQuestions = async (
  materials: TPostUpdateQuestionsMaterials
): Promise<TPostUpdateQuestionsResponse> => {
  const { payload, paths } = materials;
  const response = await ApiService.post(
    `/exams/${paths.exam_id}/update-questions`,
    payload
  );
  return response.data;
};
