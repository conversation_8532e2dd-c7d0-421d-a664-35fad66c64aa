{"name": "nextjs-less-tailwind-antd", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^1.9.3", "@types/node": "18.14.2", "@types/react": "18.0.28", "@types/react-dom": "18.0.11", "antd": "^4.24.8", "autoprefixer": "^10.4.13", "autosize": "^6.0.1", "axios": "^1.3.4", "babel-plugin-import": "^1.13.6", "clsx": "^1.2.1", "deox": "^3.3.1", "eslint": "8.35.0", "eslint-config-next": "^13.2.2", "jodit-react": "^1.3.39", "lint-staged": "^13.1.2", "moment": "^2.29.4", "moment-timezone": "^0.5.41", "next": "^13.2.1", "next-auth": "^4.20.1", "next-compose-plugins": "^2.2.1", "next-plugin-antd-less": "^1.8.0", "next-translate": "^2.6.2", "postcss": "^8.4.21", "react": "18.2.0", "react-cookie": "^4.1.1", "react-dom": "18.2.0", "react-player": "^2.12.0", "react-redux": "^8.0.5", "redux-saga": "^1.2.2", "sharp": "^0.32.6", "tailwindcss": "^3.2.7", "typescript": "4.9.5"}, "devDependencies": {"@tailwindcss/line-clamp": "^0.4.2", "@types/autosize": "^4.0.1", "eslint-config-prettier": "^8.6.0", "next-translate-plugin": "^2.0.2", "postcss-import": "^15.1.0", "prettier": "^2.8.4", "tailwind-scrollbar-hide": "^1.1.7"}}