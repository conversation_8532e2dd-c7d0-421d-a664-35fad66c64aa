import { TGetVideosResponse, postVideo } from '@/services/apis';
import { postVideoAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* postVideoSaga(
  action: ActionType<typeof postVideoAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(postVideo, materials);
    const postVideoResponse = response as TGetVideosResponse;
    yield put(postVideoAction.success(postVideoResponse));
    successCallback?.(postVideoResponse);
  } catch (err) {
    yield put(postVideoAction.failure(err));
    failedCallback?.(err);
  }
}
