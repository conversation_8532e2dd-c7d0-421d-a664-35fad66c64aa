import {
  EIsActive,
  EPlacement,
  ETypeNotification,
  EVideoPlan,
} from '@/commons/enums';
import { TUser } from '@/commons/types';
import {
  Button,
  ColumnType,
  EButtonStyleType,
  EditIcon,
  Pagination,
  Table,
} from '@/components';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import FilterUser from './FilterUser';
import { TFilterUser } from './user.types';
import { getUsersAction } from '@/store/actions';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import moment from 'moment';
import { getExportUsers } from '@/services/apis';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import { EXCEPTION } from '@/commons/messages-enum';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import { resetUserSlice } from '@/store/slices/users';

const ListUser = ({ query }: { query: TFilterUser }) => {
  const router = useRouter();
  const [filter, setFilter] = useState<TFilterUser>();
  const dispatch = useDispatch();

  const { getUsersResponse: users } = useSelector(
    (state: RootState) => state.user
  );
  const { data, meta } = users || {};

  const setDefaultFilter = () => {
    const defaultFitler = {
      is_actives: [EIsActive.ENABLE],
      video_plans: [EVideoPlan.NOT_VIDEO, EVideoPlan.HAVE_VIDEO],
      plans: [1, 2],
      register_types: [1, 3],
    };
    setFilter(defaultFitler);
  };

  useEffect(() => {
    return () => {
      dispatch(resetUserSlice());
    };
  }, [dispatch]);

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const getActions = useCallback(
    (element: TUser) => {
      return (
        <div className="flex items-center gap-5">
          <Link
            href={`/users/${element.id}/edit`}
            onClick={() => {
              dispatch(setCallBackUrl(router.asPath));
            }}
          >
            <EditIcon />
          </Link>
        </div>
      );
    },
    [dispatch, router.asPath]
  );
  const columns = useMemo(
    () =>
      [
        {
          title: 'ログインID',
          dataIndex: 'login_id',
          key: 'login_id',
          // render: (element) => element?.login_id || '...',
          render: (element) => (
            <div className="max-w-30 break-all">
              {element?.login_id || '...'}
            </div>
          ),
        },
        {
          title: '氏名',
          dataIndex: 'username',
          key: 'username',
          render: (element) => (
            <div className="max-w-25 break-all">
              {/* <div className="max-w-25 2xl:max-w-50 truncate"> */}
              {element?.username || '...'}
            </div>
          ),
        },
        {
          title: '氏名カナ',
          dataIndex: 'username_kana',
          key: 'username_kana',
          render: (element) => (
            <div className="max-w-25 break-all">
              {/* <div className="max-w-25 2xl:max-w-50 truncate"> */}
              {element?.username_kana || '...'}
            </div>
          ),
        },
        {
          title: '権限',
          dataIndex: 'role_id',
          key: 'role_id',
          render: (element) => (
            <div className="max-w-25 truncate">
              {element?.role_id === 1 ? 'ユーザー' : '講師'}
            </div>
          ),
        },
        {
          title: 'メールアドレス',
          dataIndex: 'email',
          key: 'email',
          render: (element) => (
            <div className="max-w-25 break-all">
              {/* <div className="max-w-25 2xl:max-w-50 truncate"> */}
              {element?.email || '...'}
            </div>
          ),
        },
        {
          title: '利用開始日',
          dataIndex: 'plan_start_date',
          key: 'plan_start_date',
          render: (element) =>
            moment(element?.plan_start_date).format('YYYY-MM-DD') || '...',
        },
        {
          title: '利用終了日',
          dataIndex: 'plan_end_date',
          key: 'plan_end_date',
          render: (element) =>
            moment(element?.plan_end_date).format('YYYY-MM-DD') || '...',
        },
        {
          title: '利用タイプ',
          dataIndex: 'plan',
          key: 'plan',
          render: (element) => {
            if (!element) {
              return '';
            }
            const { plan } = element;
            if (plan === 1) {
              return '無料';
            } else if (plan === 2) {
              return '有料';
            }
            return '';
          },
        },
        {
          title: 'ステータス',
          dataIndex: 'plan_status',
          key: 'plan_status',
          render: (element) => {
            switch (element?.plan_status) {
              case 1:
                return '利用前';
              case 2:
                return '利用中';
              case 3:
                return '利用終了';
              default:
                return '...';
            }
          },
        },
        {
          title: '対策動画',
          dataIndex: 'video_plan',
          key: 'video_plan',
          render: (element) => (
            <div className="max-w-25">
              {element?.video_plan === 1 ? 'なし' : 'あり'}
            </div>
          ),
        },
        {
          title: '購入方法',
          dataIndex: 'register_type',
          key: 'register_type',
          render: (element) => {
            if (!element) {
              return '';
            }
            const { register_type } = element;
            if (register_type === 1) {
              return 'EC';
            } else if (register_type === 2) {
              return '管理';
            } else if (register_type === 3) {
              return 'アプリ';
            }
            return null;
          },
        },
        {
          title: '有効・無効',
          dataIndex: 'is_active',
          key: 'is_active',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TUser>>,
    [getActions]
  );

  const fetchUsersData = useCallback(
    (page: number, filter?: TFilterUser) => {
      const username =
        filter?.username?.trim().toLocaleLowerCase() || undefined;
      const is_actives = filter?.is_actives as Array<number>;
      const video_plans = filter?.video_plans as Array<number>;
      const plan_status = filter?.plan_status;
      const email = filter?.email;
      const role_id = filter?.role_id;
      const register_types = filter?.register_types;
      const plans = filter?.plans;

      const params = {
        page,
        username,
        is_actives,
        video_plans,
        plan_status,
        email,
        role_id,
        plans,
        register_types,
      };
      dispatch(getUsersAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ query }, undefined, {
        shallow: true,
      });
      fetchUsersData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter, fetchUsersData]);

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(router.asPath));
    router.push('/users/create');
  };

  const handleRedirectToUpload = () => {
    dispatch(setCallBackUrl(router.asPath));
    router.push('/users/upload');
  };

  const handleChangePage = (page: number) => {
    fetchUsersData(page, filter);
  };

  const handleExportData = async () => {
    // const changeTypeRegisterType = (value: boolean | undefined) => {
    //   if (value) return 1;
    //   return 2;
    // };
    const username = filter?.username?.trim() || undefined;
    const video_plans = filter?.video_plans as Array<number>;

    const plan_status = filter?.plan_status
      ? Number(filter?.plan_status)
      : undefined;
    const role_id = filter?.role_id ? Number(filter?.role_id) : undefined;
    const email = filter?.email?.trim() || undefined;
    // const register_type =
    //   filter?.register_type === true || filter?.register_type === false
    //     ? changeTypeRegisterType(filter?.register_type)
    //     : undefined;
    const is_actives = filter?.is_actives as Array<number>;
    const register_types = filter?.register_types;
    const plans = filter?.plans;

    const params = {
      username,
      video_plans,
      plan_status,
      role_id,
      email,
      is_actives,
      register_types,
      plans,
    };

    try {
      const response = await getExportUsers({ params });
      const fileName = `Kojiro_unkan_users_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
          <FilterUser
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        <div className="self-end flex items-center gap-2">
          <Button
            onClick={handleRedirectToUpload}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="アップロード"
            className="!font-normal"
          />
          <Button
            onClick={handleExportData}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="出力"
            className="!font-normal"
          />
          <Button
            onClick={handleRedirectToAdd}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="新規登録"
            className="!font-normal"
          />
        </div>
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            meta?.total || 0
          }件`}</h2>
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<TUser> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default ListUser;
