import { EPlacement, ERoleId, ETypeNotification } from '@/commons/enums';
import { EXCEPTION } from '@/commons/messages-enum';
import { TCategory } from '@/commons/types';
import { Breadcrumbs } from '@/components';
import { CategoryForm } from '@/containers';
import {
  TGetCategoryProps,
  getCategory,
  helpers,
  putUpdateCategory,
} from '@/services/apis';
import { RootState } from '@/store/configureStore';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

const CategoryEditPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [category, setCategory] = useState<TCategory>();
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const fetchCategory = async () => {
    const payload: TGetCategoryProps = {
      id: id as string,
    };
    try {
      const data = await getCategory(payload);
      setCategory(data);
    } catch (error: any) {
      if (error?.request.status !== 503) {
        showNotification(
          ETypeNotification.ERROR,
          error?.data?.message
            ? error?.data?.message
            : EXCEPTION.ACTION_SUCCESS,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
  };

  const handleSubmit = async (values: TCategory) => {
    const payload = {
      id: Number(id as string),
      is_active: values.is_active,
      name: values.name,
      show_order: values.show_order,
    };
    try {
      const data = await putUpdateCategory(payload);
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      if (callBackUrl) {
        router.push(callBackUrl);
        return;
      }
      router.push('/category');
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  useEffect(() => {
    fetchCategory();
  }, []);

  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">大項目編集</div>
      <CategoryForm
        handleSubmit={handleSubmit}
        intinitalValues={category}
        isUpdate={true}
      />
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default CategoryEditPage;
