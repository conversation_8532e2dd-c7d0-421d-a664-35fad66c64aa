import React, { useEffect, useMemo, useRef, useState } from 'react';
import JoditEditor from 'jodit-react';

const buttonsConfig = [
  'source',
  '|',
  'bold',
  'italic',
  'underline',
  '|',
  'font',
  'fontsize',
  '|',
  '|',
  'left',
  'center',
  'right',
  'justify',
  '|',
  'hr',
  'fullsize',
  'brush',
  'link',
  '|',
  'preview',
];

const EditorBoxForm = (props: any) => {
  const { placeholder, className, value, onChange, height, disable } = props;
  const editor = useRef(null);

  useEffect(() => {
    if (value) {
    }
  }, [value]);

  const config = useMemo(
    () => ({
      defaultMode: 2,
      readonly: disable,
      height: height || '300px',
      width: '100%',
      buttons: buttonsConfig,
      uploader: { insertImageAsBase64URI: true },
      toolbarAdaptive: false,
      style: {
        fontSize: '18px',
        background: '#ffffff',
        color: disable ? '#aaaaaa' : '#000000',
      },
      placeholder: placeholder || '',
      limitChars: 65535,
      cleanHTML: {
        fillEmptyParagraph: false,
        removeEmptyElements: true,
      },
      showCharsCounter: false,
      autofocus: false,
      link: {
        followOnDblClick: true,
        modeClassName: 'selected',
        noFollowCheckbox: false,
        openInNewTabCheckbox: true,
      },
    }),
    [disable, height, placeholder]
  );

  return (
    <div className={className}>
      {useMemo(
        () => (
          <JoditEditor
            className="text-editor"
            value={value}
            ref={editor}
            config={config}
            onChange={onChange}
          />
        ),
        []
      )}
    </div>
  );
};

export default EditorBoxForm;
