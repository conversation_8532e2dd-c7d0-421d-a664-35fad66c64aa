import { Button, EButtonStyleType } from '@/components';
import { SelectBigCategory } from '../select';
import { TOptionCategory, TOptionSub } from '@/commons/types';
import { useEffect, useState } from 'react';
import SelectDigital from '../select/SelectDigitalText';

const SearchDigital = ({
  value,
  onChangeDigital,
  onClose,
  bottomTitle,
  bottomPlace,
}: {
  value?: TOptionSub;
  onClose?: () => void;
  onChangeDigital: (value: TOptionSub) => void;
  bottomTitle?: string;
  bottomPlace?: string;
}) => {
  const [categoryId, setCategoryId] = useState<string>();
  const [digital, setDigital] = useState<TOptionCategory>();

  useEffect(() => {
    if (!value) {
      setCategoryId(undefined);
      setDigital(undefined);
    } else {
      setDigital(value);
      setCategoryId(value.category_id);
    }
  }, [value]);

  const selectDigital = () => {
    if (digital && categoryId) {
      onChangeDigital({ ...digital, category_id: categoryId });
    }
  };
  const selectCategory = (id?: string) => {
    setCategoryId(id);
    setDigital(undefined);
  };
  return (
    <div className="grid grid-cols-1 gap-5 w-125 h-56">
      <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
        <p className="text-black-kj font-bold">大項目名</p>
        <SelectBigCategory onChange={selectCategory} value={categoryId} />
      </div>
      <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
        <p className="text-black-kj font-bold">
          {bottomTitle || 'デジタルテキスト名'}
        </p>
        <SelectDigital
          category_id={categoryId}
          value={digital?.id || ''}
          onChange={setDigital}
          className="w-full"
          placeholder={bottomPlace}
        />
      </div>
      <div className="flex justify-center gap-5">
        <Button
          title="戻る"
          className="self-center"
          styleType={EButtonStyleType.OUTLINE_PRIMARY}
          onClick={onClose}
        />
        <Button
          disabled={!digital}
          title="追加"
          className="self-center"
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          onClick={selectDigital}
        />
      </div>
    </div>
  );
};

export default SearchDigital;
