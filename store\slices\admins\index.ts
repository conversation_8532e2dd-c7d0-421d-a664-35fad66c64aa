import { TGetAdminsResponse } from '@/services/apis';
import { EGetAdminsAction, TGetAdminsSuccess } from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TAdminState {
  getAdminsResponse?: TGetAdminsResponse;
}

const initialState: TAdminState = {
  getAdminsResponse: undefined,
};

const adminSlice = createSlice({
  name: 'category-slice',
  initialState,
  reducers: {
    resetAdminSlice: () => initialState,
  },
  extraReducers: {
    [EGetAdminsAction.GET_ADMINS_SUCCESS]: (
      state: TAdminState,
      action: TGetAdminsSuccess
    ): TAdminState => ({
      ...state,
      getAdminsResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: adminReducer,
  actions: { resetAdminSlice },
} = adminSlice;

export { adminReducer, resetAdminSlice };
