import { ReactNode } from 'react';

export type TConfirmModalProps = {
  visible: boolean;
  content: string | ReactNode;
  classNames?: string;
  centered?: boolean;
  cancelContent?: string;
  submitContent?: string;
  onClose?: () => void;
  onConfirm?: (catagory_id?: any, value?: any) => void;
  value?: any
};

export type TCustomerModalProps = {
  className?: string;
  wrapClassName?: string;
  visible: boolean;
  width?: string | number;
  centered?: boolean;
  cancelButton?: ReactNode;
  confirmButton?: ReactNode;
  title?: string;
  subtitle?: string;
  closeable?: boolean;
  zIndex?: number;
  hideFooter?: boolean;
  onClose?: () => void;
  onSubmit?: () => void;
  children?: ReactNode;
  closable?: boolean;
  headerClass?: string;
};
