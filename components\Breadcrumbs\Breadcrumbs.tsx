import clsx from 'clsx';
import Link from 'next/link';
import { Fragment } from 'react';
import { TBreadcrumbsProps } from './Breadcrumbs.types';

const Breadcrumbs = ({ className, items }: TBreadcrumbsProps) => {
  const { length } = items;
  return (
    <div className={clsx('Breadcrumbs', className)}>
      <ol className="flex items-center justify-start">
        {items.map((item, idx) => (
          <li key={item.link}>
            {idx < length - 1 ? (
              <Fragment>
                <Link
                  href={item.link}
                  className="text-sm text-black font-bold hover:text-black hover:underline"
                >
                  {item.title}
                </Link>
                <span className="mx-1">/</span>
              </Fragment>
            ) : (
              <h3 className="text-sm text-black font-bold"> {item.title}</h3>
            )}
          </li>
        ))}
      </ol>
    </div>
  );
};

export default Breadcrumbs;
