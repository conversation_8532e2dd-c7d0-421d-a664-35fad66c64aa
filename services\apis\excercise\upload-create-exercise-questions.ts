import ApiService from '@/services/apis';

export type TUploadCreateExerciseQuestions = {
  is_preview: number;
  file: File;
};
export type TUploadCreateExerciseQuestionsResponse = any;
// export type TUploadUsersResponse = {
//   line_no?: number;
//   login_id: string;
//   username: string;
//   username_kana: string;
//   email: string;
//   password: string;
//   role_id: number;
//   plan_start_date: string;
//   plan_end_date: string;
//   plan_status: string;
//   register_type: number;
//   video_plan: number;
//   is_active: number;
// };

export const uploadCreateExerciseQuestions = async (
  materials: TUploadCreateExerciseQuestions
): Promise<Array<TUploadCreateExerciseQuestionsResponse>> => {
  const bodyFormData = new FormData();
  bodyFormData.append('file', materials?.file);
  bodyFormData.append('is_preview', `${materials?.is_preview}`);
  const response = await ApiService.post(
    '/import/exercise-questions',
    bodyFormData,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
    }
  );
  return response.data.data;
};
