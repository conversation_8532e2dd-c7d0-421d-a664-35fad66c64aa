import { EIsActive, EPlacement, ETypeNotification } from '@/commons/enums';
import { TVideo } from '@/commons/types';
import {
  Button,
  ColumnType,
  EButtonStyleType,
  EditIcon,
  Pagination,
  Table,
} from '@/components';
import { RootState } from '@/store/configureStore';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { use, useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FilterVideo from './FilterVideo';
import { TFilterVideo } from './video.types';
import { getExportVideos, getVideos } from '@/services/apis';
import { getVideosAction } from '@/store/actions';
import moment from 'moment';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import { EXCEPTION } from '@/commons/messages-enum';
import { resetGetVideosResponse, resetVideoSlice } from '@/store/slices/videos';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';

const ListVideos = ({ query }: { query: TFilterVideo }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [filter, setFilter] = useState<TFilterVideo>();
  const { getVideosResponse: videos } = useSelector(
    (state: RootState) => state.video
  );
  const { data, meta = undefined } = videos || {};

  useEffect(() => {
    return () => {
      dispatch(resetGetVideosResponse());
    };
  }, [dispatch]);

  const getActions = useCallback(
    (element: TVideo) => {
      const handleRedirectToEdit = () => {
        dispatch(resetVideoSlice());
        dispatch(setCallBackUrl(router.asPath));
        setTimeout(() => router.push(`/videos/${element.id}/edit`), 0);
      };
      return (
        <div className="flex items-center gap-5">
          <button onClick={handleRedirectToEdit}>
            <EditIcon />
          </button>
        </div>
      );
    },
    [dispatch, router]
  );
  const columns = useMemo(
    () =>
      [
        {
          title: '動画ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => element?.id,
        },
        {
          title: '大項目名',
          dataIndex: 'category.name',
          key: 'category.name',
          render: (element) => (
            <div className="max-w-50 2xl:max-w-lg break-all">
              {element?.category.name || '...'}
            </div>
          ),
        },
        {
          title: '動画名',
          dataIndex: 'name',
          key: 'name',
          render: (element) => (
            <div className="max-w-50 2xl:max-w-lg break-all">
              {element?.name || '...'}
            </div>
          ),
        },
        {
          title: 'ファイル名',
          dataIndex: 'fileName',
          key: 'fileName',
          render: (element) => (
            <div className="max-w-50 2xl:max-w-lg break-all">
              {element?.video || '...'}
            </div>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'invalid',
          key: 'invalid',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TVideo>>,
    [getActions]
  );

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
    };
    setFilter(defaultFilter);
    return () => setFilter(undefined);
  };
  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(router.asPath));
    router.push('/videos/create');
  };

  const fetchData = useCallback(
    (page: number, filter?: TFilterVideo) => {
      const category_id = filter?.category_id
        ? Number(filter?.category_id)
        : undefined;
      const name = filter?.name?.trim() || undefined;
      const is_actives = filter?.is_actives as Array<number>;
      const params = { category_id, page, name, is_actives };
      dispatch(getVideosAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      fetchData(1, filter);
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ query }, undefined, {
        shallow: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleExportData = async () => {
    const name = filter?.name?.trim() || undefined;
    const category_id = filter?.category_id || undefined;
    const is_actives = filter?.is_actives as Array<number>;
    const params = {
      name,
      category_id,
      is_actives,
    };

    try {
      const response = await getExportVideos({ params });
      const fileName = `Kojiro_unkan_videos_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
          <FilterVideo
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        <div className="self-end flex items-center gap-2">
          <Button
            onClick={handleExportData}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="出力"
            className="!font-normal"
          />
          <Button
            onClick={handleRedirectToAdd}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="新規登録"
            className="!font-normal"
          />
        </div>
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            meta?.total || 0
          }件`}</h2>
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<TVideo> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default ListVideos;
