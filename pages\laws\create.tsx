import { Input } from '../../components/Input';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Form, InputNumber, Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import validate from '@/utils/validate';
import dynamic from 'next/dynamic';
import { EIsActive, ERoleId, ETypeNotification } from '@/commons/enums';
import { Rule } from 'antd/lib/form';
import { DataLaws } from '@/commons/types';
import { createLaws, helpers } from '@/services/apis';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { useRouter } from 'next/router';
import { SelectBigCategory } from '@/containers/select';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import { GetServerSideProps } from 'next';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import { PreviewLawsModalEditUpdate } from '@/containers';

const RegisterDecree = () => {
  const {
    checkEmptyString,
    checkMaxLength,
    hasWhiteSpace,
    checkMaxLengthEditor,
  } = validate;
  const EditorBoxForm = dynamic(
    () => import('@/components/EditorBox/EditorBoxForm'),
    { ssr: false }
  );
  const [form] = Form.useForm();
  const [isFisrt, setFisrt] = useState<boolean>(true);
  const [isVisible, setVisible] = useState<boolean>(false);
  const [isShowPreviewLawsModal, setIsShowPreviewLawsModal] =
    useState<boolean>(false);
  const router = useRouter();
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  useEffect(() => {
    form.setFieldsValue({
      is_active: EIsActive.ENABLE,
    });
  }, []);

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-2">
        <Form.Item className={'!mb-0'} name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onBack = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/laws');
  };

  const onSubmit = async () => {
    try {
      const values = form.getFieldsValue();
      const payload = {
        content: values?.content,
        is_active: values?.is_active,
        category_id: values?.category_id,
        name: values?.name,
        text_id: values?.text_id,
      };
      const data = await createLaws(payload);
      if (data) {
        onBack();
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onCloseModal = () => {
    setVisible(false);
  };

  const onOpenModal = () => {
    setVisible(true);
  };

  const onClosePreviewLawsModal = () => {
    setIsShowPreviewLawsModal(false);
  };

  const onOpenPreviewLawsModal = () => {
    setIsShowPreviewLawsModal(true);
  };

  return (
    <div className="mx-12 mt-4">
      <span className="text-lg font-bold">法令新規登録</span>
      <PreviewLawsModalEditUpdate
        visiable={isShowPreviewLawsModal}
        handleCloseModal={onClosePreviewLawsModal}
        content={form.getFieldValue('content')}
        name={form.getFieldValue('name')}
      />
      <Form form={form} onFinish={onOpenModal}>
        <div className="bg-alice-blue-light py-4 px-5 mt-3">
          <div className="flex justify-end">
            <Button
              size={'small'}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title={'プレビュー'}
              onClick={onOpenPreviewLawsModal}
            />
          </div>
          <div className="grid grid-cols-2 justify-between mt-6 gap-5">
            <div>
              <span className="text-textGray text-sm font-bold">大項目名</span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('大項目名'),
                  },
                ]}
                className={'!mb-0'}
                name={'category_id'}
              >
                <SelectBigCategory
                  placeholder={'選択してください'}
                  size={'middle'}
                  className={'!mt-2'}
                />
              </Form.Item>
            </div>

            <div className={'w-full'}>
              <span className="text-textGray text-sm font-bold">法令名</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('法令名'),
                    },
                    () => checkMaxLength('タイトル', 255),
                  ] as Rule[]
                }
                className={'!mb-0'}
                name={'name'}
              >
                <Input maxLength={255} classNames={'!mt-2'} />
              </Form.Item>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-5 justify-between mt-6">
            <div className="">
              <div className="flex flex-row">
                <span className="text-textGray text-sm font-bold">
                  有効・無効
                </span>
              </div>
              {ratioRender()}
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">本文</span>
              <Form.Item
                rules={
                  [
                    checkEmptyString(isFisrt),
                    () => checkMaxLengthEditor('本文', 1, 65535),
                  ] as Rule[]
                }
                className={'!mb-0 !mt-2'}
                name={'content'}
              >
                <EditorBoxForm onChange={() => setFisrt(false)} />
              </Form.Item>
            </div>
          </div>
        </div>
        <ConfirmModal
          visible={isVisible}
          onClose={onCloseModal}
          content={MODAL_MESSAGE?.CONFIRM_CREATE}
          onConfirm={onSubmit}
        />
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={onBack}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'登録'}
          />
        </div>
      </Form>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default RegisterDecree;
