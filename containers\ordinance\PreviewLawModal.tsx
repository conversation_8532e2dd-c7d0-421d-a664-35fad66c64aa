import { CustomerModal } from '@/components';
import { TPreviewLawModalProps } from './ordinance.types';

const PreviewLawModal = ({
  showModal,
  law,
  handleCloseModal,
}: TPreviewLawModalProps) => {
  return (
    <CustomerModal
      visible={showModal && !!law}
      onClose={handleCloseModal}
      centered
    >
      <div className="bg-new-white px-2 py-3 w-full min-w-full md:min-w-181 lg:w-232">
        <div className="bg-white px-4 py-5">
          <div className="pb-3 border-b border-blue-border border-dashed">
            <h3 className="text-base font-bold text-dark-gray">{law?.name}</h3>
          </div>
          <div className="p-5 h-80 overflow-scroll scrollbar-hide">
            {law?.content && (
              <div
                className="text-black !leading-normal text-lg html-render"
                dangerouslySetInnerHTML={{ __html: law?.content }}
              />
            )}
          </div>
        </div>
      </div>
    </CustomerModal>
  );
};

export default PreviewLawModal;
