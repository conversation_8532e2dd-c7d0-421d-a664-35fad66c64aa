import { Button, ConfirmModal, EButtonStyleType, Input } from '@/components';
import { Form, Radio } from 'antd';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { FormUploadVideo } from '@/containers';
import { SelectBigCategory } from '@/containers/select';
import { useDispatch, useSelector } from 'react-redux';
import { postVideoAction } from '@/store/actions';
import { showNotification } from '@/utils/functions';
import { ERoleId, ETypeNotification } from '@/commons/enums';
import { GetServerSideProps } from 'next';
import helpers from '@/services/helpers';
import { RootState } from '@/store/configureStore';

const { checkMaxString } = validate;

const AddNewVideosPage = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const dispatch = useDispatch();

  const onHandleOpenModal = () => {
    setIsShowModal(true);
  };

  const onHandleCloseModal = () => {
    setIsShowModal(false);
  };

  const onHandleConfirmSubmit = () => {
    const payload = form.getFieldsValue();
    dispatch(
      postVideoAction.request({ payload }, () => {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        setTimeout(() => {
          backToList();
        });
      })
    );
  };

  const backToList = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/videos');
  };

  const handleErrorType = () => {
    form.setFields([
      {
        name: 'video',
        errors: ['動画には、以下のファイルタイプを指定してください。mp4'],
      },
    ]);
  };

  const handleErrorSize = () => {
    form.setFields([
      {
        name: 'video',
        errors: ['動画は、5242880KB以下のファイルでなければいけません。'],
      },
    ]);
  };

  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">動画新規登録</div>
      <Form
        onFinish={onHandleOpenModal}
        form={form}
        initialValues={{
          is_active: 1,
        }}
      >
        <div className="bg-alice-blue-light py-4 px-5 grid grid-cols-1 xl:grid-cols-2 gap-8">
          <div className="w-full">
            <div className="text-textGray text-sm font-bold mb-2">大項目名</div>
            <Form.Item
              rules={
                [
                  {
                    required: true,
                    message: validationMessage.required('大項目名'),
                  },
                ] as Rule[]
              }
              name={'category_id'}
              className="!mb-0"
            >
              <SelectBigCategory placeholder={'選択してください'} />
            </Form.Item>
          </div>
          <div className="w-full">
            <div className="text-textGray text-sm font-bold mb-2">動画名</div>
            <Form.Item
              rules={
                [
                  {
                    required: true,
                    message: validationMessage.required('動画名'),
                  },
                  () => checkMaxString('動画名', 255),
                ] as Rule[]
              }
              name={'name'}
              className="!mb-0"
            >
              <Input
                classNames={'!w-full !rounded !text-sm !py-[9px]'}
                maxLength={255}
              />
            </Form.Item>
          </div>
          <div className="w-full">
            <div className="text-textGray text-sm font-bold mb-2">動画</div>
            <Form.Item
              name={'video'}
              className="!mb-0"
              rules={
                [
                  {
                    required: true,
                    message: validationMessage.required('動画'),
                  },
                ] as Rule[]
              }
            >
              <FormUploadVideo
                accept="video/mp4,video/x-m4v,video/*"
                type={['mp4']}
                maxSize={5368709120}
                // 5Gb = 5368709120 Bytes
                onErrorType={handleErrorType}
                onErrorSize={handleErrorSize}
              />
            </Form.Item>
          </div>
          <div>
            <div className="flex flex-row">
              <span className="text-textGray text-sm font-bold">
                有効・無効
              </span>
            </div>
            <div className="flex flex-row items-center mt-2">
              <Form.Item
                name="is_active"
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('有効・無効'),
                  },
                ]}
                className="!mb-0"
              >
                <Radio.Group>
                  <Radio className="font-bold" value={1}>
                    有効
                  </Radio>
                  <Radio className="font-bold" value={2}>
                    無効
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={backToList}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'登録'}
          />
        </div>
      </Form>
      <ConfirmModal
        visible={isShowModal}
        onClose={onHandleCloseModal}
        onConfirm={onHandleConfirmSubmit}
        content={MODAL_MESSAGE.CONFIRM_CREATE}
      />
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default AddNewVideosPage;
