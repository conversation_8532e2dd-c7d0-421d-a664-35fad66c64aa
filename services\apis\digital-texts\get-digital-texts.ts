import { TDigital, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetDigitalTextsParams = {
  category_id?: number;
  page?: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetDigitalTextsMaterials = {
  params?: TGetDigitalTextsParams;
};

export type TGetDigitalTextsResponse = TListData<TDigital>;

export const getDigitalTexts = async (
  materials: TGetDigitalTextsMaterials
): Promise<TGetDigitalTextsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/digital-texts', { params });
  return response.data;
};
