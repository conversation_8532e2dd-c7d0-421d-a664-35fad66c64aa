import { TGetNotificationsResponse, getNotifications } from '@/services/apis';
import { getNotificationsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getNotificationsSaga(
  action: ActionType<typeof getNotificationsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getNotifications, materials);
    const getNotificationsResponse = response as TGetNotificationsResponse;
    yield put(getNotificationsAction.success(getNotificationsResponse));
    successCallback?.(getNotificationsResponse);
  } catch (err) {
    yield put(getNotificationsAction.failure(err));
    failedCallback?.(err);
  }
}
