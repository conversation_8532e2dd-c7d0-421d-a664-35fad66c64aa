import { EIsActive } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPostVideoPayload = {
  _method?: string;
  category_id: number;
  video: string;
  name: string;
  is_active: EIsActive;
};

export type TPostVideoPath = {
  id: string;
};

export type TPostVideoMaterials = {
  payload: TPostVideoPayload;
  path?: TPostVideoPath;
};

export type TPostVideoResponse = unknown;

export const postVideo = async (
  materials: TPostVideoMaterials
): Promise<TPostVideoResponse> => {
  const { payload, path } = materials;
  const url = path ? `/videos/${path.id}` : '/videos';
  const response = await ApiService.post(url, payload);
  return response.data;
};
