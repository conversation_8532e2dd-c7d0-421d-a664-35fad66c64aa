import { TGetQuestionsMaterials, TGetQuestionsResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetQuestionsAction {
  GET_QUESTIONS = 'GET_QUESTIONS',
  GET_QUESTIONS_REQUEST = 'GET_QUESTIONS_REQUEST',
  GET_QUESTIONS_SUCCESS = 'GET_QUESTIONS_SUCCESS',
  GET_QUESTIONS_FAILED = 'GET_QUESTIONS_FAILED',
}

// TYPES

export type TGetQuestionsRequest = {
  type: EGetQuestionsAction.GET_QUESTIONS_REQUEST;
  payload: {
    materials: TGetQuestionsMaterials;
    successCallback?: (response: TGetQuestionsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetQuestionsSuccess = {
  type: EGetQuestionsAction.GET_QUESTIONS_SUCCESS;
  payload: { response?: TGetQuestionsResponse };
};

export type TGetQuestionsFailed = {
  type: EGetQuestionsAction.GET_QUESTIONS_FAILED;
};

export const getQuestionsAction = {
  request: createActionCreator(
    EGetQuestionsAction.GET_QUESTIONS_REQUEST,
    (resolve) =>
      (
        materials: TGetQuestionsMaterials,
        successCallback?: (response: TGetQuestionsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetQuestionsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetQuestionsAction.GET_QUESTIONS_SUCCESS,
    (resolve) =>
      (response?: TGetQuestionsResponse): TGetQuestionsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetQuestionsAction.GET_QUESTIONS_FAILED,
    (resolve) =>
      (error: unknown): TGetQuestionsFailed =>
        resolve({ error })
  ),
};
