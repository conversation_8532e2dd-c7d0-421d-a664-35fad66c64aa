import { EIsActive } from '@/commons/enums';
import { FormInstance } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

export type TMockTestsFilter = {
  category_id?: string;
  problem_title?: string;
  year?: number;
  problem_id?: string;
  paper?: number;
  sub_category_id?: string;
  is_actives?: Array<CheckboxValueType>;
};

export type TExamsFilter = {
  name?: string;
  is_actives?: Array<CheckboxValueType>;
};

export type TExamsFilterProps = {
  defaultFilter?: TExamsFilter;
  setFilter?: (value?: TMockTestsFilter) => void;
  clearFilter?: () => void;
};

export type TFilterMockTestsProps = {
  defaultFilter?: TMockTestsFilter;
  setFilter?: (value?: TMockTestsFilter) => void;
  clearFilter?: () => void;
};

export type TExamForm = {
  name: string;
  is_active: EIsActive;
  show_order: number;
};

export type TExamFormProps = {
  formInstance: FormInstance<TExamForm>;
  handleSubmit: (values: TExamForm) => void;
};

export type TExamSettingFormProps = {
  formInstance: FormInstance<unknown>;
  handleSubmit: (values: unknown) => void;
};

export type TNumberQuestionProps = {
  index: number;
  remove: (index: number | number[]) => void;
};

export type TPassPointProps = {
  index: number;
  remove: (index: number | number[]) => void;
};

export type TFormUploadItemProps = {
  value?: string;
  onChange?: (value: string) => void;
  accept?: string;
  type?: string[];
};

export type TExerciseFilter = {
  category_id?: string;
  show_order?: string;
  question_year?: string;
  id?: number;
  title?: string;
  content?: string;
  is_actives?: Array<CheckboxValueType>;
  types?: Array<CheckboxValueType>;
  sub_category_id?: string;
};
