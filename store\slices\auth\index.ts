import { TGetMeResponse } from '@/services/apis';
import { EGetMeAction, TGetMeSuccess } from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TAuthState {
  getMeResponse?: TGetMeResponse;
}

const initialState: TAuthState = {
  getMeResponse: undefined,
};

const AuthSlice = createSlice({
  name: 'auth-slice',
  initialState,
  reducers: {},
  extraReducers: {
    [EGetMeAction.GET_ME_SUCCESS]: (
      state: TAuthState,
      action: TGetMeSuccess
    ): TAuthState => ({
      ...state,
      getMeResponse: action.payload?.response,
    }),
  },
});

const { reducer: authReducer } = AuthSlice;

export { authReducer };
