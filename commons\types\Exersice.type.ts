import { EIsActive } from '../enums';
import { TCategory, TSubCategory } from './Category.types';

export type TExerciseResponse = {
  id?: number;
  category: TCategory;
  question_no: number;
  question_year?: number;
  is_active?: EIsActive;
  content?: string;
  digital_texts?: [];
  exercise_choices?: [any];
  exercise_question_explanation_images: any[];
  explanation: string;
  image: string;
  laws: [];
  show_order: number;
  sub_category: TSubCategory;
  title: string;
};
