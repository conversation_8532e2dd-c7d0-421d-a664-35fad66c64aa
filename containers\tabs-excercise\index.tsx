import React, { useEffect, useMemo } from 'react';

const Tabs = (props: any) => {
  const { open, setOpen } = props
  const [openTab, setOpenTab] = React.useState(1);
  const { tabsDigital, tabsAnswer } = props;
  useEffect(() => {
    if (!open) return
    setOpenTab(open)
  }, [
    open
  ])

  return (
    <>
      <div className="flex flex-wrap">
        <div className="w-full">
          <ul
            className="flex mb-0 list-none flex-wrap pt-3 flex-row"
            role="tablist"
          >
            <li className="-mb-px mr-2 last:mr-0 text-center w-36">
              <div
                className={
                  'text-xs font-bold px-5 py-3 rounded-t-xl block leading-normal text-white cursor-pointer ' +
                  (openTab === 1
                    ? 'bg-[#57a5df]'
                    : 'bg-[#00589c]')
                }
                onClick={(e) => {
                  e.preventDefault();
                  setOpenTab(1);
                  setOpen?.(1)
                }}
                data-toggle="tab"
                role="tablist"
              >
                解説・その他
              </div>
            </li>
            <li className="-mb-px mr-2 last:mr-0 text-center w-36">
              <div
                className={
                  'text-xs font-bold px-5 py-3 rounded-t-xl block leading-normal !text-white cursor-pointer ' +
                  (openTab === 2
                    ? 'bg-[#57a5df]'
                    : 'bg-[#00589c]')
                }
                onClick={(e) => {
                  e.preventDefault();
                  setOpenTab(2);
                  setOpen?.(1)
                }}
                data-toggle="tab"
                role="tablist"
              >
                設間
              </div>
            </li>
          </ul>
          <div className="relative flex flex-col min-w-0 break-words w-full mb-6 bg-alice-blue-light">
            <div className="px-4 py-5 flex-auto">
              <div className="tab-content tab-space">
                <div className={openTab === 1 ? 'block' : 'hidden'} id="link1">
                  {tabsDigital()}
                </div>
                <div className={openTab === 2 ? 'block' : 'hidden'} id="link2">
                  {tabsAnswer()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Tabs;
