import clsx from 'clsx';
import { ReactNode, useCallback, useEffect, useRef, useState } from 'react';

export type TDoubleScrollbarProps = {
  className?: string;
  children: ReactNode;
};

const DoubleScrollbar = ({ className, children }: TDoubleScrollbarProps) => {
  const [width, setWidth] = useState<string>('auto');
  const outerDiv = useRef<HTMLDivElement>(null);
  const childWrapper = useRef<HTMLDivElement>(null);

  const getChildWrapperWidth = () => {
    let width = null;
    if (childWrapper?.current?.scrollWidth) {
      width = childWrapper?.current?.scrollWidth + 'px';
    }
    return width;
  };

  const calculateWidth = useCallback(() => {
    let childWidth = getChildWrapperWidth();

    if (childWidth == null) {
      childWidth = 'auto';
    }

    if (childWidth !== width) {
      setWidth(childWidth);
    }
  }, [width]);

  useEffect(() => {
    calculateWidth();
  });

  useEffect(() => {
    window.addEventListener('resize', calculateWidth);
    return () => {
      window.addEventListener('resize', calculateWidth);
    };
  }, [calculateWidth]);

  const handleScrollChildWrapper = () => {
    if (childWrapper.current && outerDiv.current) {
      outerDiv.current.scrollLeft = childWrapper.current?.scrollLeft;
    }
  };

  const handleScrollOuterDiv = () => {
    if (childWrapper.current && outerDiv.current) {
      childWrapper.current.scrollLeft = outerDiv.current?.scrollLeft;
    }
  };

  return (
    <div className={clsx(className, 'double-scrollbar')}>
      <div
        className="overflow-x-auto overflow-y-hidden"
        ref={outerDiv}
        onScroll={handleScrollOuterDiv}
      >
        <div style={{ paddingTop: '1px', width }}>&nbsp;</div>
      </div>
      <div
        className="overflow-x-auto overflow-y-hidden"
        ref={childWrapper}
        onScroll={handleScrollChildWrapper}
      >
        {children}
      </div>
    </div>
  );
};

export default DoubleScrollbar;
