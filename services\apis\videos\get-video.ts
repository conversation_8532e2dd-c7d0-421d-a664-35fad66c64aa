import { TVideo } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetVideoPath = {
  id: string;
};

export type TGetVideoMaterials = {
  paths?: TGetVideoPath;
};

export type TGetVideoResponse = { data: TVideo };

export const getVideo = async (
  materials: TGetVideoMaterials
): Promise<TGetVideoResponse> => {
  const { paths } = materials;
  const response = await ApiService.get(`/videos/${paths?.id}`);
  return response.data;
};
