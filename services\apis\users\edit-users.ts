import ApiService from '@/services/apis';

export type TPutEditUsers = {
  id: any;
  username: string;
  username_kana: string;
  role_id: number;
  video_plan: number;
  plan_start_date: string;
  plan_end_date: string;
  email: string;
  login_id: string;
  password: string;
  is_active: number;
};

export type TEditUsersResponse = unknown;

export const putEditUser = async (
  payload: TPutEditUsers
): Promise<TEditUsersResponse> => {
  const response = await ApiService.put(`users/${payload.id}`, payload);
  return response.data;
};
