// import { TCategory } from '@/commons/types';
// import { Breadcrumbs } from '@/components';
import { ERoleId } from '@/commons/enums';
import { ListSubCategory, TListSubCategoryProps } from '@/containers';
import helpers from '@/services/helpers';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';

const SubCategoryPage = ({ category, query }: TListSubCategoryProps) => {
  return (
    <div className="mx-12 my-4">
      <h1 className="text-lg font-bold text-black-kj">小項目一覧</h1>
      <div className="mt-3 w-full">
        <ListSubCategory category={category} query={query} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
}) => {
  try {
    const { cookies } = req;
    const { id, ...restQuery } = query;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    if (!id) {
      throw new Error('missing category id');
    }

    const is_actives =
      restQuery?.is_actives
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const filter = removeEmpty({ ...restQuery, is_actives });
    const category = { id };

    return {
      props: { category, query: filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default SubCategoryPage;
