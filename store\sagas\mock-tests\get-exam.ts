import { TGetExamResponse, getExam } from '@/services/apis';
import { getExamAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getExamSaga(
  action: ActionType<typeof getExamAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getExam, materials);
    const getExamResponse = response as TGetExamResponse;
    yield put(getExamAction.success(getExamResponse));
    successCallback?.(getExamResponse);
  } catch (err) {
    yield put(getExamAction.failure(err));
    failedCallback?.(err);
  }
}
