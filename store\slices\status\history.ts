import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export interface THistoryState {
  callBackUrl?: string;
  backURL: any;
}

const initialState: THistoryState = {
  callBackUrl: undefined,
  backURL: {},
};

const historySlice = createSlice({
  name: 'history-slice',
  initialState,
  reducers: {
    resetHistorySlice: () => initialState,
    setCallBackUrl: (state: THistoryState, action: PayloadAction<string>) => {
      return { ...state, callBackUrl: action.payload };
    },
    setBackObjectUrl: (state: THistoryState, action: PayloadAction<{}>) => {
      return { ...state, backURL: { ...state.backURL, ...action.payload } };
    },
  },
  extraReducers: {},
});

const {
  reducer: historyReducer,
  actions: { resetHistorySlice, setCallBackUrl, setBackObjectUrl },
} = historySlice;

export { historyReducer, resetHistorySlice, setCallBackUrl, setBackObjectUrl };
