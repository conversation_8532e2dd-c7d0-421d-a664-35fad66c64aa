import { EIsActive } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPostCouponPayload = {
  name: string,
  code: string,
  start_date: string,
  end_date: string,
  training_school: string,
  discount: number,
  note: string
};

export type TPostCouponMaterials = {
  payload: TPostCouponPayload;
};

export type TPostCouponResponse = unknown;

export const postCoupon = async (
  materials: TPostCouponMaterials
): Promise<TPostCouponResponse> => {
  const { payload } = materials;
  const response = await ApiService.post('/coupons', payload);
  return response.data;
};
