import clsx from 'clsx';
import { UIEvent, useEffect, useRef } from 'react';
import { TWrapperLazyLoadProps } from './WrapperLazyLoad.types';

const WrapperLazyLoad = ({
  maxHeight,
  className,
  showLoading,
  children,
  resetScrollToBottom,
  resetScrollToTop,
  onTop,
  onEnd,
  onScroll,
  onResetScrollSuccess,
}: TWrapperLazyLoadProps) => {
  const wrapperLazyLoadRef = useRef<HTMLDivElement>(null);

  const onScrollEnd = (e: UIEvent<HTMLDivElement>) => {
    onScroll?.(e);
    const element = e.currentTarget;
    const isScrollEndOfElment =
      Math.ceil(element.scrollTop + element.clientHeight) >=
      element.scrollHeight;

    if (element.scrollTop === 0) onTop?.();
    if (isScrollEndOfElment && !resetScrollToTop) onEnd?.();
  };

  useEffect(() => {
    if (resetScrollToTop && wrapperLazyLoadRef?.current) {
      wrapperLazyLoadRef?.current?.scrollTo(0, 0);
      onResetScrollSuccess?.();
    }
  }, [resetScrollToTop, onResetScrollSuccess]);

  useEffect(() => {
    if (resetScrollToBottom && wrapperLazyLoadRef?.current) {
      const domWrapperLazyLoad = wrapperLazyLoadRef?.current;
      if (domWrapperLazyLoad) {
        wrapperLazyLoadRef.current.scrollTop =
          wrapperLazyLoadRef.current.scrollHeight;
      }
      onResetScrollSuccess?.();
    }
  }, [resetScrollToBottom, onResetScrollSuccess]);

  return (
    <div
      ref={wrapperLazyLoadRef}
      onScroll={onScrollEnd}
      className={clsx(
        className,
        'overflow-x-hidden overflow-y-auto max-h-full'
      )}
      style={{ maxHeight }}
    >
      {children}
      <div
        className={clsx('WrapperLazyLoad__loading', !showLoading && 'hidden')}
      >
        Loading
      </div>
    </div>
  );
};

export default WrapperLazyLoad;
