import { TOptionDigital, TOptionLaws } from '@/commons/types';

export type TDigitalParam = {
    id: number;
    id_digital: number | undefined;
    name: string;
    is_active?: null,
    id_big_catagory: string| number | undefined;
};

export type TDigitalProps = {
    item: TDigitalParam;
    removeDigital?: (value: TDigitalParam) => void;
    index: number;
    onConfirm?: (value: TOptionDigital, selectedDigital: TDigitalParam, id_big_catagory: string|undefined) => void;
    disabled?: boolean;
};

export type TLawsParam = {
    id: number;
    id_laws: number | undefined;
    name: string;
}

export type TAnswerParam = {
    choice_no: number;
    content: string;
    is_correct: boolean;
}

export type TLawsProps = {
    item: TLawsParam;
    removeLaws?: (value: TLawsParam) => void;
    index: number;
    onConfirm?: (value: TOptionLaws, selectedLaws: TLawsParam) => void;
    disabled?: boolean;
};
