import ApiService from '@/services/apis';

export type TPostPayloadTermSettingsMaterials = {
  description: string;
  is_public: number;
};

export const postTermSettings = async (
  payload: TPostPayloadTermSettingsMaterials
) => {
  const response = await ApiService.post('/settings/term', payload);
  return response.data;
};

export const postTermSettingsTrial = async (
  payload: TPostPayloadTermSettingsMaterials
) => {
  const response = await ApiService.post('/settings/term-trial', payload);
  return response.data;
};

export const getTermSettings = async () => {
  const response = await ApiService.get('/settings/term');
  return response.data;
};

export const getTermSettingsTrial = async () => {
  const response = await ApiService.get('/settings/term-trial');
  return response.data;
};
