import {
  TGetCouponPlansMaterials,
  TGetCouponPlansResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetCouponPlansAction {
  GET_COUPON_PLANS = 'GET_COUPON_PLANS',
  GET_COUPON_PLANS_REQUEST = 'GET_COUPON_PLANS_REQUEST',
  GET_COUPON_PLANS_SUCCESS = 'GET_COUPON_PLANS_SUCCESS',
  GET_COUPON_PLANS_FAILED = 'GET_COUPON_PLANS_FAILED',
}

// TYPES

export type TGetCouponPlansRequest = {
  type: EGetCouponPlansAction.GET_COUPON_PLANS_REQUEST;
  payload: {
    materials: any;
    successCallback?: (response: any) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetCouponPlansSuccess = {
  type: EGetCouponPlansAction.GET_COUPON_PLANS_SUCCESS;
  payload: { response?: any };
};

export type TGetCouponPlansFailed = {
  type: EGetCouponPlansAction.GET_COUPON_PLANS_FAILED;
};

export const getCouponPlansAction = {
  request: createActionCreator(
    EGetCouponPlansAction.GET_COUPON_PLANS_REQUEST,
    (resolve) =>
      (
        materials: TGetCouponPlansMaterials,
        successCallback?: (response: TGetCouponPlansResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetCouponPlansRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetCouponPlansAction.GET_COUPON_PLANS_SUCCESS,
    (resolve) =>
      (response?: any): TGetCouponPlansSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetCouponPlansAction.GET_COUPON_PLANS_FAILED,
    (resolve) =>
      (error: unknown): TGetCouponPlansFailed =>
        resolve({ error })
  ),
};
