import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function ChatIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22.059"
      height="20.577"
      viewBox="284 185.742 22.059 20.577"
      {...props}
    >
      <g data-name="Group 181">
        <g data-name="path4146">
          <path
            d="M295.338 199.749c-.023 0-.047 0-.07-.002a3.112 3.112 0 0 1-2.75-3.086v-1.266a.323.323 0 0 1 .646 0v1.264a2.465 2.465 0 0 0 2.142 2.441l-.004-5.226.013-3.899a.327.327 0 0 1-.016-.1v-.423a3 3 0 0 0-2.98-2.922h-1.995a3 3 0 0 0-2.98 2.924l-.017.832v.002l.006 1.607a3.747 3.747 0 0 0 2.57 3.547c.169.*************.41a.325.325 0 0 1-.408.205 4.394 4.394 0 0 1-3.012-4.16l-.005-1.605c0-.053.002-.215.015-.854a3.611 3.611 0 0 1 1.089-2.517 3.614 3.614 0 0 1 2.538-1.036h1.994c.956 0 1.857.37 2.54 1.04a3.6 3.6 0 0 1 1.087 2.515v.332c.**************.016.104l-.013 4.002.003 5.255a.616.616 0 0 1-.613.615Z"
            fill="#fff"
            fillRule="evenodd"
            data-name="Path 467"
          />
        </g>
        <g
          data-name="Group 180"
          transform="translate(284 185)"
          clipPath='url("#a")'
        >
          <g data-name="path4162">
            <path
              d="M13.367 21.317H1.276c-.344 0-.666-.134-.908-.382a1.257 1.257 0 0 1-.368-.91l.06-5.034c0-.008.002-.013.002-.015.056-.975.67-1.823 1.64-2.278 1.036-.486 3.44-1.197 3.541-1.224.011-.006.022-.008.033-.012a.242.242 0 0 0 .202-.24v-.828a.323.323 0 0 1 .646 0v.828a.892.892 0 0 1-.712.873c-.2.062-2.483.74-3.436 1.187-.761.356-1.223.985-1.269 1.723l-.061 5.027a.618.618 0 0 0 .182.45c.12.12.278.187.448.187h12.091a.623.623 0 0 0 .448-.188.613.613 0 0 0 .18-.449l-.06-5.027c-.045-.738-.508-1.367-1.269-1.723-.26-.123-.645-.273-1.145-.449a.324.324 0 1 1 .214-.61c.52.184.926.344 1.206.475.97.455 1.583 1.303 1.64 2.276v.017l.06 5.033c.006.344-.125.668-.366.91a1.258 1.258 0 0 1-.908.383Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 470"
            />
          </g>
          <g data-name="path4166">
            <path
              d="M7.994 15.306H6.649a3.043 3.043 0 0 1-3.039-3.039v-.047a.324.324 0 1 1 .647 0v.047a2.395 2.395 0 0 0 2.392 2.39h1.345c.72 0 1.396-.32 1.853-.878a.328.328 0 0 1 .455-.047.326.326 0 0 1 .045.457 3.026 3.026 0 0 1-2.353 1.117Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 471"
            />
          </g>
          <g data-name="path4170">
            <path
              d="M7.32 11.276h-.003c-1.027-.012-1.754-.594-2.387-1.222-.574-.567-1.165-1.692-1.185-2.743V5.366c0-.027.002-.055.009-.078.038-.504.12-.973.247-1.399a1.198 1.198 0 0 1 1.926-.578C7.61 4.737 9.487 5.46 10.6 5.541a.326.326 0 0 1 .298.325V7.311c-.022 1.051-.613 2.176-1.186 2.743-.634.628-1.361 1.21-2.388 1.222H7.32ZM4.39 7.3c.02.869.526 1.832.995 2.295.499.496 1.102 1.025 1.935 1.035.835-.01 1.437-.54 1.937-1.035.469-.463.975-1.426.994-2.295V6.15C9.02 5.97 7.164 5.202 5.51 3.804a.554.554 0 0 0-.888.27 6.194 6.194 0 0 0-.23 1.347V7.3Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 472"
            />
          </g>
          <g data-name="path4174">
            <path
              d="M13.386 11.289a.528.528 0 0 1-.52-.627l.324-1.709h-.268a.323.323 0 1 1 0-.646h.659a.323.323 0 0 1 .317.382l-.339 1.79 2.737-2.104a.318.318 0 0 1 .198-.068h3.513c.775 0 1.406-.631 1.406-1.407V2.795c0-.774-.63-1.406-1.406-1.406H12.22c-.203 0-.4.044-.583.128a.324.324 0 0 1-.268-.59c.268-.12.554-.185.85-.185h7.787c1.132 0 2.052.92 2.052 2.053V6.9c0 1.133-.92 2.053-2.052 2.053h-3.404l-2.895 2.227a.534.534 0 0 1-.322.109Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 473"
            />
          </g>
          <g data-name="path4178">
            <path
              d="M7.574 8.114H7.36a.426.426 0 0 1-.424-.426v-.646c0-.178.144-.327.322-.327.18 0 .324.149.324.327v.427a.32.32 0 0 1 .316.32c0 .18-.145.325-.324.325Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 474"
            />
          </g>
          <g data-name="path4182">
            <path
              d="M7.814 9.427h-.995a.324.324 0 0 1 0-.646h.995a.322.322 0 1 1 0 .646Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 475"
            />
          </g>
          <g data-name="path4186">
            <path
              d="M8.802 7.053a.321.321 0 0 1-.323-.32v-.297c0-.178.146-.324.323-.324.179 0 .324.146.324.324v.297c0 .177-.145.32-.324.32Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 476"
            />
          </g>
          <g data-name="path4190">
            <path
              d="M5.83 7.053a.321.321 0 0 1-.323-.32v-.297c0-.178.145-.324.323-.324.179 0 .323.146.323.324v.297c0 .177-.144.32-.323.32Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 477"
            />
          </g>
          <g data-name="path4194">
            <path
              d="M3.946 8.165c-.287 0-.568-.098-.796-.282a1.27 1.27 0 0 1-.464-.976l-.002-.526c-.024-.38.153-.738.466-.933.193-.12.414-.17.64-.149l.31.032a.324.324 0 0 1-.065.645l-.31-.032a.36.36 0 0 0-.233.053.374.374 0 0 0-.162.347v.024l.003.535a.614.614 0 0 0 .731.605.322.322 0 1 1 .126.633 1.16 1.16 0 0 1-.244.024Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 478"
            />
          </g>
          <g data-name="path4198">
            <path
              d="M3.073 21.32a.324.324 0 0 1-.324-.325v-3.512a.324.324 0 1 1 .646 0v3.512c0 .18-.144.324-.322.324Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 479"
            />
          </g>
          <g data-name="path4202">
            <path
              d="M11.664 21.32a.323.323 0 0 1-.323-.325v-3.512a.323.323 0 1 1 .647 0v3.512c0 .18-.145.324-.324.324Z"
              fill="#fff"
              fillRule="evenodd"
              data-name="Path 480"
            />
          </g>
        </g>
      </g>
      <defs>
        <clipPath id="a">
          <path d="M0 0h22.059v22.059H0V0z" data-name="Rectangle 84" />
        </clipPath>
      </defs>
    </svg>
  );
}
