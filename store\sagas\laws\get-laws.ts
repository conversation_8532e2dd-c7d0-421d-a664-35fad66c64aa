import { TGetLawsResponse, getLaws } from '@/services/apis';
import { getLawsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getLawsSaga(
  action: ActionType<typeof getLawsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getLaws, materials);
    const getLawsResponse = response as TGetLawsResponse;
    yield put(getLawsAction.success(getLawsResponse));
    successCallback?.(getLawsResponse);
  } catch (err) {
    yield put(getLawsAction.failure(err));
    failedCallback?.(err);
  }
}
