import {
  TDeleteQuestionMaterials,
  TDeleteQuestionResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EDeleteQuestionAction {
  DELETE_QUESTION = 'DELETE_QUESTION',
  DELETE_QUESTION_REQUEST = 'DELETE_QUESTION_REQUEST',
  DELETE_QUESTION_SUCCESS = 'DELETE_QUESTION_SUCCESS',
  DELETE_QUESTION_FAILED = 'DELETE_QUESTION_FAILED',
}

// TYPES

export type TDeleteQuestionRequest = {
  type: EDeleteQuestionAction.DELETE_QUESTION_REQUEST;
  payload: {
    materials: TDeleteQuestionMaterials;
    successCallback?: (response: TDeleteQuestionResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TDeleteQuestionSuccess = {
  type: EDeleteQuestionAction.DELETE_QUESTION_SUCCESS;
  payload: { response?: TDeleteQuestionResponse };
};

export type TDeleteQuestionFailed = {
  type: EDeleteQuestionAction.DELETE_QUESTION_FAILED;
};

export const deleteQuestionAction = {
  request: createActionCreator(
    EDeleteQuestionAction.DELETE_QUESTION_REQUEST,
    (resolve) =>
      (
        materials: TDeleteQuestionMaterials,
        successCallback?: (response: TDeleteQuestionResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TDeleteQuestionRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EDeleteQuestionAction.DELETE_QUESTION_SUCCESS,
    (resolve) =>
      (response?: TDeleteQuestionResponse): TDeleteQuestionSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EDeleteQuestionAction.DELETE_QUESTION_FAILED,
    (resolve) =>
      (error: unknown): TDeleteQuestionFailed =>
        resolve({ error })
  ),
};
