import { TGetMyNotificationsResponse } from '@/services/apis';
import {
  EGetMyNotificationsAction,
  TGetMyNotificationsSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TMyNotificationState {
  getMyNotificationResponse?: TGetMyNotificationsResponse;
}

const initialState: TMyNotificationState = {
  getMyNotificationResponse: undefined,
};

const MyNotificationSlice = createSlice({
  name: 'my-notification-slice',
  initialState,
  reducers: {},
  extraReducers: {
    [EGetMyNotificationsAction.GET_MY_NOTIFICATIONS_SUCCESS]: (
      state: TMyNotificationState,
      action: TGetMyNotificationsSuccess
    ): TMyNotificationState => ({
      ...state,
      getMyNotificationResponse: action.payload?.response,
    }),
  },
});

const { reducer: myNotificationReducer } = MyNotificationSlice;

export { myNotificationReducer };
