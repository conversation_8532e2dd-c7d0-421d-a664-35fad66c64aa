import { TPutQuestionMaterials, TPutQuestionResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPutQuestionAction {
  PUT_QUESTION = 'PUT_QUESTION',
  PUT_QUESTION_REQUEST = 'PUT_QUESTION_REQUEST',
  PUT_QUESTION_SUCCESS = 'PUT_QUESTION_SUCCESS',
  PUT_QUESTION_FAILED = 'PUT_QUESTION_FAILED',
}

// TYPES

export type TPutQuestionRequest = {
  type: EPutQuestionAction.PUT_QUESTION_REQUEST;
  payload: {
    materials: TPutQuestionMaterials;
    successCallback?: (response: TPutQuestionResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPutQuestionSuccess = {
  type: EPutQuestionAction.PUT_QUESTION_SUCCESS;
  payload: { response?: TPutQuestionResponse };
};

export type TPutQuestionFailed = {
  type: EPutQuestionAction.PUT_QUESTION_FAILED;
};

export const putQuestionAction = {
  request: createActionCreator(
    EPutQuestionAction.PUT_QUESTION_REQUEST,
    (resolve) =>
      (
        materials: TPutQuestionMaterials,
        successCallback?: (response: TPutQuestionResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TPutQuestionRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPutQuestionAction.PUT_QUESTION_SUCCESS,
    (resolve) =>
      (response?: TPutQuestionResponse): TPutQuestionSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPutQuestionAction.PUT_QUESTION_FAILED,
    (resolve) =>
      (error: unknown): TPutQuestionFailed =>
        resolve({ error })
  ),
};
