import { TGetKnowledgeBoardsResponse } from '@/services/apis';
import {
  EGetKnowledgeBoardsAction,
  TGetKnowledgeBoardsSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TKnowledgeBoardsState {
  getKnowledgeBoardsResponse?: TGetKnowledgeBoardsResponse;
}

const initialState: TKnowledgeBoardsState = {
  getKnowledgeBoardsResponse: undefined,
};

const knowlegdeBoardsSlice = createSlice({
  name: 'knowledgeBoards-slice',
  initialState,
  reducers: {
    resetKnowlegdeBoardsSlice: () => initialState,
  },
  extraReducers: {
    [EGetKnowledgeBoardsAction.GET_KNOWLEDGE_BOARDS_SUCCESS]: (
      state: TKnowledgeBoardsState,
      action: TGetKnowledgeBoardsSuccess
    ): TKnowledgeBoardsState => ({
      ...state,
      getKnowledgeBoardsResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: knowledgeBoardsReducer,
  actions: { resetKnowlegdeBoardsSlice },
} = knowlegdeBoardsSlice;

export { knowledgeBoardsReducer, resetKnowlegdeBoardsSlice };
