import { TGetMockTestsMaterials, TGetMockTestsResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetMockTestsAction {
  GET_MOCK_TESTS = 'GET_MOCK_TESTS',
  GET_MOCK_TESTS_REQUEST = 'GET_MOCK_TESTS_REQUEST',
  GET_MOCK_TESTS_SUCCESS = 'GET_MOCK_TESTS_SUCCESS',
  GET_MOCK_TESTS_FAILED = 'GET_MOCK_TESTS_FAILED',
}

// TYPES

export type TGetMockTestsRequest = {
  type: EGetMockTestsAction.GET_MOCK_TESTS_REQUEST;
  payload: {
    materials: TGetMockTestsMaterials;
    successCallback?: (response: TGetMockTestsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetMockTestsSuccess = {
  type: EGetMockTestsAction.GET_MOCK_TESTS_SUCCESS;
  payload: { response?: TGetMockTestsResponse };
};

export type TGetMockTestsFailed = {
  type: EGetMockTestsAction.GET_MOCK_TESTS_FAILED;
};

export const getMockTestsAction = {
  request: createActionCreator(
    EGetMockTestsAction.GET_MOCK_TESTS_REQUEST,
    (resolve) =>
      (
        materials: TGetMockTestsMaterials,
        successCallback?: (response: TGetMockTestsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetMockTestsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetMockTestsAction.GET_MOCK_TESTS_SUCCESS,
    (resolve) =>
      (response?: TGetMockTestsResponse): TGetMockTestsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetMockTestsAction.GET_MOCK_TESTS_FAILED,
    (resolve) =>
      (error: unknown): TGetMockTestsFailed =>
        resolve({ error })
  ),
};
