import { TDeleteExamResponse, deleteExam } from '@/services/apis';
import { deleteExamAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* deleteExamSaga(
  action: ActionType<typeof deleteExamAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(deleteExam, materials);
    const deleteExamResponse = response as TDeleteExamResponse;
    yield put(deleteExamAction.success(deleteExamResponse));
    successCallback?.(deleteExamResponse);
  } catch (err) {
    yield put(deleteExamAction.failure(err));
    failedCallback?.(err);
  }
}
