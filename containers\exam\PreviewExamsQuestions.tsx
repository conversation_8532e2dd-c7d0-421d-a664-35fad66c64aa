import { EIsActive, EQuestionForm } from '@/commons/enums';
import { ColumnType, Pagination, Table } from '@/components';
import { useMemo } from 'react';
import {
  TUploadCreateQuestionsResponse,
  TUploadUpdateQuestionsResponse,
} from '@/services/apis';

type PreviewProps = {
  data: Array<TUploadCreateQuestionsResponse | TUploadUpdateQuestionsResponse>;
  pagination: any;
  handleChangePage: (page: number) => void;
  previewType?: number;
};

const PreviewExamsQuestions = ({
  data,
  pagination,
  handleChangePage,
  previewType,
}: PreviewProps) => {
  const columns = useMemo(
    () =>
      [
        {
          title: '行数',
          dataIndex: 'line_no',
          key: 'line_no',
          render: (element) => element?.line_no,
        },
        {
          title: '問題番号',
          dataIndex: 'question_no',
          key: 'question_no',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 break-all">
              {element?.question_no}
            </div>
          ),
        },
        {
          title: '模擬試験名',
          dataIndex: 'exam',
          key: 'exam',
          render: (element) => (
            <div className="min-w-25 max-w-25 2xl:max-w-50 break-all">
              {`(${element.exam.id}) ${element.exam.name}`}
            </div>
          ),
        },
        {
          title: '大項目名',
          dataIndex: 'category',
          key: 'category',
          render: (element) => (
            <div className="min-w-25 max-w-25 2xl:max-w-50 break-all">
              {`(${element.category.id}) ${element.category.name}`}
            </div>
          ),
        },
        {
          title: '出題No.',
          dataIndex: 'show_order',
          key: 'show_order',
          render: (element) => element?.show_order,
        },
        {
          title: '出題年度',
          dataIndex: 'question_year',
          key: 'question_year',
          render: (element) => (
            <div className="min-w-25 max-w-25 break-all">
              {element?.question_year}
            </div>
          ),
        },
        {
          title: '配点',
          dataIndex: 'allocation_point',
          key: 'allocation_point',
          render: (element) => element?.allocation_point,
        },
        {
          title: '問題タイトル',
          dataIndex: 'title',
          key: 'title',
          render: (element) => (
            <div className="min-w-25 2xl:max-w-50 break-all">
              {element.title}
            </div>
          ),
        },
        {
          title: '問題本文',
          dataIndex: 'content',
          key: 'content',
          render: (element) => (
            <div
              className="min-w-75 max-w-75 break-all !leading-normal html-render"
              dangerouslySetInnerHTML={{ __html: element?.content }}
            />
          ),
        },
        {
          title: '問題画像',
          dataIndex: 'image',
          key: 'image',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-50 break-all">
              {element.image}
            </div>
          ),
        },
        {
          title: '解説本文',
          dataIndex: 'explanation',
          key: 'explanation',
          render: (element) => (
            <div
              className="min-w-75 max-w-75 break-all !leading-normal html-render"
              dangerouslySetInnerHTML={{ __html: element?.explanation }}
            />
          ),
        },
        {
          title: '解説画像',
          dataIndex: 'explanation_images',
          key: 'explanation_images',
          render: (element) => (
            <>
              {element?.explanation_images?.map((image: any, index: any) => (
                <div
                  key={`explanation_image_${index}`}
                  className="min-w-25 max-w-25 2xl:max-w-50 break-all"
                >
                  {image.explanation_image}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '出題形式',
          dataIndex: 'type',
          key: 'type',
          render: (element) => (
            <> {element?.type === EQuestionForm.SINGLE ? '単一' : '複数'}</>
          ),
        },
        {
          title: '設問1',
          dataIndex: 'sub_questions_1',
          key: 'sub_questions_1',
          render: (element) => (
            <>
              {element?.sub_questions[0]?.map((choice: any, index: any) => (
                <div
                  key={`sub_questions_1_${index}`}
                  className="min-w-25 max-w-25 2xl:max-w-50 break-all"
                >
                  {choice.is_correct === 1 ? '○' : '×'} : {choice?.content}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '設問2',
          dataIndex: 'sub_questions_2',
          key: 'sub_questions_2',
          render: (element) => (
            <>
              {element?.sub_questions[1]?.map((choice: any, index: any) => (
                <div
                  key={`sub_questions_2_${index}`}
                  className="min-w-25 max-w-25 2xl:max-w-50 break-all"
                >
                  {choice.is_correct === 1 ? '○' : '×'} : {choice?.content}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '設問3',
          dataIndex: 'sub_questions_3',
          key: 'sub_questions_3',
          render: (element) => (
            <>
              {element?.sub_questions[2]?.map((choice: any, index: any) => (
                <div
                  key={`sub_questions_3_${index}`}
                  className="min-w-25 max-w-25 2xl:max-w-50 break-all"
                >
                  {choice.is_correct === 1 ? '○' : '×'} : {choice?.content}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '設問4',
          dataIndex: 'sub_questions_4',
          key: 'sub_questions_4',
          render: (element) => (
            <>
              {element?.sub_questions[3]?.map((choice: any, index: any) => (
                <div
                  key={`sub_questions_4_${index}`}
                  className="min-w-25 max-w-25 2xl:max-w-50 break-all"
                >
                  {choice.is_correct === 1 ? '○' : '×'} : {choice?.content}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '法令名',
          dataIndex: 'laws',
          key: 'laws',
          render: (element) => (
            <>
              {element?.laws?.map((law: any, index: any) => (
                <div
                  key={`law_${index}`}
                  className="min-w-35 max-w-50 break-all"
                >
                  {`(${law.id}) ${law.name}`}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '対策動画名',
          dataIndex: 'videos',
          key: 'videos',
          render: (element) => (
            <>
              {element?.videos?.map((video: any, index: any) => (
                <div
                  key={`video_${index}`}
                  className="min-w-35 max-w-50 break-all"
                >
                  {`(${video.id}) ${video.name}`}
                </div>
              ))}
            </>
          ),
        },
        {
          title: 'テキスト名',
          dataIndex: 'digital_texts',
          key: 'digital_texts',
          render: (element) => (
            <>
              {element?.digital_texts?.map((digital_text: any, index: any) => (
                <div
                  key={`digital_${index}`}
                  className="min-w-35 max-w-50 break-all"
                >
                  {`(${digital_text.id}) ${digital_text.name}`}
                </div>
              ))}
            </>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'is_active',
          key: 'is_active',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
      ] as Array<
        ColumnType<
          TUploadCreateQuestionsResponse | TUploadUpdateQuestionsResponse
        >
      >,
    []
  );

  return (
    <div className="grid grid-cols-1 mt-8 bg-alice-blue py-8 px-5">
      <div className="flex flex-col items-center gap-y-4 justify-between -mt-4">
        <div className="w-full text-left">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">
            {previewType && previewType === 1 ? '登録' : '更新'}
            <span className="text-primary ml-5">
              {pagination?.total ? pagination?.total : 0}件
            </span>
          </h2>
        </div>
        <Table<TUploadCreateQuestionsResponse | TUploadUpdateQuestionsResponse>
          columns={columns}
          dataSource={data}
          isPreview={true}
        />
        {data.length !== 0 && (
          <div className="w-full flex justify-center relative">
            <Pagination
              current={pagination.current_page || 0}
              total={pagination?.last_page || 0}
              onChange={handleChangePage}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PreviewExamsQuestions;
