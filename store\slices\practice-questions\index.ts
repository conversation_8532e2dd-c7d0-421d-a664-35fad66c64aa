import {
  TGetPracticeQuestionResponse,
  TGetPracticeQuestionsResponse,
} from '@/services/apis';
import {
  EGetPracticeQuestionAction,
  EGetPracticeQuestionsAction,
  TGetPracticeQuestionSuccess,
  TGetPracticeQuestionsSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TPracticeQuestionsState {
  getPracticeQuestionResponse?: TGetPracticeQuestionResponse;
  getPracticeQuestionsResponse?: TGetPracticeQuestionsResponse;
}

const initialState: TPracticeQuestionsState = {
  getPracticeQuestionsResponse: undefined,
};

const practiceQuestionsSlice = createSlice({
  name: 'practice-questions-slice',
  initialState,
  reducers: {
    resetPracticeQuestionsSlice: (state) => ({
      ...state,
      getPracticeQuestionResponse: undefined,
    }),
    resetGetPracticeQuestionsResponse: (state) => ({
      ...state,
      getPracticeQuestionsResponse: undefined,
    }),
  },
  extraReducers: {
    [EGetPracticeQuestionAction.GET_PRACTICE_QUESTION_SUCCESS]: (
      state: TPracticeQuestionsState,
      action: TGetPracticeQuestionSuccess
    ): TPracticeQuestionsState => ({
      ...state,
      getPracticeQuestionResponse: action.payload?.response,
    }),
    [EGetPracticeQuestionsAction.GET_PRACTICE_QUESTIONS_SUCCESS]: (
      state: TPracticeQuestionsState,
      action: TGetPracticeQuestionsSuccess
    ): TPracticeQuestionsState => ({
      ...state,
      getPracticeQuestionsResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: practiceQuestionsReducer,
  actions: { resetPracticeQuestionsSlice, resetGetPracticeQuestionsResponse },
} = practiceQuestionsSlice;

export {
  practiceQuestionsReducer,
  resetPracticeQuestionsSlice,
  resetGetPracticeQuestionsResponse,
};
