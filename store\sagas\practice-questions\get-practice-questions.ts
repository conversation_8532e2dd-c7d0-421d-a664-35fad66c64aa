import {
  TGetPracticeQuestionsResponse,
  getPracticeQuestions,
} from '@/services/apis';
import { getPracticeQuestionsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getPracticeQuestionsSaga(
  action: ActionType<typeof getPracticeQuestionsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getPracticeQuestions, materials);
    const getPracticeQuestionsResponse =
      response as TGetPracticeQuestionsResponse;
    yield put(getPracticeQuestionsAction.success(getPracticeQuestionsResponse));
    successCallback?.(getPracticeQuestionsResponse);
  } catch (err) {
    yield put(getPracticeQuestionsAction.failure(err));
    failedCallback?.(err);
  }
}
