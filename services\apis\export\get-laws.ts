import ApiService from '@/services/apis';

export type TGetExportLawsParams = {
  category_id?: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetExportLawsMaterials = {
  params?: TGetExportLawsParams;
};

export type TGetExportLawsResponse = Blob;

export const getExportLaws = async (
  materials: TGetExportLawsMaterials
): Promise<TGetExportLawsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/export/laws', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
