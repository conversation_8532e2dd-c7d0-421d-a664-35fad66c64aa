import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function PresentationIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15.004"
      height="17"
      viewBox="1225 330 15.004 17"
    >
      <g data-name="presentation">
        <path
          d="M1239.388 330h-13.772a.616.616 0 0 0-.616.616v10.498c0 .34.276.616.616.616h5.872l-2.268 4.37a.616.616 0 0 0 1.094.567l1.572-3.029v1.516a.616.616 0 0 0 1.232 0v-1.516l1.573 3.03a.616.616 0 1 0 1.094-.568l-2.269-4.37h5.872c.34 0 .616-.276.616-.616v-10.498a.616.616 0 0 0-.616-.616Zm-.616 10.498h-12.54v-9.266h12.54v9.266Z"
          fill="#3e87bf"
          fillRule="evenodd"
          data-name="Path 585"
        />
        <path
          d="M1227.686 339.211c.158 0 .315-.06.436-.18l2.03-2.03 1.87 1.87a.617.617 0 0 0 .87 0l4.027-4.026a.616.616 0 0 0-.872-.872l-3.59 3.59-1.87-1.87a.617.617 0 0 0-.871 0l-2.466 2.466a.616.616 0 0 0 .436 1.052Z"
          fill="#3e87bf"
          fillRule="evenodd"
          data-name="Path 586"
        />
      </g>
    </svg>
  );
}
