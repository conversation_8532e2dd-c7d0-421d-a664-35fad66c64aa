
import { DataLaws, TLaws } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetLaws = any;

export const createLaws = async (payload: DataLaws): Promise<TGetLaws> => {
  const response = await ApiService.post('/laws', payload);
  return response.data;
};

export const getDetailLaws = async (id: any): Promise<TGetLaws> => {
  const response = await ApiService.get(`/laws/${id}`);
  return response.data;
};

export const updateLaws = async (id: any, payload: DataLaws): Promise<TGetLaws> => {
  const response = await ApiService.put(`/laws/${id}`, payload);
  return response.data;
};

export const deleteLaws = async (id: any): Promise<TGetLaws> => {
  const response = await ApiService.delete(`/laws/${id}`);
  return response.data;
};
