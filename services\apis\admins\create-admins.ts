import ApiService from '@/services/apis';

export type TPostCreateAdmins = {
  username: string;
  username_kana: string;
  login_id: string;
  password: string;
  role_id: number;
  is_active: number;
};

export type TCreateAdminsResponse = unknown;

export const postCreateAdmins = async (
  payload: TPostCreateAdmins
): Promise<TCreateAdminsResponse> => {
  const response = await ApiService.post('/admins', payload);
  return response.data;
};
