import { TGetExamSettingResponse, postQuestion } from '@/services/apis';
import { postQuestionAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* postQuestionSaga(
  action: ActionType<typeof postQuestionAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(postQuestion, materials);
    const postQuestionResponse = response as TGetExamSettingResponse;
    yield put(postQuestionAction.success(postQuestionResponse));
    successCallback?.(postQuestionResponse);
  } catch (err) {
    yield put(postQuestionAction.failure(err));
    failedCallback?.(err);
  }
}
