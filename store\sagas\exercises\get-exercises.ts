import { TGetExerciseResponse, getExercises } from '@/services/apis';
import { getExerciseAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getExercisesSaga(
  action: ActionType<typeof getExerciseAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getExercises, materials);
    const getExercisesResponse = response as TGetExerciseResponse;
    yield put(getExerciseAction.success(getExercisesResponse));
    successCallback?.(getExercisesResponse);
  } catch (err) {
    yield put(getExerciseAction.failure(err));
    failedCallback?.(err);
  }
}
