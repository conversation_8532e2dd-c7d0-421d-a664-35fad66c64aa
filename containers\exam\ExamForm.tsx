import { useMemo } from 'react';
import { EIsActive } from '@/commons/enums';
import { Input, InputNumber } from '@/components';
import { Form, Radio } from 'antd';
import { TExamForm, TExamFormProps } from './mock-tests.types';
import { validationMessage } from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';

const {
  checkMaxLength,
  hasWhiteSpace,
  checkBetweenNumberValue,
} = validate;

const ExamForm = ({ formInstance, handleSubmit }: TExamFormProps) => {
  const onSubmit = (values: TExamForm) => {
    handleSubmit(values);
  };
  const rulesName = useMemo(() => {
    const checkLength = () => checkMaxLength('模擬試験名', 255);
    return [
      {
        required: true,
        message: validationMessage.required('模擬試験名'),
      },
      checkLength,
    ];
  }, []) as Array<Rule>;

  const rulesOrder = useMemo(() => {
    const checkValue = () => checkBetweenNumberValue('表示順', 1, 99999);
    return [
      {
        required: true,
        message: validationMessage.required('表示順'),
      },
      checkValue,
    ];
  }, []) as Array<Rule>;

  const rulesIsActive = useMemo(
    () => [
      {
        required: true,
        message: validationMessage.required('表示順'),
      },
    ],
    []
  );
  return (
    <Form
      form={formInstance}
      onFinish={onSubmit}
      className="grid grid-cols-1 xl:grid-cols-2 gap-x-5 gap-y-1"
    >
      <div className="col-span-1 grid-cols-1 grid gap-2">
        <p className="text-black-kj font-bold">模擬試験名</p>
        <Form.Item name="name" rules={rulesName}>
          <Input
            classNames="w-full !rounded !text-sm !py-[9px]"
            size="large"
            maxLength={255}
          />
        </Form.Item>
      </div>
      <div className="col-span-1 grid-cols-1 grid gap-2">
        <p className="text-black-kj font-bold">表示順</p>
        <Form.Item name="show_order" rules={rulesOrder}>
          <InputNumber
            hideControls
            classNames="!w-full !rounded !text-sm"
            size="large"
            maxLength={5}
            stringMode
          />
        </Form.Item>
      </div>
      <div className="col-span-1 grid-cols-1 grid gap-2 !font-bold">
        <p className="text-black-kj">有効・無効</p>
        <Form.Item
          name="is_active"
          rules={rulesIsActive}
          initialValue={EIsActive.ENABLE}
        >
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    </Form>
  );
};

export default ExamForm;
