import { TDeleteVideoMaterials, TDeleteVideoResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EDeleteVideoAction {
  DELETE_VIDEO = 'DELETE_VIDEO',
  DELETE_VIDEO_REQUEST = 'DELETE_VIDEO_REQUEST',
  DELETE_VIDEO_SUCCESS = 'DELETE_VIDEO_SUCCESS',
  DELETE_VIDEO_FAILED = 'DELETE_VIDEO_FAILED',
}

// TYPES

export type TDeleteVideoRequest = {
  type: EDeleteVideoAction.DELETE_VIDEO_REQUEST;
  payload: {
    materials: TDeleteVideoMaterials;
    successCallback?: (response: TDeleteVideoResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TDeleteVideoSuccess = {
  type: EDeleteVideoAction.DELETE_VIDEO_SUCCESS;
  payload: { response?: TDeleteVideoResponse };
};

export type TDeleteVideoFailed = {
  type: EDeleteVideoAction.DELETE_VIDEO_FAILED;
};

export const deleteVideoAction = {
  request: createActionCreator(
    EDeleteVideoAction.DELETE_VIDEO_REQUEST,
    (resolve) =>
      (
        materials: TDeleteVideoMaterials,
        successCallback?: (response: TDeleteVideoResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TDeleteVideoRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EDeleteVideoAction.DELETE_VIDEO_SUCCESS,
    (resolve) =>
      (response?: TDeleteVideoResponse): TDeleteVideoSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EDeleteVideoAction.DELETE_VIDEO_FAILED,
    (resolve) =>
      (error: unknown): TDeleteVideoFailed =>
        resolve({ error })
  ),
};
