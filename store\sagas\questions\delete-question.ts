import { TGetQuestionResponse, deleteQuestion } from '@/services/apis';
import { deleteQuestionAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* deleteQuestionSaga(
  action: ActionType<typeof deleteQuestionAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(deleteQuestion, materials);
    const deleteQuestionResponse = response as TGetQuestionResponse;
    yield put(deleteQuestionAction.success(deleteQuestionResponse));
    successCallback?.(deleteQuestionResponse);
  } catch (err) {
    yield put(deleteQuestionAction.failure(err));
    failedCallback?.(err);
  }
}
