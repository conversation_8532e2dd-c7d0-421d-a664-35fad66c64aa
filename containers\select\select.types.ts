import { TOptionCategory, TOptionDigital, TOptionLaws } from '@/commons/types';
import { TCategory } from '@/services/apis';
import { SizeType } from 'antd/lib/config-provider/SizeContext';

export type TSelectBigCategoryProps = {
  value?: string | undefined;
  onChange?: (value: string) => void;
  size?: SizeType;
  className?: string;
  placeholder?: string;
  initOption?: TCategory;
  disabled?: boolean;
};

export type TFilterSelectCategory = {
  page?: number;
  name?: string;
};

export type TSelectSubCategoryProps = {
  category_id?: string;
  value?: string;
  onChange?: (value: string) => void;
  size?: SizeType;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
};

export type TSelectLawProps = {
  category_id?: string;
  value?: number | string;
  onChange?: (value: TOptionLaws) => void;
  size?: SizeType;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
};

export type TSelectDigitalProps = {
  category_id?: string;
  value?: number | string;
  onChange?: (value: TOptionDigital) => void;
  size?: SizeType;
  className?: string;
  placeholder?: string;
  disabled?: boolean
};

export type TSelectAvailableDiscountProps = {
  value?: string | undefined;
  onChange?: (value: string) => void;
  size?: SizeType;
  className?: string;
  placeholder?: string;
  initOption?: TCategory;
  disabled?: boolean;
};
