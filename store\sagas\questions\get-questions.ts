import { TGetQuestionsResponse, getQuestions } from '@/services/apis';
import { getQuestionsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getQuestionsSaga(
  action: ActionType<typeof getQuestionsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getQuestions, materials);
    const getQuestionsResponse = response as TGetQuestionsResponse;
    yield put(getQuestionsAction.success(getQuestionsResponse));
    successCallback?.(getQuestionsResponse);
  } catch (err) {
    yield put(getQuestionsAction.failure(err));
    failedCallback?.(err);
  }
}
