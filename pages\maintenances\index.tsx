import { Input as AntdInput, Form, Radio, message } from 'antd';
import validate from '@/utils/validate';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import dynamic from 'next/dynamic';
import { Rule } from 'antd/lib/form';
import { useEffect, useState } from 'react';
import {
  getMaintenacesSettings,
  helpers,
  postMaintenacesSettings,
} from '@/services/apis';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { EPlacement, ERoleId, ETypeNotification } from '@/commons/enums';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';

const EditorBoxForm = dynamic(
  () => import('@/components/EditorBox/EditorBoxForm'),
  {
    ssr: false,
  }
);

const MaintenancesSetting = () => {
  const { checkMaxLengthEditor } = validate;
  const [whiteListIPs, setWhiteListIPs] = useState<Array<String>>([]);
  const [ipValue, setIpValue] = useState<string>();
  const [isError, setIsError] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const { checkEmptyTextEditor } = validate;
  const [form] = Form.useForm();
  const router = useRouter();

  const onCloseModal = () => {
    setIsShowModal(false);
  };

  const onOpenModal = () => {
    setIsShowModal(true);
  };

  const onChangeIPValue = (value: string) => {
    setIpValue(value.trim());
    onValidateIPAdress(value, whiteListIPs);
  };

  const onValidateIPAdress = (value: string, whiteList: Array<String>) => {
    const regexExp =
      /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/gi;

    if (!regexExp.test(value) && value !== '') {
      setIsError(true);
      setErrorMessage(validationMessage.ip('アクセス可能IP'));
      return false;
    }

    if (whiteList.indexOf(value) >= 0) {
      setIsError(true);
      setErrorMessage(validationMessage.unique('アクセス可能IP'));
      return false;
    }

    setIsError(false);
    return true;
  };

  const onAddIP = () => {
    if (onValidateIPAdress(ipValue as string, whiteListIPs)) {
      const newListIPs = [...whiteListIPs];
      if (!isError && ipValue) {
        newListIPs.push(ipValue);
        setWhiteListIPs([...newListIPs]);
        setIpValue('');
      }
    }
  };

  const onDeleteIP = (index: number) => {
    const newListIPs = [...whiteListIPs];
    newListIPs.splice(index, 1);

    setWhiteListIPs([...newListIPs]);
  };

  const onSubmit = (values: any) => {
    if (whiteListIPs.length <= 0) {
      setIsError(true);
      setErrorMessage(validationMessage.required('アクセス可能IP'));
      return;
    }
    onOpenModal();
  };

  const onHandleConfirmSubmit = () => {
    onUpdateMaintenancesSettings();
  };

  const fetchMaintenancesSettings = async () => {
    setIsLoading(true);
    try {
      const data = await getMaintenacesSettings();
      form.setFieldsValue({
        is_active: data.is_active,
        content: data.content,
      });

      setWhiteListIPs([...data.whitelist_ips]);
    } catch (error: any) {
      if (error?.request.status !== 503) {
        showNotification(
          ETypeNotification.ERROR,
          error?.data?.message
            ? error?.data?.message
            : EXCEPTION.ACTION_FAILURE,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
    setIsLoading(false);
  };

  const onUpdateMaintenancesSettings = async () => {
    try {
      const payload = {
        is_active: form.getFieldValue('is_active'),
        content: form.getFieldValue('content'),
        whitelist_ips: [...whiteListIPs],
      };

      const data = await postMaintenacesSettings(payload);

      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  const backToHome = () => {
    router.push('/');
  };

  useEffect(() => {
    fetchMaintenancesSettings();
  }, []);

  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">メンテナンス設定</div>
      {!isLoading && (
        <Form onFinish={onSubmit} form={form} initialValues={{ is_active: 2 }}>
          <div className="bg-alice-blue-light py-4 px-5">
            <div className="flex flex-row justify-between">
              <div className="w-full mr-8">
                <div>
                  <div className="text-textGray text-sm font-bold mb-2">
                    メンテナンスメッセージ
                  </div>
                  <Form.Item
                    rules={
                      [
                        () => checkEmptyTextEditor('メンテナンスメッセージ'),
                        () =>
                          checkMaxLengthEditor(
                            'メンテナンスメッセージ',
                            1,
                            65535
                          ),
                      ] as Rule[]
                    }
                    name="content"
                  >
                    <EditorBoxForm className="max-" />
                  </Form.Item>
                </div>
                <div>
                  <div className="flex flex-row">
                    <span className="text-textGray text-sm font-bold">
                      有効・無効
                    </span>
                  </div>
                  <div className="flex flex-row items-center mt-2">
                    <Form.Item
                      name="is_active"
                      rules={[
                        {
                          required: true,
                          message: validationMessage.required('有効・無効'),
                        },
                      ]}
                      className="!mb-0"
                    >
                      <Radio.Group>
                        <Radio className="font-bold" value={1}>
                          有効
                        </Radio>
                        <Radio className="font-bold" value={2}>
                          無効
                        </Radio>
                      </Radio.Group>
                    </Form.Item>
                  </div>
                </div>
              </div>
              <div className="w-full ml-8">
                <div className="mb-4">
                  <div className="text-textGray text-sm font-bold mb-2">
                    アクセス可能IP
                  </div>
                  <div>
                    <div className="flex justify-between items-center">
                      <AntdInput
                        value={ipValue}
                        onChange={(e) => onChangeIPValue(e.target.value)}
                        status={isError ? 'error' : ''}
                        onFocus={(e) =>
                          onValidateIPAdress(e.target.value, whiteListIPs)
                        }
                      />
                      <Button
                        onClick={onAddIP}
                        size="small"
                        styleType={EButtonStyleType.PRIMARY_VARIANT}
                        title={'＋IP追加'}
                        className="ml-4"
                      />
                    </div>
                    {isError && (
                      <div style={{ color: '#ff4d4f' }}>{errorMessage}</div>
                    )}
                  </div>
                </div>
                <div>
                  {whiteListIPs &&
                    whiteListIPs.map((ip, index) => (
                      <div
                        key={index}
                        className="flex justify-between border bg-white p-1 mb-1"
                      >
                        {ip}
                        <span
                          onClick={() => onDeleteIP(index)}
                          className="cursor-pointer font-bold text-red-400"
                        >
                          {/* <EditIcon /> */}
                          削除
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-row justify-end mt-2.5 gap-4">
            <Button
              size={'small'}
              styleType={EButtonStyleType.OUTLINE_PRIMARY}
              title={'戻る'}
              onClick={backToHome}
            />
            <Button
              htmlType={'submit'}
              size={'small'}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title={'設定'}
            />
          </div>
        </Form>
      )}
      <ConfirmModal
        visible={isShowModal}
        onClose={onCloseModal}
        onConfirm={onHandleConfirmSubmit}
        content={MODAL_MESSAGE.CONFIRM_CREATE}
      />
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default MaintenancesSetting;
