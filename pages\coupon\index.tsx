import { ERoleId } from '@/commons/enums';
import CouponListOrganism from '@/containers/coupon/CouponList';
import { TFilterCoupon } from '@/containers/coupon/coupon.types';
import helpers from '@/services/helpers';
import { GetServerSideProps } from 'next';

const ListUserPage = ({ query }: { query: TFilterCoupon }) => {
  return (
    <div className="mx-12 my-4">
      <h1 className="text-lg font-bold text-black-kj">クーポン一覧</h1>
      <div className="mt-3 w-full">
        <CouponListOrganism query={query} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: { query },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default ListUserPage;
