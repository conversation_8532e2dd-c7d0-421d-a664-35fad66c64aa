import { ERoleId } from '@/commons/enums';
import { ListCategory, TFilterCategory } from '@/containers';
import helpers from '@/services/helpers';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';
import { useMemo } from 'react';

const CategoryListPage = ({ filter }: { filter: TFilterCategory }) => {
  return (
    <div className="mx-12 my-4">
      <h1 className="text-lg font-bold text-black-kj">大項目一覧</h1>
      <div className="mt-3 w-full">
        <ListCategory query={filter} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    const is_actives =
      query?.is_actives
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const filter = removeEmpty({ ...query, is_actives });
    return {
      props: { filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default CategoryListPage;
