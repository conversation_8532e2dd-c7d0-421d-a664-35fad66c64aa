import { useCallback, useMemo } from 'react';
import QuestionChoice from './QuestionChoice';
import { TMultipleChoiceProps } from './questions.types';
import { TSubQuestion } from '@/commons/types';
import clsx from 'clsx';

const MultipleChoice = ({
  titleNums,
  choices,
  disabled,
  choiceSelected,
  choiceCorrect,
  showTitle,
  handleChoice,
}: TMultipleChoiceProps) => {
  const isSelected = useCallback(
    (choice: TSubQuestion) => {
      const choiceIds = choiceSelected as Array<number>;
      return !!choice?.id && choiceIds?.includes(choice.id);
    },
    [choiceSelected]
  );
  const isCorrect = useCallback(
    (choice: TSubQuestion) => {
      const choiceIds = choiceCorrect as Array<number>;
      const choiceSelectIds = choiceSelected as Array<number>;
      return (
        !!choice?.id &&
        choiceIds?.includes(choice.id) &&
        choiceSelectIds?.includes(choice?.id)
      );
    },
    [choiceCorrect, choiceSelected]
  );

  const isCorrectNotChoice = useCallback(
    (choice: TSubQuestion) => {
      const choiceIds = choiceCorrect as Array<number>;
      const choiceSelectIds = choiceSelected as Array<number>;
      return (
        !!choice?.id &&
        choiceIds?.includes(choice.id) &&
        !choiceSelectIds?.includes(choice?.id)
      );
    },
    [choiceCorrect, choiceSelected]
  );
  const isFail = useCallback(
    (choice: TSubQuestion) => {
      const choiceIds = choiceCorrect as Array<number>;
      const choiceSelectIds = choiceSelected as Array<number>;
      if (!choiceCorrect) {
        return false;
      }
      return (
        !!choice?.id &&
        !choiceIds?.includes(choice.id) &&
        choiceSelectIds?.includes(choice.id)
      );
    },
    [choiceCorrect, choiceSelected]
  );
  const handleSelectChoice = useCallback(
    (choice: TSubQuestion) => {
      handleChoice?.(choice?.id);
    },
    [handleChoice]
  );

  return (
    <div className="flex items-start justify-between gap-4">
      <div
        className={clsx(
          'flex-none font-bold text-sm text-textGray',
          !showTitle && 'hidden'
        )}
      >
        {titleNums}
      </div>
      <div className="flex-grow flex flex-wrap gap-x-5 gap-y-2.5 justify-center">
        {choices.map((choice) => (
          <QuestionChoice
            key={choice.id}
            choice={choice}
            disabled={disabled}
            onClick={() => handleSelectChoice(choice)}
            isSelected={isSelected(choice)}
            isCorrect={isCorrect(choice)}
            isFail={isFail(choice)}
            isCorrectNotChoice={isCorrectNotChoice(choice)}
          />
        ))}
      </div>
      <div className="flex-none invisible">{titleNums}</div>
    </div>
  );
};

export default MultipleChoice;
