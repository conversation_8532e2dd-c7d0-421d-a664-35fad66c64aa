import axios, {
  AxiosError,
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import { EResponseCode, ETypeNotification } from '@/commons/enums';
import Helpers from './helpers';
import { helpers } from './apis';
import { EXCEPTION } from '@/commons/messages-enum';
import { showNotification } from '@/utils/functions';
// import { getSession } from 'next-auth/react';

// let isRefreshingAccessToken = false;

const AuthorizedInstance = (baseURL: string) => {
  const instance = axios.create({
    baseURL,
  }) as AxiosInstance;

  const onRequest = async (request: InternalAxiosRequestConfig) => {
    // const session = await getSession();
    // const { accessToken } = (session?.user as any) || {};
    const accessToken = Helpers.getAccessToken();
    if (accessToken && request?.headers && !request.headers.Authorization) {
      request.headers.Authorization = `Bearer ${accessToken}`;
    }
    return request;
  };

  const onResponseSuccess = (response: AxiosResponse): AxiosResponse =>
    response;

  const onResponseError = async (axiosError: AxiosError) => {
    const { response, code } = axiosError;
    const originalRequest = axiosError.config;
    const responseStatus = response?.status;

    if (code === 'ERR_NETWORK') {
      if (!navigator.onLine) {
        showNotification(ETypeNotification.ERROR, EXCEPTION.ERROR_NETWORK);
      }
      return Promise.reject();
    }
    if (
      responseStatus === EResponseCode.NOT_FOUND &&
      originalRequest &&
      originalRequest.method === 'get'
    ) {
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          window.location.href = '/404';
        }, 0);
      }
    }
    if (responseStatus === EResponseCode.UNAUTHORIZED && originalRequest) {
      // if (!isRefreshingAccessToken) {
      //   isRefreshingAccessToken = true;
      // }
      // return Promise.reject(axiosError);
      if (typeof window !== 'undefined') {
        helpers.clearTokens();
        setTimeout(() => {
          window.location.href = '/login';
        }, 0);
      }
    }
    if (
      responseStatus === EResponseCode.SERVICE_UNAVAILABLE &&
      originalRequest &&
      typeof window !== 'undefined'
    ) {
      // window.location.href = '/503';
      const maintaining = new CustomEvent('maintaining', {
        detail: {
          response,
        },
      });
      window.dispatchEvent(maintaining);
      return Promise.reject(response);
    }
    if (
      responseStatus === EResponseCode.FORBIDDEN &&
      originalRequest &&
      typeof window !== 'undefined'
    ) {
      window.location.href = '/403';
    }

    if (
      responseStatus === EResponseCode.GATEWAY_TIMEOUT &&
      originalRequest &&
      typeof window !== 'undefined'
    ) {
      showNotification(ETypeNotification.ERROR, EXCEPTION.FORBIDDEN);
      return Promise.reject();
    }
    return Promise.reject(response);
  };
  instance.interceptors.request.use(onRequest);
  instance.interceptors.response.use(onResponseSuccess, onResponseError);
  return instance;
};

export default AuthorizedInstance;
