import { Button, CloseIcon, DeleteIcon, EButtonStyleType } from '@/components';
import clsx from 'clsx';
import { Fragment, MouseEvent, useState } from 'react';
import { TSelectItemFormProps } from './mock-tests.types';
import CustomerModal from '@/components/Modal/CustomerModal';
import SearchVideo from './SearchVideo';
import { TOptionSub } from '@/commons/types';

const FormVideoItem = ({
  value,
  onChange,
  disable,
}: TSelectItemFormProps<TOptionSub>) => {
  const [isVisibleSearch, setIsVisibleSearch] = useState<boolean>(false);

  const showSearchModal = () => {
    setIsVisibleSearch(true);
  };

  const handleRemove = (e: MouseEvent<HTMLButtonElement>) => {
    onChange?.(undefined);
    e.preventDefault();
  };

  const onChangeVideo = (value: TOptionSub) => {
    onChange?.(value);
    setIsVisibleSearch(false);
  };

  const closeModal = () => {
    setIsVisibleSearch(false);
  };

  return (
    <Fragment>
      {isVisibleSearch && (
        <CustomerModal visible={true} onClose={closeModal}>
          <SearchVideo
            value={value}
            onChangeVideo={onChangeVideo}
            onClose={closeModal}
          />
        </CustomerModal>
      )}
      <div className="flex gap-2.5 items-center mr-5">
        <div
          className={clsx(
            'flex-grow border border-black border-opacity-10',
            'rounded h-10 flex items-center px-3 justify-between',
            'bg-white'
          )}
        >
          <div className="relative flex-grow h-full">
            <div className="w-full absolute h-full top-0 left-0 flex items-center">
              <p
                className={clsx(
                  'flex-grow truncate text-sm overflow-hidden input-disabled',
                  disable ? 'text-gray-400' : 'text-black'
                )}
              >
                {value?.name}
              </p>
            </div>
          </div>
          {value && !disable && (
            <button onClick={handleRemove} disabled={disable}>
              <DeleteIcon />
            </button>
          )}
        </div>
        <Button
          title="検索"
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          onClick={showSearchModal}
        />
      </div>
    </Fragment>
  );
};

export default FormVideoItem;
