import ApiService from '@/services/apis';

export type TCategory = {
  id: number;
  name: string;
  is_active: number;
  show_order: number;
};

export type TPutUpdateCategoryResponse = {
  id: number;
  name: string;
  is_active: number;
  show_order: number;
};

export const putUpdateCategory = async (
  payload: TCategory
): Promise<TPutUpdateCategoryResponse> => {
  const response = await ApiService.put(`/categories/${payload?.id}`, payload);
  return response.data;
};
