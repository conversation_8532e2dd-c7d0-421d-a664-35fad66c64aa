import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function Delete(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="13.673" height="16"
      viewBox="646 1037 13.673 16">
      <path
        d="M659.236 1039.327h-3.054v-.727a1.6 1.6 0 0 0-1.6-1.6h-3.491a1.6 1.6 0 0 0-1.6 1.6v.727h-3.055a.436.436 0 0 0 0 .873h.309l.913 11.328a1.612 1.612 0 0 0 1.594 1.472h7.168a1.612 1.612 0 0 0 1.595-1.472l.913-11.328h.308a.436.436 0 1 0 0-.873Zm-8.872-.727c0-.402.325-.727.727-.727h3.49c.402 0 .728.325.728.727v.727h-4.945v-.727Zm6.778 12.858a.733.733 0 0 1-.727.67h-7.163a.733.733 0 0 1-.721-.67l-.931-11.258h10.473l-.931 11.258Zm-1.542-8.203v5.818a.436.436 0 0 1-.873 0v-5.818a.436.436 0 0 1 .873 0Zm-2.327 0v5.818a.436.436 0 0 1-.873 0v-5.818a.436.436 0 0 1 .873 0Zm-2.328 0v5.818a.436.436 0 0 1-.872 0v-5.818a.436.436 0 0 1 .872 0Z"
        fill={props?.fill || 'red'}
        fillRule="evenodd"
        data-name="delete" />
    </svg>
  );
}
