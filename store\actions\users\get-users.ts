import {
  TGetCategoriesMaterials,
  TGetCategoriesResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetUsersAction {
  GET_USERS = 'GET_USERS',
  GET_USERS_REQUEST = 'GET_USERS_REQUEST',
  GET_USERS_SUCCESS = 'GET_USERS_SUCCESS',
  GET_USERS_FAILED = 'GET_USERS_FAILED',
}

// TYPES

export type TGetUsersRequest = {
  type: EGetUsersAction.GET_USERS_REQUEST;
  payload: {
    materials: any;
    successCallback?: (response: any) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetUsersSuccess = {
  type: EGetUsersAction.GET_USERS_SUCCESS;
  payload: { response?: any };
};

export type TGetUsersFailed = {
  type: EGetUsersAction.GET_USERS_FAILED;
};

export const getUsersAction = {
  request: createActionCreator(
    EGetUsersAction.GET_USERS_REQUEST,
    (resolve) =>
      (
        materials: any,
        successCallback?: (response: any) => void,
        failedCallback?: (err: unknown) => void
      ): TGetUsersRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetUsersAction.GET_USERS_SUCCESS,
    (resolve) =>
      (response?: any): TGetUsersSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetUsersAction.GET_USERS_FAILED,
    (resolve) =>
      (error: unknown): TGetUsersFailed =>
        resolve({ error })
  ),
};
