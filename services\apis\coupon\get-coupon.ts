import { TCouponPlan } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetCoupon = {
  id: string;
};

export type TGetCouponMaterials = {
  paths: TGetCoupon;
};

export type TGetCouponResponse = {
  id: number,
  name: string,
  code: string,
  start_date: string,
  end_date: string,
  training_school: string,
  discount: number,
  note: string
  coupon_plans: Array<TCouponPlan>
};

export const getCoupon = async (
  { paths }: TGetCouponMaterials
): Promise<TGetCouponResponse> => {
  const response = await ApiService.get(`/coupons/${paths.id}`);
  return response.data.data;
};
