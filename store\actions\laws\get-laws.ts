import {
  TGetLawsMaterials,
  TGetLawsResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetLawsAction {
  GET_LAWS = 'GET_LAWS',
  GET_LAWS_REQUEST = 'GET_LAWS_REQUEST',
  GET_LAWS_SUCCESS = 'GET_LAWS_SUCCESS',
  GET_LAWS_FAILED = 'GET_LAWS_FAILED',
}

// TYPES

export type TGetLawsRequest = {
  type: EGetLawsAction.GET_LAWS_REQUEST;
  payload: {
    materials: TGetLawsMaterials;
    successCallback?: (response: TGetLawsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetLawsSuccess = {
  type: EGetLawsAction.GET_LAWS_SUCCESS;
  payload: { response?: TGetLawsResponse };
};

export type TGetLawsFailed = {
  type: EGetLawsAction.GET_LAWS_FAILED;
};

export const getLawsAction = {
  request: createActionCreator(
    EGetLawsAction.GET_LAWS_REQUEST,
    (resolve) =>
      (
        materials: TGetLawsMaterials,
        successCallback?: (response: TGetLawsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetLawsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetLawsAction.GET_LAWS_SUCCESS,
    (resolve) =>
      (response?: TGetLawsResponse): TGetLawsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetLawsAction.GET_LAWS_FAILED,
    (resolve) =>
      (error: unknown): TGetLawsFailed =>
        resolve({ error })
  ),
};
