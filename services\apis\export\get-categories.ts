import { TCategory, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetExportCategoriesParams = {
  name?: string;
  is_actives?: Array<number>;
};

export type TGetExportCategoriesMaterials = {
  params?: TGetExportCategoriesParams;
};

export type TGetExportCategoriesResponse = Blob;

export const getExportCategories = async (
  materials: TGetExportCategoriesMaterials
): Promise<TGetExportCategoriesResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/export/categories', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
