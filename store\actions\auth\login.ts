import { TPostLoginMaterials, TPostLoginResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPostLoginAction {
  POST_LOGIN = 'POST_LOGIN',
  POST_LOGIN_REQUEST = 'POST_LOGIN_REQUEST',
  POST_LOGIN_SUCCESS = 'POST_LOGIN_SUCCESS',
  POST_LOGIN_FAILED = 'POST_LOGIN_FAILED',
}

// TYPES

export type TPostLoginRequest = {
  type: EPostLoginAction.POST_LOGIN_REQUEST;
  payload: {
    materials: TPostLoginMaterials;
    successCallback?: (response: TPostLoginResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPostLoginSuccess = {
  type: EPostLoginAction.POST_LOGIN_SUCCESS;
  payload: { response?: TPostLoginResponse };
};

export type TPostLoginFailed = {
  type: EPostLoginAction.POST_LOGIN_FAILED;
};

export const postLoginAction = {
  request: createActionCreator(
    EPostLoginAction.POST_LOGIN_REQUEST,
    (resolve) =>
      (
        materials: TPostLoginMaterials,
        successCallback?: (response: TPostLoginResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TPostLoginRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPostLoginAction.POST_LOGIN_SUCCESS,
    (resolve) =>
      (response?: TPostLoginResponse): TPostLoginSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPostLoginAction.POST_LOGIN_FAILED,
    (resolve) =>
      (error: unknown): TPostLoginFailed =>
        resolve({ error })
  ),
};
