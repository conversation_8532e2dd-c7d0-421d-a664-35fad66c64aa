import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function ArrowLeftIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      version="1.1"
      id="triangle-11"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 11 11"
      transform="rotate(180, 0, 0)"
    >
      <path
        id="rect3338"
        d="M5.5174,1.2315&#xA;&#x9;C5.3163,1.2253,5.1276,1.328,5.024,1.5l-4,6.6598C0.8013,8.5293,1.0679,8.9999,1.5,9h8c0.4321-0.0001,0.6987-0.4707,0.476-0.8402&#xA;&#x9;l-4-6.6598C5.8787,1.3386,5.706,1.2375,5.5174,1.2315z"
      />
    </svg>
  );
}
