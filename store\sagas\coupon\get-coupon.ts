import { TGetCouponResponse, getCoupon } from '@/services/apis';
import { getCouponAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getCouponSaga(
  action: ActionType<typeof getCouponAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;

  try {
    const response = yield call(getCoupon, materials);
    const getCouponResponse = response as TGetCouponResponse;
    yield put(getCouponAction.success(getCouponResponse));
    successCallback?.(getCouponResponse);
  } catch (err) {
    yield put(getCouponAction.failure(err));
    failedCallback?.(err);
  }
}
