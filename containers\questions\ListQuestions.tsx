import {
  EIsActive,
  EPlacement,
  EQuestionForm,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { TMockTest, TQuestion } from '@/commons/types';
import {
  Button,
  ColumnType,
  ConfirmModal,
  EButtonStyleType,
  EditIcon,
  InputNumber,
  Pagination,
  PresentationIcon,
  PreviewChildIcon,
  PreviewIcon,
  Table,
} from '@/components';
import { RootState } from '@/store/configureStore';
import Link from 'next/link';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FilterQuestions from './FilterQuestions';
import { TQuestionsFilter } from './questions.types';
import { useRouter } from 'next/router';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import {
  getPracticeQuestionsAction,
  postUpdatePracticeQuestionsAction,
} from '@/store/actions';
import clsx from 'clsx';
import { getExportPracticeQuestions } from '@/services/apis';
import moment from 'moment';
import {
  resetGetPracticeQuestionsResponse,
  resetPracticeQuestionsSlice,
} from '@/store/slices/practice-questions';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import PreviewQuestionModal from './PreviewQuestionModal';

const MAX_SHOW_ORDER = 9999999999;
const MIN_SHOW_ORDER = 1;

const ListQuestions = ({ query }: { query: TQuestionsFilter }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [filter, setFilter] = useState<TQuestionsFilter>();
  const [dataSource, setDataSource] = useState<Array<TQuestion>>([]);
  const [isShowModalSubmit, setIsShowModalSubmit] = useState<boolean>(false);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [questionPreivew, setQuestionPreivew] = useState<TQuestion>();

  const { getPracticeQuestionsResponse: practiceQuestions } = useSelector(
    (state: RootState) => state.practiceQuestion
  );
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  const { data, meta } = useMemo(
    () => practiceQuestions || { data: [], meta: undefined },
    [practiceQuestions]
  );

  useEffect(() => {
    return () => {
      dispatch(resetGetPracticeQuestionsResponse());
    };
  }, [dispatch]);

  useEffect(() => {
    setDataSource(data);
  }, [data]);

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
      types: [EQuestionForm.SINGLE, EQuestionForm.MULTIPLE],
    };
    setFilter(defaultFilter);
  };
  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, []);

  const handleUpdateQuestionNo = (mock: TQuestion) => {
    setDataSource((current) =>
      current.map((item) => {
        if (item.id === mock.id) {
          return mock;
        }
        return item;
      })
    );
  };

  const getActions = useCallback(
    (element: TQuestion) => {
      const handleRedirectViewQuestion = () => {
        dispatch(resetPracticeQuestionsSlice());
        dispatch(setCallBackUrl(router.asPath));
        setTimeout(() => {
          router.push(`/questions/${element.id}/view`);
        }, 0);
      };
      const handlePreviewQuestion = () => {
        setOpenModal(true);
        setQuestionPreivew(element);
      };

      return (
        <div className="flex items-center justify-center gap-5">
          <button onClick={handlePreviewQuestion}>
            <PreviewIcon />
          </button>
          <button onClick={handleRedirectViewQuestion}>
            <PreviewChildIcon />
          </button>
        </div>
      );
    },
    [dispatch, router]
  );

  const checkErrorQuestionNo = useCallback(
    (question: TQuestion) => {
      // if question is disabled, disabled input
      const originalQuestion = data?.filter(({ id }) => id === question.id);
      if (question.is_active === EIsActive.DISABLE) {
        return false;
      }
      if (question.show_order === 0) {
        return true;
      }
      if (
        question.show_order &&
        (Number(question.show_order) < MIN_SHOW_ORDER ||
          Number(question.show_order) > MAX_SHOW_ORDER)
      ) {
        return true;
      }
      // if original question not null, but remove it -> error required
      if (isNaN(question.show_order) && originalQuestion?.[0]?.show_order) {
        return true;
      }
      const duplicates = dataSource.filter(
        ({ id, show_order, category }) =>
          id !== question.id &&
          category.id === question.category.id &&
          question.show_order &&
          show_order === question.show_order
      );
      return !!duplicates.length;
    },
    [data, dataSource]
  );

  const isEditted = useCallback(
    (question: TQuestion) => {
      // list show_order editted
      const question_editted = dataSource.filter(
        (item, idx) =>
          item.show_order !== data?.[idx]?.show_order &&
          !(data?.[idx]?.show_order === null && isNaN(item.show_order))
      );

      const include = question_editted.filter(({ id }) => id === question.id);
      return !!include.length;
    },
    [data, dataSource]
  );

  const columns = useMemo(
    () =>
      [
        {
          title: '問題ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => element?.id,
        },
        {
          title: '出題No.',
          dataIndex: 'show_order',
          key: 'show_order',
          render: (element) => {
            const updateQuestionNo = (show_order: string) => {
              if (element) {
                handleUpdateQuestionNo({
                  ...element,
                  show_order: Number.parseFloat(show_order),
                });
              }
            };
            const checkColor = (element: TQuestion) => {
              if (checkErrorQuestionNo(element)) return '!border-danger';
              if (isEditted(element)) return '!border-green';
              return '';
            };
            return (
              <InputNumber
                // value={element?.show_order.toString()}
                key={element?.id}
                defaultValue={element?.show_order}
                onChange={updateQuestionNo}
                size="middle"
                hideControls
                // classNames="!w-16 !text-xs !rounded"
                classNames={clsx(
                  '!w-16 !text-xs !rounded',
                  element && checkColor(element)
                )}
                disabled={
                  me?.data.role_id !== ERoleId.ADMIN ||
                  element?.is_active !== EIsActive.ENABLE
                }
                stringMode
              />
            );
          },
        },
        {
          title: '問題番号',
          dataIndex: 'question_no',
          key: 'question_no',
          render: (element) => {
            return <div className="w-12 truncate">{element?.question_no}</div>;
          },
        },
        {
          title: '出題年度',
          dataIndex: 'question_year',
          key: 'question_year',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-lg truncate">
              {element?.question_year}
            </div>
          ),
        },
        {
          title: '大項目名',
          dataIndex: 'category.name',
          key: 'category.name',
          render: (element) => (
            <div className="max-w-25 2xl:max-w-lg break-all">
              {element?.category.name}
            </div>
          ),
        },
        {
          title: 'タイトル',
          dataIndex: 'title',
          key: 'title',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-xl break-all">
              {element?.title}
            </div>
          ),
        },
        {
          title: '出題形式',
          dataIndex: 'type',
          key: 'type',
          render: (element) => (
            <div className="w-12 truncate">
              {element?.type === EQuestionForm.SINGLE ? '単一' : '複数'}
            </div>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'is_active',
          key: 'is_active',
          render: (element) => (
            <div className="w-12 truncate">
              {element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}
            </div>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TQuestion>>,
    [checkErrorQuestionNo, getActions, me?.data.role_id, isEditted]
  );

  const fetchData = useCallback(
    (page: number, filter?: TQuestionsFilter) => {
      const {
        is_actives: filterIsActives,
        types: filterTypes,
        question_year: questionYear,
        title: titleFilter,
        content: contentFilter,
        ...restFilter
      } = filter || {};

      const is_actives = filterIsActives as Array<number>;
      const types = filterTypes as Array<number>;
      const question_year = questionYear ? questionYear : undefined;
      const title = titleFilter ? titleFilter : undefined;
      const content = contentFilter ? contentFilter : undefined;
      const params = {
        page,
        is_actives,
        types,
        content,
        title,
        question_year,
        ...restFilter,
      };

      // call api here
      dispatch(getPracticeQuestionsAction.request({ params }));
    },
    [dispatch]
  );

  const handleExportData = async () => {
    const category_id = filter?.category_id
      ? Number(filter?.category_id)
      : undefined;
    const show_order = filter?.show_order || undefined;
    const content = filter?.content?.trim() || undefined;
    const title = filter?.title?.trim() || undefined;
    const id = filter?.id || undefined;
    const question_year = filter?.question_year || undefined;
    const types = filter?.types as Array<number>;
    const is_actives = filter?.is_actives as Array<number>;
    const params = {
      category_id,
      show_order,
      content,
      title,
      id,
      question_year,
      types,
      is_actives,
    };

    try {
      const response = await getExportPracticeQuestions({ params });
      const fileName = `Kojiro_unkan_practice_questions_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  useEffect(() => {
    if (filter) {
      fetchData(1, filter);
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ query }, undefined, {
        shallow: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleRenewQuestion = () => {
    // get list changed show_order
    // show_order after edite and backspace, null become NaN
    // and if original question is null, and editted is NaN, dont send to server
    const question_show_order = dataSource
      .filter(
        (question, idx) =>
          question.show_order !== data?.[idx].show_order &&
          !(data?.[idx].show_order === null && isNaN(question.show_order))
      )
      .map(({ id = 0, show_order }) => ({ id, show_order }));
    const payload = { question_show_order };
    // call api renew questions here
    dispatch(
      postUpdatePracticeQuestionsAction.request({ payload }, () => {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        fetchData(meta?.current_page || 1, filter);
        onCloseModalSubmit();
      })
    );
  };

  const handleClickSubmit = () => {
    handleRenewQuestion();
  };
  const onCloseModalSubmit = () => {
    setIsShowModalSubmit(false);
  };
  const onShowModalSubmit = () => {
    let errorMinMax = false;
    const empty = dataSource
      .map(({ show_order }) => show_order)
      .filter((item) => item);

    const notEmpty = dataSource.filter((item: any) => {
      return item.show_order;
    });

    const duplicate: Array<TQuestion> = [];

    // check min max show order
    for (let idx = 0; idx < dataSource.length; idx++) {
      if (
        typeof dataSource[idx].show_order === 'number' &&
        (dataSource[idx].show_order < MIN_SHOW_ORDER ||
          dataSource[idx].show_order > MAX_SHOW_ORDER)
      ) {
        errorMinMax = true;
        break;
      }
    }

    if (errorMinMax) {
      showNotification(
        ETypeNotification.ERROR,
        validationMessage.betweenNumber(
          '出題No',
          MIN_SHOW_ORDER,
          MAX_SHOW_ORDER
        )
      );
      return;
    }

    // check null after edit
    const requiredShowOrder = dataSource.filter((item, index) => {
      if (data?.[index].show_order && isNaN(item.show_order)) return item;
    });

    if (requiredShowOrder.length > 0) {
      showNotification(
        ETypeNotification.ERROR,
        validationMessage.required('出題No')
      );
      return;
    }

    // check duplicate show order
    notEmpty.forEach((item, index) => {
      for (let childIndex = 0; childIndex < notEmpty.length; childIndex++) {
        if (
          childIndex !== index &&
          notEmpty[childIndex]?.category.id === item.category.id &&
          notEmpty[childIndex]?.show_order === item?.show_order
        ) {
          duplicate.push(notEmpty[childIndex]);
          break;
        }
      }
    });

    if (duplicate.length > 0) {
      showNotification(
        ETypeNotification.ERROR,
        validationMessage.unique('出題No')
      );
      return;
    }
    setIsShowModalSubmit(true);
  };

  return (
    <Fragment>
      <PreviewQuestionModal
        showModal={openModal}
        handleCloseModal={() => {
          setOpenModal(false);
          setQuestionPreivew(undefined);
        }}
        quesitonContent={questionPreivew}
      />
      <ConfirmModal
        visible={isShowModalSubmit}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
        onClose={onCloseModalSubmit}
        onConfirm={handleClickSubmit}
      />
      <div className="grid grid-cols-1 gap-2.5">
        <div className="flex flex-col gap-y-4">
          <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
            <FilterQuestions
              defaultFilter={filter}
              setFilter={setFilter}
              clearFilter={setDefaultFilter}
            />
          </div>
          {me?.data.role_id === ERoleId.ADMIN && (
            <div className="self-end flex items-center gap-2">
              <Button
                onClick={handleExportData}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="出力"
                className="!font-normal"
              />
              <Button
                onClick={onShowModalSubmit}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title="更新"
                className="!font-normal"
              />
            </div>
          )}
        </div>
        <div className="flex flex-col items-center gap-y-4 justify-between">
          <div className="w-full flex justify-between items-center">
            <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
              meta?.total || 0
            }件`}</h2>
            {meta?.total !== 0 && (
              <Pagination
                current={meta?.current_page || 0}
                total={meta?.last_page || 0}
                onChange={handleChangePage}
              />
            )}
          </div>
          <Table<TQuestion>
            columns={columns}
            dataSource={practiceQuestions ? dataSource : undefined}
          />
          {meta?.total !== 0 && (
            <Pagination
              current={meta?.current_page || 0}
              total={meta?.last_page || 0}
              onChange={handleChangePage}
            />
          )}
        </div>
      </div>
    </Fragment>
  );
};

export default ListQuestions;
