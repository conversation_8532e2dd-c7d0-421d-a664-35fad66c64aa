import {
  Button,
  ConfirmModal,
  EButtonStyleType,
  Input,
  UploadFile,
} from '@/components';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import { PreviewExamsQuestions } from '@/containers';
import {
  TUploadUsersResponse,
  helpers,
  postUploadQuestionsExplainationImages,
  postUploadQuestionsImages,
  uploadCreateQuestions,
  uploadUpdateQuestions,
} from '@/services/apis';
import { showNotification } from '@/utils/functions';
import {
  ENeedCheckWithSetting,
  EPlacement,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { GetServerSideProps } from 'next';
import { Radio, RadioChangeEvent, Space, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { useHistory } from '@/utils/contexts';

const antIcon = <LoadingOutlined style={{ fontSize: 64 }} spin />;

const PER_PAGE = 20;
const defaultPagination = {
  current_page: 1,
  last_page: 1,
  total: 0,
};

const RADIO_SELECT_VALUES = {
  EXAMS: 1,
  EXAMS_IMAGES: 2,
  EXAMS_EXPLAIN_IMAGES: 3,
};

const PREVIEW_TYPE = {
  CREATE: 1,
  UPDATE: 2,
};

const IS_PREVIEW = {
  PREVIEW: 1,
  NOT_PREVIEW: 0,
};

const IS_CREATING = {
  CREATE: 1,
  UPDATE: 0,
};

const ExersciesUploadPage = () => {
  const router = useRouter();
  const [fileExams, setFileExams] = useState<File>();
  const [fileExamsImages, setFileExamsImages] = useState<File>();
  const [fileExamsExplainationImages, setFileExamsExplainationImages] =
    useState<File>();
  const [fileExamsValidateMessages, setFileExamsValidateMessages] =
    useState<string>();
  const [fileExamsImagesValidateMessages, setFileExamsImagesValidateMessages] =
    useState<string>();
  const [
    fileExamsExplainationImagesValidateMessages,
    setFileExamsExplainationImagesValidateMessages,
  ] = useState<string>();
  const [radioValue, setRadioValue] = useState<number>(1);
  const [previewType, setPreviewType] = useState<number>();
  const [questionsUploadList, setQuestionsUploadList] = useState<
    Array<TUploadUsersResponse>
  >([]);
  const [usersListPagination, setUsersListPagination] = useState<
    Array<TUploadUsersResponse>
  >([]);
  const [errors, setErrors] = useState<Array<string>>([]);
  const [pagination, setPagination] = useState(defaultPagination);
  const [isShowModalEdit, setIsShowModalEdit] = useState<boolean>(false);
  const [isShowModalCreate, setIsShowModalCreate] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isShowModalSetting, setIsShowModalSetting] = useState<boolean>(false);
  const historyManager = useHistory();
  const canGoBack = historyManager.canGoBack();

  const onHandleResetValues = () => {
    setPreviewType(undefined);
    setFileExamsValidateMessages(undefined);
    setFileExamsImagesValidateMessages(undefined);
    setFileExamsExplainationImagesValidateMessages(undefined);
    setFileExams(undefined);
    setFileExamsImages(undefined);
    setFileExamsExplainationImages(undefined);
    setQuestionsUploadList([]);
    setErrors([]);
  };

  const onChangeSelectRadio = (e: RadioChangeEvent) => {
    onHandleResetValues();
    setRadioValue(e.target.value);
  };

  const onHandlePreviewExams = async (previewSelectType: number) => {
    if (!fileExams) {
      setFileExamsValidateMessages('ファイルは必須項目です。');
      return;
    }
    setQuestionsUploadList([]);
    setErrors([]);
    setPreviewType(undefined);
    setIsLoading(true);
    try {
      let response;
      if (previewSelectType === PREVIEW_TYPE.CREATE) {
        response = await uploadCreateQuestions({
          need_check_with_setting: 1,
          file: fileExams,
          is_preview: IS_PREVIEW.PREVIEW,
        });
      } else if (previewSelectType === PREVIEW_TYPE.UPDATE) {
        response = await uploadUpdateQuestions({
          need_check_with_setting: 1,
          file: fileExams,
          is_preview: IS_PREVIEW.PREVIEW,
        });
      }

      if (!response) return;
      setQuestionsUploadList(response.data);
      setPreviewType(previewSelectType);

      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      const lastPage = Math.ceil(response.data?.length / PER_PAGE);

      setPagination({
        ...defaultPagination,
        total: response.data?.length,
        last_page: lastPage,
      });
    } catch (error: any) {
      if (error) {
        const listError = error?.data?.errors?.flat(Infinity);
        setErrors(listError);
        showNotification(
          ETypeNotification.ERROR,
          EXCEPTION.ACTION_FAILURE,
          EPlacement.BOTTOM_RIGHT
        );
      } else {
        setFileExams(undefined);
        showNotification(
          ETypeNotification.ERROR,
          EXCEPTION.TRY_AGAIN,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
    setIsLoading(false);
  };

  const handleImportCreate = async () => {
    setIsLoading(true);
    try {
      let response;
      switch (radioValue) {
        case RADIO_SELECT_VALUES.EXAMS: {
          if (!fileExams) return;
          // call api here
          response = await uploadCreateQuestions({
            need_check_with_setting: ENeedCheckWithSetting.NEED,
            file: fileExams,
            is_preview: IS_PREVIEW.NOT_PREVIEW,
          });

          const { is_pass_setting } = response;
          if (is_pass_setting === false) {
            setIsLoading(false);
            setIsShowModalSetting(true);
            return;
          }
          break;
        }
        case RADIO_SELECT_VALUES.EXAMS_IMAGES: {
          if (!fileExamsImages) {
            setFileExamsImagesValidateMessages('ファイルは必須項目です。');
            setIsLoading(false);
            return;
          }

          const payload = {
            file_zip: fileExamsImages,
            is_creating: IS_CREATING.CREATE,
          };
          // call api here
          response = await postUploadQuestionsImages({
            payload,
          });
          break;
        }
        case RADIO_SELECT_VALUES.EXAMS_EXPLAIN_IMAGES: {
          if (!fileExamsExplainationImages) {
            setFileExamsExplainationImagesValidateMessages(
              'ファイルは必須項目です。'
            );
            setIsLoading(false);
            return;
          }

          const payload = {
            file_zip: fileExamsExplainationImages,
            is_creating: IS_CREATING.CREATE,
          };
          // call api here
          response = await postUploadQuestionsExplainationImages({
            payload,
          });
          break;
        }
        default:
          return;
      }
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      onHandleResetValues();
      setRadioValue(RADIO_SELECT_VALUES.EXAMS);
    } catch (error: any) {
      setErrors(error?.data?.data?.invalid_files);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
    setIsLoading(false);
  };
  const handleImportEdit = async () => {
    setIsLoading(true);
    let response;
    try {
      switch (radioValue) {
        case RADIO_SELECT_VALUES.EXAMS: {
          if (!fileExams) return;
          // call api here
          response = await uploadUpdateQuestions({
            need_check_with_setting: ENeedCheckWithSetting.NEED,
            file: fileExams,
            is_preview: IS_PREVIEW.NOT_PREVIEW,
          });

          const { is_pass_setting } = response;
          if (is_pass_setting === false) {
            setIsLoading(false);
            setIsShowModalSetting(true);
            return;
          }
          break;
        }
        case RADIO_SELECT_VALUES.EXAMS_IMAGES: {
          if (!fileExamsImages) {
            setFileExamsImagesValidateMessages('ファイルは必須項目です。');
            setIsLoading(false);
            return;
          }

          const payload = {
            file_zip: fileExamsImages,
            is_creating: IS_CREATING.UPDATE,
          };
          // call api here
          response = await postUploadQuestionsImages({
            payload,
          });
          break;
        }
        case RADIO_SELECT_VALUES.EXAMS_EXPLAIN_IMAGES: {
          if (!fileExamsExplainationImages) {
            setFileExamsExplainationImagesValidateMessages(
              'ファイルは必須項目です。'
            );
            setIsLoading(false);
            return;
          }

          const payload = {
            file_zip: fileExamsExplainationImages,
            is_creating: IS_CREATING.UPDATE,
          };
          // call api here
          response = await postUploadQuestionsExplainationImages({
            payload,
          });
          break;
        }
        default:
          setIsLoading(false);
          return;
      }
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      onHandleResetValues();
      setRadioValue(RADIO_SELECT_VALUES.EXAMS);
    } catch (error: any) {
      setErrors(error?.data?.data?.invalid_files);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
    setIsLoading(false);
  };

  const backToList = () => {
    if (canGoBack) {
      router.back();
      return;
    }
    router.push('/exams');
  };

  const handleSuccess = (file: File, radioType: number) => {
    setQuestionsUploadList([]);
    setErrors([]);
    setPreviewType(undefined);
    switch (radioType) {
      case RADIO_SELECT_VALUES.EXAMS:
        setFileExams(file);
        setFileExamsValidateMessages(undefined);
        break;
      case RADIO_SELECT_VALUES.EXAMS_IMAGES:
        setFileExamsImages(file);
        setFileExamsImagesValidateMessages(undefined);
        break;
      case RADIO_SELECT_VALUES.EXAMS_EXPLAIN_IMAGES:
        setFileExamsExplainationImages(file);
        setFileExamsExplainationImagesValidateMessages(undefined);
        break;
      default:
        break;
    }
    // reset message errors
    // setFileValidateMessages('');
  };

  const handleError = () => {
    console.log('Error!');
  };

  const onErrorSizeExams = () => {
    setFileExamsValidateMessages(
      'ファイルは、50Mb以下のファイルでなければいけません。'
    );
  };
  const onErrorTypeExams = () => {
    setFileExamsValidateMessages(
      'ファイルには、以下のファイルタイプを指定してください。xlsx'
    );
  };
  const onErrorSizeExamsImages = () => {
    setFileExamsImagesValidateMessages(
      'ファイルは、5Gb以下のファイルでなければいけません。'
    );
  };
  const onErrorTypeExamsImages = () => {
    setFileExamsImagesValidateMessages(
      'ファイルには、以下のファイルタイプを指定してください。zip, 7zip'
    );
  };
  const onErrorSizeExamsExplainImages = () => {
    setFileExamsExplainationImagesValidateMessages(
      'ファイルは、5Gb以下のファイルでなければいけません。'
    );
  };
  const onErrorTypeExamsExplainImages = () => {
    setFileExamsExplainationImagesValidateMessages(
      'ファイルには、以下のファイルタイプを指定してください。zip, 7zip'
    );
  };

  const handleChangePage = (page: number) => {
    setPagination({
      ...pagination,
      current_page: page,
    });
  };

  const handleSetUserList = (pagination: any) => {
    let userListPage;
    // make pagination data
    if (pagination.current_page === 1) {
      userListPage = questionsUploadList.slice(
        0,
        PER_PAGE * pagination.current_page
      );
    } else {
      userListPage = questionsUploadList.slice(
        PER_PAGE * (pagination.current_page - 1),
        PER_PAGE * pagination.current_page
      );
    }

    setUsersListPagination(userListPage);
  };

  const onHandleOpenModalEdit = () => {
    setIsShowModalEdit(true);
  };

  const onHandleCloseModalEdit = () => {
    setIsShowModalEdit(false);
  };

  const onHandleOpenModalCreate = () => {
    setIsShowModalCreate(true);
  };

  const onHandleCloseModalCreate = () => {
    setIsShowModalCreate(false);
  };

  const onCloseModalSetting = () => {
    setIsShowModalSetting(false);
  };

  const handleClickSubmitWithoutCheck = async () => {
    setIsLoading(true);
    try {
      let response;
      if (!fileExams) {
        setIsLoading(false);
        return;
      }
      if (previewType === PREVIEW_TYPE.CREATE) {
        response = await uploadCreateQuestions({
          need_check_with_setting: ENeedCheckWithSetting.DONT_NEED,
          file: fileExams,
          is_preview: IS_PREVIEW.NOT_PREVIEW,
        });
      } else if (previewType === PREVIEW_TYPE.UPDATE) {
        response = await uploadUpdateQuestions({
          need_check_with_setting: ENeedCheckWithSetting.DONT_NEED,
          file: fileExams,
          is_preview: IS_PREVIEW.NOT_PREVIEW,
        });
      }

      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      onHandleResetValues();
      setRadioValue(RADIO_SELECT_VALUES.EXAMS);
    } catch (error: any) {
      const listError = error?.data?.errors?.flat(Infinity);
      setErrors(listError);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
    setIsLoading(false);
  };

  useEffect(() => {
    handleSetUserList(pagination);
  }, [pagination, questionsUploadList]);

  return (
    <>
      {isLoading && (
        <div className="fixed w-full h-full top-0 left-0 bg-gray-400 z-20 bg-opacity-30">
          <div className="w-full h-full flex items-center justify-center">
            <Spin indicator={antIcon} />
          </div>
        </div>
      )}
      <div className="mx-12 mt-4">
        <div className="text-lg font-bold mb-3">
          模擬試験問題アップロード（登録・更新）
        </div>
        <div className="bg-alice-blue-light py-4 px-5">
          <Radio.Group onChange={onChangeSelectRadio} value={radioValue}>
            <Space direction="vertical">
              {/* Radio upload exercises */}
              <Radio className="font-bold" value={RADIO_SELECT_VALUES.EXAMS}>
                <div className="w-full">
                  <div className="flex items-center">
                    <div className="text-textGray text-sm font-bold min-w-25 mx-2">
                      問題
                    </div>
                    <div className="mr-8">
                      <Input
                        disabled
                        value={fileExams?.name}
                        classNames="!w-48 2xl:!w-96 !rounded !mr-8"
                        size="large"
                      />
                      <UploadFile
                        accept=".xlsx"
                        type={['xlsx']}
                        maxSize={52428800}
                        // 50Mb = 52428800 Bytes
                        setFile={(file) =>
                          handleSuccess(file, RADIO_SELECT_VALUES.EXAMS)
                        }
                        maxCount={1}
                        onError={handleError}
                        onErrorSize={onErrorSizeExams}
                        onErrorType={onErrorTypeExams}
                      >
                        <button
                          className={clsx(
                            'rounded bg-primary bg-opacity-10',
                            '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold'
                          )}
                          disabled={radioValue !== RADIO_SELECT_VALUES.EXAMS}
                        >
                          ファイル選択
                        </button>
                      </UploadFile>
                    </div>
                    {radioValue && radioValue === RADIO_SELECT_VALUES.EXAMS && (
                      <div className="flex gap-4">
                        <Button
                          onClick={() =>
                            onHandlePreviewExams(PREVIEW_TYPE.CREATE)
                          }
                          size={'small'}
                          styleType={EButtonStyleType.PRIMARY_VARIANT}
                          title={'登録プレビュー'}
                        />
                        <Button
                          onClick={() =>
                            onHandlePreviewExams(PREVIEW_TYPE.UPDATE)
                          }
                          size={'small'}
                          styleType={EButtonStyleType.OUTLINE_PRIMARY}
                          title={'更新プレビュー'}
                        />
                      </div>
                    )}
                  </div>
                  {fileExamsValidateMessages && (
                    <div style={{ color: '#ff4d4f' }} className="flex">
                      <div className="min-w-25 mx-2"></div>
                      {fileExamsValidateMessages}
                    </div>
                  )}
                </div>
              </Radio>
              {/* Radio upload exercises images */}
              <Radio
                className="font-bold"
                value={RADIO_SELECT_VALUES.EXAMS_IMAGES}
              >
                <div className="w-full">
                  <div className="flex items-center">
                    <div className="text-textGray text-sm font-bold min-w-25 mx-2">
                      問題画像
                    </div>
                    <div className="mr-8">
                      <Input
                        disabled
                        value={fileExamsImages?.name}
                        classNames="!w-48 2xl:!w-96 !rounded !mr-8"
                        size="large"
                      />
                      <UploadFile
                        accept=".zip,.7zip"
                        type={['zip', '7zip']}
                        maxSize={5368709120}
                        // 5Gb = 5368709120 Bytes
                        setFile={(file) =>
                          handleSuccess(file, RADIO_SELECT_VALUES.EXAMS_IMAGES)
                        }
                        maxCount={1}
                        onError={handleError}
                        onErrorSize={onErrorSizeExamsImages}
                        onErrorType={onErrorTypeExamsImages}
                      >
                        <button
                          className={clsx(
                            'rounded bg-primary bg-opacity-10',
                            '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold'
                          )}
                          disabled={
                            radioValue !== RADIO_SELECT_VALUES.EXAMS_IMAGES
                          }
                        >
                          ファイル選択
                        </button>
                      </UploadFile>
                    </div>
                  </div>
                  {fileExamsImagesValidateMessages && (
                    <div style={{ color: '#ff4d4f' }} className="flex">
                      <div className="min-w-25 mx-2"></div>
                      {fileExamsImagesValidateMessages}
                    </div>
                  )}
                </div>
              </Radio>
              {/* Radio upload exercises explain images */}
              <Radio
                className="font-bold"
                value={RADIO_SELECT_VALUES.EXAMS_EXPLAIN_IMAGES}
              >
                <div className="w-full">
                  <div className="flex items-center">
                    <div className="text-textGray text-sm font-bold min-w-25 mx-2">
                      解説画像
                    </div>
                    <div className="mr-8">
                      <Input
                        disabled
                        value={fileExamsExplainationImages?.name}
                        classNames="!w-48 2xl:!w-96 !rounded !mr-8"
                        size="large"
                      />
                      <UploadFile
                        accept=".zip,.7zip"
                        type={['zip', '7zip']}
                        maxSize={5368709120}
                        // 5Gb = 5368709120 Bytes
                        setFile={(file) =>
                          handleSuccess(
                            file,
                            RADIO_SELECT_VALUES.EXAMS_EXPLAIN_IMAGES
                          )
                        }
                        maxCount={1}
                        onError={handleError}
                        onErrorSize={onErrorSizeExamsExplainImages}
                        onErrorType={onErrorTypeExamsExplainImages}
                      >
                        <button
                          className={clsx(
                            'rounded bg-primary bg-opacity-10',
                            '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold'
                          )}
                          disabled={
                            radioValue !==
                            RADIO_SELECT_VALUES.EXAMS_EXPLAIN_IMAGES
                          }
                        >
                          ファイル選択
                        </button>
                      </UploadFile>
                    </div>
                  </div>
                  {fileExamsExplainationImagesValidateMessages && (
                    <div style={{ color: '#ff4d4f' }} className="flex">
                      <div className="min-w-25 mx-2"></div>
                      {fileExamsExplainationImagesValidateMessages}
                    </div>
                  )}
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </div>
        {questionsUploadList.length > 0 && (
          <PreviewExamsQuestions
            data={usersListPagination}
            pagination={pagination}
            handleChangePage={handleChangePage}
            previewType={previewType}
          />
        )}
        {/* Button handle create/update */}
        <div className="flex justify-between mt-8">
          <div>
            {errors &&
              errors.map((error, index) => (
                <div key={index} style={{ color: '#ff4d4f' }}>
                  {error}
                </div>
              ))}
          </div>
          <div className="flex gap-4">
            {previewType === PREVIEW_TYPE.CREATE && (
              <Button
                onClick={onHandleOpenModalCreate}
                size={'small'}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title={'登録'}
              />
            )}
            {previewType === PREVIEW_TYPE.UPDATE && (
              <Button
                onClick={onHandleOpenModalEdit}
                size={'small'}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title={'更新'}
              />
            )}
            {radioValue &&
              (radioValue === RADIO_SELECT_VALUES.EXAMS_IMAGES ||
                radioValue === RADIO_SELECT_VALUES.EXAMS_EXPLAIN_IMAGES) && (
              <>
                <Button
                  onClick={onHandleOpenModalCreate}
                  size={'small'}
                  styleType={EButtonStyleType.PRIMARY_VARIANT}
                  title={'登録'}
                />
                <Button
                  onClick={onHandleOpenModalEdit}
                  size={'small'}
                  styleType={EButtonStyleType.PRIMARY_VARIANT}
                  title={'更新'}
                />
              </>
            )}
            <Button
              onClick={backToList}
              size={'small'}
              styleType={EButtonStyleType.OUTLINE_PRIMARY}
              title={'戻る'}
            />
          </div>
        </div>
        <ConfirmModal
          visible={isShowModalEdit}
          onClose={onHandleCloseModalEdit}
          onConfirm={handleImportEdit}
          content={MODAL_MESSAGE.CONFIRM_EDIT}
        />
        <ConfirmModal
          visible={isShowModalCreate}
          onClose={onHandleCloseModalCreate}
          onConfirm={handleImportCreate}
          content={MODAL_MESSAGE.CONFIRM_CREATE}
        />
        <ConfirmModal
          visible={isShowModalSetting}
          onClose={onCloseModalSetting}
          onConfirm={handleClickSubmitWithoutCheck}
          content={
            <div className="max-w-lg">{MODAL_MESSAGE.CONFIRM_SETTING_EXAM}</div>
          }
        />
      </div>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default ExersciesUploadPage;
