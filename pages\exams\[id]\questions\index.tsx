import { EIsActive, ESetingStatus } from '@/commons/enums';
import { ListMockTests, TMockTestsFilter } from '@/containers';
import { getExamAction, getExamSettingAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const MockTestListPage = ({ filter }: { filter: TMockTestsFilter }) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useDispatch();
  const { getExamResponse: exam } = useSelector(
    (store: RootState) => store.mockTest
  );

  const { getExamSettingResponse: examSetting } = useSelector(
    (state: RootState) => state.setting
  );

  useEffect(() => {
    if (id && typeof id === 'string') {
      dispatch(getExamAction.request({ paths: { id } }));
    }
  }, [id, dispatch]);

  useEffect(() => {
    dispatch(getExamSettingAction.request({}));
  }, [dispatch]);

  const totalQuestions = useMemo(
    () =>
      examSetting?.category_settings?.reduce(
        (s, { question_amount = 0, category }) => {
          if (category?.is_active === EIsActive.ENABLE) {
            return s + question_amount;
          }
          return s;
        },
        0
      ),
    [examSetting]
  );
  return (
    <div className="mx-12 my-4">
      <div className="flex justify-between items-center gap-40">
        <h1 className="text-lg font-bold text-black-kj flex-none">
          模擬試験問題一覧
        </h1>
        <div className="flex items-start gap-2 flex-grow justify-end">
          <h2 className="text-sm font-bold flex">
            <div className=" break-all">{exam?.data.name}</div>
            <span className="mx-2">:</span>
            <span className="text-primary whitespace-nowrap">{`${
              exam?.data.active_questions_count || 0
            }問（全${totalQuestions}問中) 登録済`}</span>
          </h2>
          <h2 className="text-sm font-bold whitespace-nowrap">
            配点<span className="mx-2">:</span>
            <span className="text-primary">{`${
              examSetting?.total_point || 0
            }点中${
              exam?.data.active_questions_sum_allocation_point || 0
            }点`}</span>
          </h2>
          <h2 className="text-sm font-bold whitespace-nowrap">
            出題設定<span className="mx-2">:</span>
            <span className="text-primary">
              {exam?.data.setting_status === ESetingStatus.DONE ? '完了' : '未'}
            </span>
          </h2>
        </div>
      </div>
      <div className="mt-3 w-full">
        <ListMockTests exam_id={Number(id)} query={filter} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  try {
    const { id, ...restQuery } = query;
    const is_actives =
      restQuery?.is_actives
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const types =
      restQuery?.types
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const category_id = restQuery?.category_id
      ? Number(restQuery?.category_id)
      : null;

    const show_order = restQuery?.show_order
      ? Number(restQuery?.show_order)
      : null;
    const question_id = restQuery?.question_id
      ? Number(restQuery?.question_id)
      : null;
    const filter = removeEmpty({
      ...restQuery,
      is_actives,
      types,
      category_id,
      show_order,
      id: question_id,
    });
    return {
      props: { filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default MockTestListPage;
