import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { Select } from '@/components/Select';
import { Form } from 'antd';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { useRouter } from 'next/router';
import moment from 'moment-timezone';
import { Fragment, useCallback, useEffect, useState } from 'react';
import { showNotification } from '@/utils/functions';
import { EPlacement, ETypeNotification } from '@/commons/enums';
import { GetServerSideProps } from 'next';
import helpers from '@/services/helpers';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import NotificationErrors from '@/components/NotificationErrors';
import { CouponForm, CouponReferTable } from '@/containers';
import { getCouponAction } from '@/store/actions';
import { deleteCoupon, putCoupon } from '@/services/apis';
import { TCouponPlan } from '@/commons/types';

const EditVoucherPage = () => {
  moment.tz.setDefault('Asia/Tokyo');
  const [form] = Form.useForm();
  const router = useRouter();
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);
  const [isShowDeleteModal, setIsShowDeleteModal] = useState<boolean>(false);
  const [couponPlans, setCouponPlans] = useState<Array<TCouponPlan>>([]);

  const dispatch = useDispatch();

  const { id } = router.query;

  const getCouponDetail = useCallback(
    async (id: string) => {
      dispatch(
        getCouponAction.request({ paths: { id } }, (response) => {
          const { start_date, end_date, coupon_plans, ...restValue } = response;
          form.setFieldsValue({
            ...restValue,
            plan_start_date: moment(start_date),
            plan_end_date: moment(end_date),
          });
          setCouponPlans(coupon_plans);
        })
      );
    },
    [dispatch, form]
  );

  useEffect(() => {
    if (id) {
      getCouponDetail(id.toString());
      // getFetchCouponPlans(id.toString());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const onHandleOpenModal = async () => {
    try {
      await form.validateFields();
      setIsShowModal(true);
    } catch (error) {}
  };

  const onHandleCloseModal = () => {
    setIsShowModal(false);
  };

  const onHandleConfirmSubmit = async () => {
    const values = form.getFieldsValue();
    const { plan_end_date, plan_start_date, ...restValues } = values;
    const payload = {
      ...restValues,
      start_date: plan_start_date?.format('YYYY-MM-DD'),
      end_date: plan_end_date?.format('YYYY-MM-DD'),
    };
    try {
      if (id) {
        const idPath = id.toString();
        await putCoupon({ payload, paths: { id: idPath } });
        showNotification(
          ETypeNotification.SUCCESS,
          EXCEPTION.ACTION_SUCCESS,
          EPlacement.BOTTOM_RIGHT
        );
        backToList();
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  const onHandleConfirmDelete = async () => {
    try {
      if (id) {
        const idPath = id.toString();
        await deleteCoupon({ paths: { id: idPath } });
        showNotification(
          ETypeNotification.SUCCESS,
          EXCEPTION.ACTION_SUCCESS,
          EPlacement.BOTTOM_RIGHT
        );
        backToList();
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  const backToList = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/coupon');
  };

  const onHandleOpenDeleteModal = () => {
    setIsShowDeleteModal(true);
  };

  const onHandleCloseDeleteModal = () => {
    setIsShowDeleteModal(false);
  };

  return (
    <Fragment>
      <ConfirmModal
        visible={isShowModal}
        onClose={onHandleCloseModal}
        onConfirm={onHandleConfirmSubmit}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
      />
      <ConfirmModal
        visible={isShowDeleteModal}
        onClose={onHandleCloseDeleteModal}
        onConfirm={onHandleConfirmDelete}
        content={MODAL_MESSAGE.CONFIRM_DELETE}
      />
      <div className="mx-12 my-4 grid grid-cols-1 gap-3">
        <div className="text-lg font-bold">クーポン編集</div>
        <div className="bg-alice-blue-light py-4 px-5">
          <CouponForm
            isDisableDiscount
            formInstance={form}
            handleSubmit={onHandleConfirmSubmit}
          />
        </div>
        <div className="bg-alice-blue-light py-4 px-5 grid grid-cols-5">
          <div className="col-span-2">
            <CouponReferTable couponPlans={couponPlans} />
          </div>
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={backToList}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            onClick={onHandleOpenDeleteModal}
            size={'small'}
            styleType={EButtonStyleType.DANGER}
            title={'削除'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
            onClick={onHandleOpenModal}
          />
        </div>
      </div>
    </Fragment>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default EditVoucherPage;
