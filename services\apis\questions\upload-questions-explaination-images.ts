import { EIsCreating } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPostUploadQuestionsExplainationImagesPayload = {
  file_zip: File;
  is_creating: EIsCreating;
};

export type TPostUploadQuestionsExplainationImagesMaterials = {
  payload: TPostUploadQuestionsExplainationImagesPayload;
};

export type TPostUploadQuestionsExplainationImagesResponse = unknown;

export const postUploadQuestionsExplainationImages = async (
  materials: TPostUploadQuestionsExplainationImagesMaterials
): Promise<TPostUploadQuestionsExplainationImagesResponse> => {
  const { payload } = materials;
  const bodyFormData = new FormData();
  bodyFormData.append('file_zip', payload?.file_zip);
  bodyFormData.append('is_creating', `${payload?.is_creating}`);
  const response = await ApiService.post('/upload/zip-image/question-explanation-images', bodyFormData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
};
