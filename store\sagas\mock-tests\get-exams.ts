import { TGetExamsResponse, getExams } from '@/services/apis';
import { getExamsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getExamsSaga(
  action: ActionType<typeof getExamsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getExams, materials);
    const getExamsResponse = response as TGetExamsResponse;
    yield put(getExamsAction.success(getExamsResponse));
    successCallback?.(getExamsResponse);
  } catch (err) {
    yield put(getExamsAction.failure(err));
    failedCallback?.(err);
  }
}
