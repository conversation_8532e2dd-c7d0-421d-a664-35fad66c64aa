import { EFolderUpload } from '@/commons/enums';
import ApiService from '..';

export const createExcerciseQuestion = async (payload: any): Promise<any> => {
  const response = await ApiService.post('/exercise-questions', payload);
  return response.data;
};

export const updateExcerciseQuestion = async (
  id: any,
  payload: any
): Promise<any> => {
  const response = await ApiService.put(`/exercise-questions/${id}`, payload);
  return response.data;
};

export const deleteExcerciseQuestion = async (id: any): Promise<any> => {
  const response = await ApiService.delete(`/exercise-questions/${id}`);
  return response.data;
};

export const uploadImage = async (
  payload: File,
  folder: EFolderUpload
): Promise<any> => {
  const formData = new FormData();
  formData.append('file', payload);
  formData.append('folder', folder);
  const response = await ApiService.post('/upload/images', formData);
  return response.data;
};

export const uploadVideo = async (payload: File): Promise<any> => {
  const formData = new FormData();
  formData.append('file', payload);

  const response = await ApiService.post('/upload/videos', formData);
  return response.data;
};
