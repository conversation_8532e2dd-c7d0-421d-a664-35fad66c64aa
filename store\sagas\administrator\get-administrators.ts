import { TGetAdministratorsResponse, getAdministrators } from '@/services/apis';
import { getAdministratorsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getAdministratorsSaga(
  action: ActionType<typeof getAdministratorsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getAdministrators, materials);
    const getAdministratorsResponse = response as TGetAdministratorsResponse;
    yield put(getAdministratorsAction.success(getAdministratorsResponse));
    successCallback?.(getAdministratorsResponse);
  } catch (err) {
    yield put(getAdministratorsAction.failure(err));
    failedCallback?.(err);
  }
}
