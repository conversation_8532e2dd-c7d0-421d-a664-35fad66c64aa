import { ETimeoutDebounce } from '@/commons/enums';
import { useDebounce } from '@/utils/hooks';
import { Select as SelectAntd } from 'antd';
import clsx from 'clsx';
import { ReactElement, useEffect, useState } from 'react';
import { WrapperLazyLoad } from '../WrapperLazyLoad';
import { TSelectProps } from './Select.types';
import styles from './styles.module.less';

const { Option } = SelectAntd;
const Select = ({
  className,
  value,
  size,
  showSearch,
  onSearch,
  onChange,
  onLoadMore,
  children,
  placeholder,
  disabled,
}: TSelectProps) => {
  const [keyword, setKeyword] = useState<string>('');
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const searchValueDebounce = useDebounce(keyword, ETimeoutDebounce.SEARCH);

  const handleChange = (value: string) => {
    onChange?.(value);
  };

  const handleScrollEnd = (): void => {
    if (onSearch) {
      onLoadMore?.();
    }
  };

  const dropdownRender = (menu: ReactElement) => {
    return (
      <WrapperLazyLoad onEnd={handleScrollEnd} maxHeight={256}>
        {menu}
      </WrapperLazyLoad>
    );
  };

  const handleSearch = (keywordValue: string) => {
    setKeyword(keywordValue.substring(0, 255));
  };

  useEffect(() => {
    if (isMounted && onSearch) {
      onSearch?.(searchValueDebounce);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValueDebounce, isMounted]);

  return (
    <SelectAntd
      className={clsx('Select', styles.select, className)}
      size={size}
      disabled={disabled}
      onChange={handleChange}
      value={value}
      showSearch={showSearch}
      getPopupContainer={(trigger: HTMLElement): HTMLElement => trigger}
      onSearch={handleSearch}
      virtual={false}
      dropdownRender={dropdownRender}
      filterOption={!onSearch}
      searchValue={keyword}
      onFocus={() => setIsMounted(true)}
      onBlur={() => setIsMounted(false)}
      placeholder={placeholder}
      notFoundContent={
        <div className="h-12 text-sm font-medium flex items-center justify-center">
          データがありません。
        </div>
      }
    >
      {children}
    </SelectAntd>
  );
};

Select.Option = Option;
export default Select;
