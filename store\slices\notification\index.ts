import { TGetNotificationsResponse } from '@/services/apis';
import {
  EGetNotificationsAction,
  TGetNotificationsSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TnotificationState {
  getNotificationsResponse?: TGetNotificationsResponse;
}

const initialState: TnotificationState = {
  getNotificationsResponse: undefined,
};

const notificationSlice = createSlice({
  name: 'notification-slice',
  initialState,
  reducers: {
    resetNotificationSlice: () => initialState,
  },
  extraReducers: {
    [EGetNotificationsAction.GET_NOTIFICATIONS_SUCCESS]: (
      state: TnotificationState,
      action: TGetNotificationsSuccess
    ): TnotificationState => ({
      ...state,
      getNotificationsResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: notificationReducer,
  actions: { resetNotificationSlice },
} = notificationSlice;

export { notificationReducer, resetNotificationSlice };
