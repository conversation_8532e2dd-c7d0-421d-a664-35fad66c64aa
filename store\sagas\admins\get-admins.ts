import { TGetAdminsResponse, getAdmins } from '@/services/apis';
import { getAdminsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getAdminsSaga(
  action: ActionType<typeof getAdminsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;

  try {
    const response = yield call(getAdmins, materials);
    const getAdminsResponse = response as TGetAdminsResponse;
    yield put(getAdminsAction.success(getAdminsResponse));
    successCallback?.(getAdminsResponse);
  } catch (err) {
    yield put(getAdminsAction.failure(err));
    failedCallback?.(err);
  }
}
