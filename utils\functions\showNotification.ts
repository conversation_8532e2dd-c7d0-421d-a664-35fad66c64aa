import clsx from 'clsx';
import { notification } from 'antd';
import { ReactNode, createElement } from 'react';
import { EPlacement, ETypeNotification } from '@/commons/enums';
import NotificationErrors from '@/components/NotificationErrors';
import Errors from '@/components/NotificationErrors/Errors';

export const showNotification = (
  type: ETypeNotification,
  description: string | ReactNode,
  placement?: EPlacement,
  icon?: ReactNode
): void => {
  // let iconName;

  // switch (type) {
  //   case ETypeNotification.ERROR:
  //     iconName = createElement(CheckIconFalse, {
  //       fillcolor: 'transparent',
  //       fillcolor2: '#FFFFFF',
  //     });
  //     break;
  //   case ETypeNotification.SUCCESS:
  //     iconName = createElement(CheckIconTrue, {
  //       fillcolor: 'transparent',
  //     });
  //     break;
  //   default:
  //     break;
  // }

  const descriptionComponent =
    typeof description === 'string'
      ? createElement(Errors, { errors: description.split('. ') })
      : description;

  const options: any = {
    description: descriptionComponent,
    placement: placement || 'top-center',
    className: clsx('Notification', type),
    icon: null,
    duration: 7,
  };

  switch (type) {
    case ETypeNotification.SUCCESS:
      notification.success(options);
      break;
    case ETypeNotification.WARNING:
      notification.warning(options);
      break;
    case ETypeNotification.ERROR:
      notification.error(options);
      break;
    case ETypeNotification.INFO:
      notification.info(options);
      break;
    default:
      notification.open(options);
  }
};
