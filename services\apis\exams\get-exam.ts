import { TExam } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetExamPath = {
  id: string;
};

export type TGetExamMaterials = {
  paths?: TGetExamPath;
};

export type TGetExamResponse = { data: TExam };

export const getExam = async (
  materials: TGetExamMaterials
): Promise<TGetExamResponse> => {
  const { paths } = materials;
  const response = await ApiService.get(`/exams/${paths?.id}`);
  return response.data;
};
