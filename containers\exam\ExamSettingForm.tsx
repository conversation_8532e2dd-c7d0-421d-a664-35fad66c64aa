import { Button, EButtonStyleType, InputNumber } from '@/components';
import { Form } from 'antd';
import NumberQuestion from './NumberQuestion';
import PassPoint from './PassPoint';
import { TExamSettingFormProps } from './mock-tests.types';
import { useCallback, useMemo } from 'react';
import validate from '@/utils/validate';
import { validationMessage } from '@/commons/messages-enum';
import { Rule } from 'antd/lib/form';
import { TExamSetting } from '@/commons/types';

const {
  checkMaxLength,
  hasWhiteSpace,
  checkPassPoint,
  checkBetweenNumberValue,
} = validate;

const ExamSettingForm = ({
  formInstance,
  handleSubmit,
}: TExamSettingFormProps) => {
  const rulesTotal = useCallback((name: string) => {
    const checkSpave = () => hasWhiteSpace(name);
    const checkBetween = () => checkBetweenNumberValue(name, 1, 999);
    return [
      {
        required: true,
        message: validationMessage.required(name),
      },
      // checkLength,
      checkSpave,
      checkBetween,
    ] as Array<Rule>;
  }, []);

  const rulesTotalPoint = useMemo(() => rulesTotal('満点'), [rulesTotal]);
  const rulesPassPoint = useMemo(
    () => [...rulesTotal('合格点'), checkPassPoint] as Array<Rule>,
    [rulesTotal]
  );
  const rulesLimitTime = useMemo(() => rulesTotal('制限時間'), [rulesTotal]);
  const rulesCategorySettings = useMemo(() => {
    return [
      {
        required: true,
        message: validationMessage.required('出題数設定'),
      },
    ] as Array<any>;
  }, []);

  const onSubmit = (values: TExamSetting) => {
    handleSubmit?.(values);
  };
  const onValuesChange = async (changedValues: TExamSetting) => {
    const keys = Object.keys(changedValues);
    if (keys.includes('total_point')) {
      const { pass_point } = formInstance.getFieldsValue();
      if (pass_point) {
        formInstance.validateFields(['pass_point']);
      }
    }
    if (keys.includes('category_settings')) {
      const categoryChange =
        changedValues.category_settings?.find((i) => i) || {};
      const categoryIdx = changedValues.category_settings?.findIndex((i) => i);
      if (Object.keys(categoryChange).includes('category_id')) {
        const { category_settings } = formInstance.getFieldsValue();
        const countDuplicate = category_settings?.reduce(
          (s: Array<any>, setting: any, idx: number) => {
            if (setting?.category_id) {
              return [...s, ['category_settings', idx, 'category_id']];
            }
            return s;
          },
          []
        );
        formInstance.validateFields(countDuplicate);
      }
      if (
        Object.keys(categoryChange).includes('question_amount') &&
        typeof categoryIdx === 'number'
      ) {
        const { category_settings } = formInstance.getFieldsValue();
        const setting = category_settings?.[categoryIdx];
        if (setting && setting.require_correct_amount) {
          formInstance.validateFields([
            ['category_settings', categoryIdx, 'require_correct_amount'],
          ]);
        }
      }
    }
  };

  return (
    <Form
      form={formInstance}
      onFinish={onSubmit}
      className="grid grid-cols-1 gap-5"
      onValuesChange={onValuesChange}
    >
      <div className="px-5 py-4 bg-new-white rounded-md">
        <h3 className="!font-bold text-base !mb-3 text-dark-shade-of-gray">
          出題数設定
        </h3>
        <Form.List name="category_settings" rules={rulesCategorySettings}>
          {(fields, { add, remove }, { errors }) => {
            return (
              <div className="grid grid-cols-1 gap-5">
                <div className="grid grid-cols-1 gap-4">
                  {fields.map((field, idx) => (
                    <NumberQuestion key={idx} index={idx} remove={remove} />
                  ))}
                </div>
                <Form.ErrorList errors={errors} />
                <div className="w-full flex justify-end items-center">
                  <Button
                    styleType={EButtonStyleType.PRIMARY_VARIANT}
                    title="＋出題数設定追加"
                    onClick={() => add()}
                  />
                </div>
              </div>
            );
          }}
        </Form.List>
      </div>
      <div className="px-5 py-4 bg-new-white rounded-md">
        <h3 className="!font-bold text-base !mb-3 text-dark-shade-of-gray">
          合格点設定
        </h3>
        {/* <Form.List name="pass_point_setting">
          {(fields, { add, remove }) => {
            return (
              <div className="grid grid-cols-1 gap-5">
                <div className="grid grid-cols-1 gap-4">
                  {fields.map((field, idx) => (
                    <PassPoint key={field.key} index={idx} remove={remove} />
                  ))}
                </div>
                <div className="w-full flex justify-end items-center">
                  <Button
                    styleType={EButtonStyleType.PRIMARY_VARIANT}
                    title="＋必須設定追加"
                    onClick={() => add()}
                  />
                </div>
              </div>
            );
          }}
        </Form.List> */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-x-5 items-start">
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">{'満点（点)'}</p>
            <Form.Item name="total_point" rules={rulesTotalPoint}>
              <InputNumber
                classNames="!w-full !rounded !text-sm"
                size="large"
                maxLength={3}
                hideControls
              />
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">{'合格点（点)'}</p>
            <Form.Item name="pass_point" rules={rulesPassPoint}>
              <InputNumber
                classNames="!w-full !rounded !text-sm"
                size="large"
                maxLength={3}
                hideControls
              />
            </Form.Item>
          </div>
          <div className="col-span-1 grid-cols-1 grid gap-2">
            <p className="text-black-kj font-bold">{'制限時間（分)'}</p>
            <Form.Item name="limit_time" rules={rulesLimitTime}>
              <InputNumber
                classNames="!w-full !rounded !text-sm"
                size="large"
                maxLength={3}
                hideControls
              />
            </Form.Item>
          </div>
        </div>
      </div>
    </Form>
  );
};

export default ExamSettingForm;
