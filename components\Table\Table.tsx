import clsx from 'clsx';
import styles from './styles.module.less';
import { TTableProps } from './Table.types';
import { DoubleScrollbar } from '../DoubleScrollbar';

function Table<T>({ dataSource, columns, isPreview = false }: TTableProps<T>) {
  const tableClass = isPreview
    ? 'min-w-full text-left text-sm bg-white border'
    : 'min-w-full text-left text-sm bg-alice-blue rounded-md';
  return (
    <DoubleScrollbar className={clsx('Table w-full', styles.table)}>
      <table className={tableClass}>
        <thead className="border-b border-black border-opacity-10">
          <tr>
            {columns?.map(({ dataIndex, title, className }) => (
              <th
                key={dataIndex}
                className={clsx(
                  'pl-2 2xl:pl-5 py-2.5 whitespace-nowrap',
                  className
                )}
              >
                {title}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {dataSource?.map((source, idx) => (
            <tr key={idx} className="border-b border-black border-opacity-10">
              {columns?.map(({ dataIndex, render }) => (
                <td
                  key={dataIndex}
                  className="pl-2 2xl:pl-5 py-2.5 2xl::pr-5 text-xs text-black-kj last:pr-5"
                >
                  {render(source)}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
      {dataSource && dataSource.length === 0 && (
        <div className="py-2 text-center"> データがありません。</div>
      )}
    </DoubleScrollbar>
  );
}

export default Table;
