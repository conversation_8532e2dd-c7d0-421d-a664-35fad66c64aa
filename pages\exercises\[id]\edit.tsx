import { Input, InputNumber } from '../../../components/Input';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { Checkbox, Form, Radio } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import dynamic from 'next/dynamic';
import {
  EFolderUpload,
  EIsActive,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { Rule } from 'antd/lib/form';
import { useRouter } from 'next/router';
import { SelectBigCategory, SelectSubCategory } from '@/containers/select';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import validate from '@/utils/validate';
import FormUploadItem from '@/containers/exam/FormUploadItem';
import { showNotification } from '@/utils/functions';
import Tabs from '@/containers/tabs-excercise';
import { TAnswerParam } from '@/components/excercise/Pickup.types';
import {
  deleteExcerciseQuestion,
  helpers,
  updateExcerciseQuestion,
} from '@/services/apis';
import { TOptionSub } from '@/commons/types';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
import { getDetailExerciseAction } from '@/store/actions';
import LawItemForm from '../../../containers/exam/FormLawItem';
import FormDigitalItem from '../../../containers/exam/FormDigitalItem';
import { GetServerSideProps } from 'next';
import QuestionSingleChoice from '@/containers/exam/QuestionSingleChoice';
import { NamePath } from 'antd/lib/form/interface';
import NotificationErrors from '@/components/NotificationErrors';
import { resetExerciseDetailSlice } from '@/store/slices/detailExercise';

const EditorBoxForm = dynamic(
  () => import('@/components/EditorBox/EditorBoxInForm'),
  { ssr: false }
);

const EditExcercise = () => {
  const defaultAnswer = Array.from({ length: 10 }, (value, index: number) => {
    return {
      choice_no: index + 1,
      content: '',
      is_correct: false,
    };
  });
  const [form] = Form.useForm();
  const router = useRouter();
  const big_category_id = Form.useWatch('category_id', form);
  // const contentData = Form.useWatch('content', form);
  const { checkMaxLengthEditor, checkEmptyStringAndImage } = validate;
  const [isFisrt, setFisrt] = useState<boolean>(true);
  const [isVisibleDelete, setIsVisibleDelete] = useState<boolean>(false);
  const {
    hasWhiteSpace,
    checkMaxLength,
    checkImageValidate,
    checkEmptyTextEditorExam,
    extractContent,
    checkUniqueItemQuestion,
    checkBetweenNumberValue,
  } = validate;
  const [openTabs, setOpenTabs] = useState<number>(1);
  const [isVisible, setVisible] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(true);
  const { callBackUrl } = useSelector((state: RootState) => state.history);
  const upload = Form.useWatch('upload', form);
  const content = Form.useWatch('content', form);

  const dispatch = useDispatch();
  const { id } = router.query;
  const { getDetailExerciseResponse } = useSelector(
    (state: RootState) => state.exerciseDetail
  );

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const params = { id: id?.toString() };
      dispatch(getDetailExerciseAction.request({ params }));
    } catch (error: any) {
      setLoading(false);
      showNotification(ETypeNotification?.ERROR, error?.data?.message);
    }
  };

  useEffect(() => {
    if (!getDetailExerciseResponse) return;
    const paramData = getDetailExerciseResponse?.data;
    if (paramData) {
      const filterParamLaws = paramData?.laws?.map((item: any) => {
        return {
          id: item?.id,
          name: item?.name,
          category_id: item?.category?.id,
        };
      });

      const filterParamDigital = paramData?.digital_texts?.map((item: any) => {
        return {
          id: item?.id,
          name: item?.name,
          category_id: item?.category?.id,
        };
      });
      const law_ids = filterParamLaws.concat(
        Array(4 - paramData?.laws?.length).fill(undefined)
      );
      const digital_text_ids = filterParamDigital.concat(
        Array(4 - paramData?.digital_texts?.length).fill(undefined)
      );
      form.setFieldsValue({
        question_no: paramData?.question_no,
        is_active: paramData?.is_active,
        category_id: paramData?.category?.id,
        sub_category_id: paramData?.sub_category?.id,
        question_year: paramData?.question_year,
        title: paramData?.title,
        content: paramData?.content,
        show_order: paramData?.show_order,
        explanation: paramData?.explanation,
        upload: paramData?.image,
        upload1:
          paramData?.exercise_question_explanation_images?.[0]
            ?.explanation_image,
        upload2:
          paramData?.exercise_question_explanation_images?.[1]
            ?.explanation_image,
        law_ids,
        digital_text_ids,
      });

      if (paramData?.exercise_choices?.length > 0) {
        const newAnswerParam = [...defaultAnswer].map(
          (obj) =>
            paramData?.exercise_choices.find(
              (items: any) => items.choice_no === obj.choice_no
            ) || obj
        );
        const changeCorrect = [...newAnswerParam]?.map((item: any) => {
          return { ...item, is_correct: item?.is_correct === 1 };
        });
        form.setFieldsValue({
          sub_questions: changeCorrect,
        });
        // setAnswerParam(changeCorrect);
      }
      setLoading(false);
    }
  }, [getDetailExerciseResponse, form]);

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-2">
        <Form.Item name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onBack = () => {
    dispatch(resetExerciseDetailSlice());
    router.push(`/exercises/${id}/detail`);
  };

  const rulesLaw = useMemo(() => {
    const checkUnique = checkUniqueItemQuestion('法令連携', 'law_ids');
    return [checkUnique] as Array<Rule>;
  }, []);

  const rulesDigital = useMemo(() => {
    const checkUnique = checkUniqueItemQuestion(
      'デジタルテキスト連携',
      'digital_text_ids'
    );
    return [checkUnique] as Array<Rule>;
  }, []);

  const rulesContent = useMemo(() => {
    const checkLength = () => checkMaxLengthEditor('問題本文', 1, 65535);
    const checkEmpty = () => checkEmptyTextEditorExam('問題本文');
    if (upload) {
      return [checkLength];
    }
    return [checkEmpty, checkLength];
  }, [upload, checkMaxLengthEditor, checkEmptyTextEditorExam]) as Array<Rule>;

  const rulesImage = useMemo(() => {
    if (typeof window === 'undefined' || !window.document) {
      return [];
    }
    const extract = content && extractContent(content);
    return [
      {
        required: !extract,
        message: validationMessage.required_content_or_images(),
      },
    ];
  }, [content, extractContent]) as Array<Rule>;

  const tabsDigital = useCallback(() => {
    return (
      <div>
        <div className="flex flex-row gap-5 mt-6">
          <div className="h-[300px] flex flex-grow flex-col">
            <span className="text-textGray text-sm font-bold">解説本文</span>
            <Form.Item
              rules={
                [() => checkMaxLengthEditor('解説本文', 1, 65535)] as Rule[]
              }
              className={'!mb-0 !mt-2'}
              name={'explanation'}
            >
              <EditorBoxForm />
            </Form.Item>
          </div>
          <div>
            <span className="text-textGray text-sm font-bold">解説画像１</span>
            <Form.Item name={'upload1'} className={'!mt-2 !w-75'}>
              <FormUploadItem
                name="upload1"
                label="解説画像１"
                accept={'.png,.jpeg,.jpg,.PNG'}
                type={['png', 'jpeg', 'jpg', 'PNG']}
                folder={EFolderUpload.EXERCISE_QUESTIONS_EXPLANATIONS}
              />
            </Form.Item>
          </div>
          <div>
            <span className="text-textGray text-sm font-bold">解説画像2</span>
            <Form.Item name={'upload2'} className={'!mt-2 !w-75'}>
              <FormUploadItem
                name="upload2"
                label="解説画像2"
                accept={'.png,.jpeg,.jpg,.PNG'}
                type={['png', 'jpeg', 'jpg', 'PNG']}
                folder={EFolderUpload.EXERCISE_QUESTIONS_EXPLANATIONS}
              />
            </Form.Item>
          </div>
        </div>
        <div className="mt-10">
          <div className="grid grid-cols-2 gap-5">
            <Form.List name={'law_ids'} initialValue={Array(4).fill(undefined)}>
              {(fields) => {
                return fields.map((field, idx) => (
                  <div
                    key={field.key}
                    className="col-span-1 flex flex-col gap-2"
                  >
                    <p className="text-black-kj">{`法令 ${idx + 1}`}</p>
                    <Form.Item rules={rulesLaw} name={[idx]}>
                      <LawItemForm
                        bottomTitle={'タイトル'}
                        bottomPlace={'選択してください'}
                        className={'h-8 font-thin'}
                        disable={false}
                      />
                    </Form.Item>
                  </div>
                ));
              }}
            </Form.List>
          </div>
        </div>
        <div className="mt-7">
          <div className="grid grid-cols-2 gap-5">
            <Form.List
              name={'digital_text_ids'}
              initialValue={Array(4).fill(undefined)}
            >
              {(fields) => {
                return fields.map((field, idx) => (
                  <div
                    key={field.key}
                    className="col-span-1 flex flex-col gap-2"
                  >
                    <p className="text-black-kj">{`デジタルテキスト ${
                      idx + 1
                    }`}</p>
                    <Form.Item rules={rulesDigital} name={[idx]}>
                      <FormDigitalItem
                        bottomTitle={'タイトル'}
                        bottomPlace={'選択してください'}
                        className={'h-8 font-thin'}
                        disable={false}
                      />
                    </Form.Item>
                  </div>
                ));
              }}
            </Form.List>
          </div>
        </div>
      </div>
    );
  }, [checkMaxLengthEditor, rulesDigital, rulesLaw]);

  const onValuesChange = (changedValues: any) => {
    const keys = Object.keys(changedValues);
    if (keys.includes('upload') || keys.includes('content')) {
      setTimeout(() => {
        form.validateFields(['upload', 'content']);
      }, 0);
    }
    if (keys.includes('law_ids')) {
      const { law_ids } = form.getFieldsValue();
      const countDuplicate = law_ids?.reduce(
        (s: Array<number>, law: any, idx: number) => {
          if (!law?.id) {
            return s;
          }
          return [...s, ['law_ids', idx]];
        },
        []
      );
      form.validateFields(countDuplicate);
    }
    if (keys.includes('digital_text_ids')) {
      const { digital_text_ids } = form.getFieldsValue();
      const countDuplicate = digital_text_ids?.reduce(
        (s: Array<number>, law: any, idx: number) => {
          if (!law?.id) {
            return s;
          }
          return [...s, ['digital_text_ids', idx]];
        },
        []
      );
      form.validateFields(countDuplicate);
    }
    if (keys.includes('sub_questions')) {
      const { sub_questions } = form.getFieldsValue();
      const idxChange = changedValues?.sub_questions.findIndex(
        (item: any) => item
      );
      if (sub_questions?.[idxChange].is_correct) {
        sub_questions
          ?.reduce((s: Array<number>, question: any, idx: number) => {
            if (idx !== idxChange && question) {
              return [
                ...s,
                {
                  name: ['sub_questions', idx],
                  value: { ...question, is_correct: false },
                },
              ];
            }
            return s;
          }, [])
          .map((item: { name: NamePath; value: any }) => {
            form.setFieldValue(item.name, item.value);
            return item;
          });
      }
    }
  };

  const tabsAnswer = useCallback(() => {
    return <QuestionSingleChoice minQuestion={'2'} name={'sub_questions'} />;
  }, []);

  const checkDuplicate = (arr: any) => {
    const unique = arr.filter(
      (obj: any, index: number) =>
        arr.findIndex((item: any) => item === obj) !== index
    );
    return unique;
  };

  const submit = async () => {
    const data = form.getFieldsValue();
    const paramAnswer = data?.sub_questions?.map(
      (items: any, index: number) => {
        if (items !== undefined) {
          return {
            ...items,
            is_correct: items?.is_correct === true ? 1 : 2,
            choice_no: index + 1,
          };
        }
      }
    );
    const filterListAnswer = [...paramAnswer]
      ?.filter((items: any, index: number) => items !== undefined)
      .filter((items) => items?.content !== '');
    const law_ids = data?.law_ids
      ?.filter((law: TOptionSub) => law)
      .map(({ id }: TOptionSub) => id);
    const digital_text_ids = data?.digital_text_ids
      ?.filter((digital: TOptionSub) => digital)
      .map(({ id }: TOptionSub) => id);
    const checkArrayLaws = checkDuplicate(law_ids);
    const checkArrayDigital = checkDuplicate(digital_text_ids);
    if (checkArrayLaws?.length > 0) {
      showNotification(
        ETypeNotification?.ERROR,
        validationMessage.unique('法令関連')
      );
      return;
    }
    if (checkArrayDigital?.length > 0) {
      showNotification(
        ETypeNotification?.ERROR,
        validationMessage.unique('デジタルテキスト関連')
      );
      return;
    }
    try {
      const explanation_images = [];
      if (data?.upload1) {
        explanation_images.push({ explanation_image: data?.upload1 });
      }
      if (data?.upload2) {
        explanation_images.push({ explanation_image: data?.upload2 });
      }
      const payload = {
        question_no: data?.question_no,
        category_id: data?.category_id,
        sub_category_id: data?.sub_category_id,
        question_year: data?.question_year,
        title: data?.title,
        image: data?.upload || null,
        content: data?.content,
        is_active: data?.is_active,
        show_order: data?.show_order,
        explanation: data?.explanation,
        explanation_images,
        law_ids: [...law_ids],
        digital_text_ids: [...digital_text_ids],
        exercise_choices: [...filterListAnswer],
      };

      const responsion = await updateExcerciseQuestion(id, payload);
      if (responsion) {
        onBack();
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        <NotificationErrors error={error} />
      );
    }
  };

  const onCloseModal = () => {
    setVisible(false);
  };

  const onOpenModal = async () => {
    try {
      await form.validateFields();
      setVisible(true);
    } catch ({ values, errorFields }: any) {
      const firstErrorTab = (errorFields as any)?.find(({ name }: any) => {
        return name.indexOf('sub_questions') !== -1;
      });
      const lawsError = (errorFields as any)?.find(({ name }: any) => {
        return name.indexOf('law_ids') !== -1;
      });
      const digitalError = (errorFields as any)?.find(({ name }: any) => {
        return name.indexOf('digital_text_ids') !== -1;
      });
      if (firstErrorTab && !lawsError && !digitalError) {
        setOpenTabs(2);
      } else {
        setOpenTabs(1);
      }
    }
  };

  const onChangeBigCategory = () => {
    form.setFieldsValue({
      sub_category_id: null,
    });
  };

  const showModalDelete = async () => {
    setIsVisibleDelete(true);
  };

  const onCloseModalDelete = () => {
    setIsVisibleDelete(false);
  };

  const handleDelete = async () => {
    try {
      const data = await deleteExcerciseQuestion(id);
      showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      dispatch(resetExerciseDetailSlice());

      setTimeout(() => {
        if (callBackUrl) {
          router.push(callBackUrl);
          return;
        }
        router.push('/exercises');
      }, 0);
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        <NotificationErrors error={error} />
      );
    }
  };

  const rulesQuestionNo = useMemo(() => {
    const checkLength = () => checkMaxLength('問題番号', 10);
    const checkSpave = () => hasWhiteSpace('問題番号');
    return [
      {
        required: true,
        message: validationMessage.required('問題番号'),
      },
      checkLength,
      checkSpave,
    ];
  }, [checkMaxLength, hasWhiteSpace]) as Array<Rule>;

  const tabs = useMemo(() => {
    return (
      <Tabs
        open={openTabs}
        setOpen={setOpenTabs}
        tabsDigital={tabsDigital}
        tabsAnswer={tabsAnswer}
      />
    );
  }, [openTabs, tabsDigital, tabsAnswer]);

  if (isLoading) return;
  return (
    <div className="mx-12 mt-4">
      <span className="text-lg font-bold">練習問題編集</span>
      <Form onValuesChange={onValuesChange} form={form}>
        <div className="bg-alice-blue-light py-4 px-5 mt-3">
          <div className="flex flex-row">
            <span className="text-base font-bold">問題ID</span>
            <span className="ml-3 text-primary text-base font-bold">{id}</span>
          </div>
          <div className="grid grid-cols-2 gap-x-5 gap-y-2 w-full mt-3">
            <div className="col-span-2 grid grid-cols-1 xl:grid-cols-2 gap-x-5">
              <div className="col-span-1 grid-cols-1 grid gap-2">
                <p className="text-black-kj font-bold">問題番号</p>
                <Form.Item name={'question_no'} rules={rulesQuestionNo}>
                  <InputNumber
                    classNames="!w-full !rounded !text-sm"
                    hideControls
                  />
                </Form.Item>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
              <span className="text-textGray text-sm font-bold">大項目名</span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('大項目名'),
                  },
                ]}
                name={'category_id'}
              >
                <SelectBigCategory
                  onChange={onChangeBigCategory}
                  placeholder={'選択してください'}
                  size={'middle'}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
              <span className="text-textGray text-sm font-bold">小項目名</span>
              <Form.Item
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('小項目名'),
                  },
                ]}
                name={'sub_category_id'}
              >
                <SelectSubCategory
                  size={'middle'}
                  category_id={big_category_id}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
              <span className="text-textGray text-sm font-bold">出題No.</span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('出題No.'),
                    },
                    checkBetweenNumberValue('出題No', 1, 9999999999),
                  ] as Rule[]
                }
                name={'show_order'}
              >
                <InputNumber
                  hideControls
                  classNames={'!w-full'}
                  min={0}
                  stringMode
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
              <span className="text-textGray text-sm font-bold">出題年度</span>
              <Form.Item
                rules={[checkMaxLength('出題年度', 50)] as unknown as Rule[]}
                name={'question_year'}
              >
                <Input />
              </Form.Item>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-5 justify-between mt-3">
            <div>
              <span className="text-textGray text-sm font-bold">
                問題タイトル
              </span>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('問題タイトル'),
                    },
                    () => checkMaxLength('問題タイトル', 255),
                  ] as Rule[]
                }
                name={'title'}
              >
                <Input classNames={'!mt-2'} />
              </Form.Item>
            </div>
          </div>
          <div className="flex flex-row gap-5 mt-3">
            <div className="h-[300px] flex flex-grow flex-col">
              <span className="text-textGray text-sm font-bold">問題本文</span>
              <Form.Item
                rules={rulesContent}
                className={'!mb-0 !mt-2'}
                name={'content'}
              >
                <EditorBoxForm onChange={() => setFisrt(false)} />
              </Form.Item>
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">問題画像</span>
              <Form.Item
                rules={rulesImage}
                className={'!mt-2 !w-75'}
                name={'upload'}
              >
                <FormUploadItem
                  name="upload"
                  label="問題画像"
                  accept={'.png,.jpeg,.jpg,.PNG'}
                  type={['png', 'jpeg', 'jpg', 'PNG']}
                  folder={EFolderUpload.EXERCISE_QUESTIONS}
                />
              </Form.Item>
            </div>
          </div>
          <div className="grid grid-cols-2 justify-between mt-3 gap-5">
            <div className="mt-3">
              <div className="flex flex-row">
                <span className="text-textGray text-sm font-bold">
                  有効・無効
                </span>
              </div>
              {ratioRender()}
            </div>
          </div>
        </div>
        {tabs}
        <ConfirmModal
          visible={isVisible}
          onClose={onCloseModal}
          content={MODAL_MESSAGE?.CONFIRM_EDIT}
          onConfirm={submit}
        />
        <ConfirmModal
          visible={isVisibleDelete}
          onClose={onCloseModalDelete}
          content={MODAL_MESSAGE?.CONFIRM_DELETE}
          onConfirm={handleDelete}
        />
        <div className="flex flex-row justify-end mt-2.5 gap-4 mb-2.5">
          <Button
            onClick={onBack}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            styleType={EButtonStyleType.DANGER}
            title="削除"
            className="!font-normal"
            onClick={showModalDelete}
          />
          <Button
            onClick={onOpenModal}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
          />
        </div>
      </Form>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default EditExcercise;
