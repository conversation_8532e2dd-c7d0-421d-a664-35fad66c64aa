import {
  EInquiryIsPublic,
  EIsActive,
  EQuestionType,
  ERoleId,
} from '@/commons/enums';
import { MODAL_MESSAGE } from '@/commons/messages-enum';
import { TKnowledgeBoard } from '@/commons/types';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import {
  TPutEditCommentMaterials,
  TPutUpdateKnowledgeBoardMaterials,
} from '@/services/apis';
import { RootState } from '@/store/configureStore';
import { formatDateTime } from '@/utils/functions';
import clsx from 'clsx';
import Link from 'next/link';
import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import PreviewQuestionKnowledge from './PreviewQuestionKnowledge';

type TInquiryProps = {
  inquiry: TKnowledgeBoard;
  handleGetRepliedComment: (inquiry: TKnowledgeBoard) => void;
  handleChangeIsPublicInquiry: (
    materials: TPutUpdateKnowledgeBoardMaterials
  ) => void;
};

const Inquiry = ({
  inquiry,
  handleGetRepliedComment,
  handleChangeIsPublicInquiry,
}: TInquiryProps) => {
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  const onChangePublic = () => {
    const materials = {
      paths: {
        id: inquiry.id.toString(),
      },
      payload: {
        is_public:
          inquiry.is_public === EInquiryIsPublic.PUBLIC
            ? EInquiryIsPublic.PRIVATE
            : EInquiryIsPublic.PUBLIC,
      },
    };
    handleChangeIsPublicInquiry(materials);
  };

  const question = useMemo(() => {
    switch (inquiry.type) {
      case EQuestionType.EXERCISE:
        return inquiry.exercise_question;
      case EQuestionType.EXAM:
        return inquiry.question;
      case EQuestionType.PRACTICE:
        return inquiry.question;
      default:
        return null;
    }
  }, [inquiry]);

  const detailQuestionLink = useMemo(() => {
    switch (inquiry.type) {
      case EQuestionType.EXERCISE:
        return `/exercises/${inquiry.exercise_question?.id}/detail`;
      case EQuestionType.PRACTICE:
        return `/questions/${inquiry.question?.id}/view`;
      case EQuestionType.EXAM:
        return `/exams/${inquiry.question?.exam?.id}/questions/${inquiry.question?.id}/view`;
      default:
        return '/404';
    }
  }, [inquiry]);

  const onOpenModal = () => {
    setIsShowModal(true);
  };

  const onCloseModal = () => {
    setIsShowModal(false);
  };

  return (
    <div>
      <ConfirmModal
        visible={isShowModal}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
        onClose={onCloseModal}
        onConfirm={onChangePublic}
      />
      <div
        className={clsx(
          'grid gap-5 my-4',
          question && question.sub_category ? 'grid-cols-4' : 'grid-cols-3'
        )}
      >
        <div className="!font-bold">
          <span className="mr-4">大項目名</span>
          <span className="text-primary break-all">
            {question && question.category.name}
          </span>
        </div>
        {question && question.sub_category && (
          <div className="!font-bold">
            <span className="mr-4">小項目名</span>
            <span className="text-primary">{question.sub_category.name}</span>
          </div>
        )}
        <div className="!font-bold">
          <span className="mr-4">タイプ</span>
          <span className="text-primary">
            {inquiry.type === EQuestionType.EXERCISE && '練習問題'}
            {inquiry.type === EQuestionType.PRACTICE && '実践問題'}
            {inquiry.type === EQuestionType.EXAM && '模擬試験'}
          </span>
        </div>
        <div className="!font-bold">
          <span className="mr-4">問題ID</span>
          <PreviewQuestionKnowledge inquiry={inquiry} />
        </div>
      </div>
      <div
        className={clsx(
          'flex justify-between border rounded-md py-4 px-5',
          inquiry.is_public === EIsActive.ENABLE ? 'bg-white' : 'bg-gray-200'
        )}
      >
        <div className="grow py-2 px-4 mr-4 font-bold">
          <div className="flex items-center mb-1">
            <span className="mr-6">{formatDateTime(inquiry.created_at)}</span>
            <span className="border rounded-md px-[6px] py-[6px]">
              {`${inquiry.user.username} (ID: ${inquiry.user.id})`}
            </span>
          </div>
          <div className="mb-1">
            <span className="mr-2">宛先：</span>講師
          </div>
          {/* Inquiry content */}
          <div className="flex justify-start">
            <div className="mr-2">1.</div>
            <p className="whitespace-pre-line break-all">{inquiry.inquiry}</p>
          </div>
        </div>
        <div className="flex flex-col py-2 gap-2">
          {me?.data.role_id === ERoleId.ADMIN && (
            <Button
              onClick={onOpenModal}
              size={'small'}
              styleType={
                inquiry.is_public === EInquiryIsPublic.PUBLIC
                  ? EButtonStyleType.OUTLINE_PRIMARY
                  : EButtonStyleType.GREEN
              }
              title={
                inquiry.is_public === EInquiryIsPublic.PUBLIC
                  ? '非公開'
                  : '公開'
              }
            />
          )}
          <Button
            onClick={() => handleGetRepliedComment(inquiry)}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY}
            title={'返信'}
          />
        </div>
      </div>
    </div>
  );
};

export default Inquiry;
