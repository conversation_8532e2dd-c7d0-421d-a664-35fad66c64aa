import { validationMessage } from '@/commons/messages-enum';
import { FormInstance } from 'antd';
import { RangePickerProps } from 'antd/lib/date-picker';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import moment from 'moment-timezone';

dayjs.extend(customParseFormat);
moment.tz.setDefault('Asia/Tokyo');

export const extractContent = (content: string) => {
  const span = document.createElement('span');
  span.innerHTML = content;
  return span.textContent || span.innerText;
};

const changeDateToMiliSencondWithoutTime = (date: any) => {
  return moment(date).hours(0).minutes(0).seconds(0).milliseconds(0).valueOf();
};

const validateStartEndDate =
  (isFisrtEndDate: boolean = false) =>
    ({ getFieldsValue }: FormInstance) => ({
      validator(_: any, value: any) {
        if (isFisrtEndDate) return Promise.resolve();
        const values = getFieldsValue();
        if (!values.end_time) return Promise.resolve();
        const startDate = moment(values.start_time?.toDate())?.format(
          'YYYY MM DD HH:mm:00'
        );
        const endDate = moment(values.end_time?.toDate())?.format(
          'YYYY MM DD HH:mm:00'
        );
        const now = moment(dayjs()?.toDate())?.format('YYYY MM DD HH:mm:00');
        if (endDate <= now) {
          return Promise.reject(new Error());
        } else if (values.start_time && values.end_time && startDate >= endDate)
          return Promise.reject(new Error());

        return Promise.resolve();
      },
      message: () => {
        const values = getFieldsValue();
        const endDate = moment(values.end_time?.toDate())?.format(
          'YYYY MM DD HH:mm:00'
        );

        const now = moment(dayjs()?.toDate())?.format('YYYY MM DD HH:mm:00');
        return endDate <= now
          ? validationMessage.afterOrEqual('終了日', '現在日')
          : validationMessage.afterOrEqual('終了日', '開始日');
      },
    });

const validateStartDate =
  (isFisrtStartDate: boolean = false, canPast?: boolean) =>
    ({ getFieldsValue }: FormInstance) => ({
      validator(_: any, value: any) {
        if (isFisrtStartDate) return Promise.resolve();
        const values = getFieldsValue();
        if (!values.start_time) return Promise.resolve();
        const startDate = moment(values.start_time?.toDate())?.format(
          'YYYY MM DD HH:mm:00'
        );
        const endDate = moment(values.end_time?.toDate())?.format(
          'YYYY MM DD HH:mm:00'
        );
        const now = moment(dayjs()?.toDate())?.format('YYYY MM DD HH:mm:00');

        if (startDate <= now && !canPast) {
          return Promise.reject(new Error());
        } else if (values.start_time && values.end_time && startDate >= endDate)
          return Promise.reject(new Error());

        return Promise.resolve();
      },
      message: () => {
        const values = getFieldsValue();
        const startDate = moment(values.start_time?.toDate())?.format(
          'YYYY MM DD HH:mm:00'
        );
        const endDate = moment(values.end_time?.toDate())?.format(
          'YYYY MM DD HH:mm:00'
        );
        const now = moment(dayjs()?.toDate())?.format('YYYY MM DD HH:mm:00');
        if (startDate <= now && !canPast) {
          return validationMessage.afterOrEqual('開始日', '現在日');
        }
        return startDate >= endDate
          ? validationMessage.afterOrEqual('終了日', '開始日')
          : validationMessage.afterOrEqual('終了日', '現在日');
      },
    });

const validateStartDateByDay =
  (isFisrtStartDate: boolean = false) =>
    ({ getFieldsValue }: FormInstance) => ({
      validator(_: any, value: any) {
        if (isFisrtStartDate) return Promise.resolve();
        const values = getFieldsValue();
        if (!values.plan_start_date) return Promise.resolve();
        const startDate = changeDateToMiliSencondWithoutTime(
          values.plan_start_date
        );
        const endDate = changeDateToMiliSencondWithoutTime(values.plan_end_date);
        // const now = moment()
        //   .hours(0)
        //   .minutes(0)
        //   .seconds(0)
        //   .milliseconds(0)
        //   .valueOf();
        // if (startDate < now) {
        //   return Promise.reject(new Error());
        // } else
        if (values.plan_start_date && values.plan_end_date && startDate > endDate)
          return Promise.reject(new Error());

        return Promise.resolve();
      },
      message: () => {
        const values = getFieldsValue();
        // const startDate = changeDateToMiliSencondWithoutTime(
        //   values.plan_start_date
        // );
        // const endDate = changeDateToMiliSencondWithoutTime(values.plan_end_date);
        // return startDate > endDate
        //   ? validationMessage.afterOrEqual('終了日', '開始日')
        //   : validationMessage.afterOrEqual('開始日', '現在日');
        //   ? validationMessage.afterOrEqual('終了日', '開始日')
        return validationMessage.afterOrEqual('終了日', '開始日');
      },
    });

const disabledDateBeforNow: RangePickerProps['disabledDate'] = (current) => {
  return current && current < dayjs().startOf('day');
};

const disabledDateBeforeOrEqualNow: RangePickerProps['disabledDate'] = (
  current
) => {
  return current && current <= moment().subtract(1, 'days');
};

const validateStartEndDateByDay =
  (isFisrtEndDate: boolean = false, isFuture: boolean = true) =>
    ({ getFieldsValue }: FormInstance) => ({
      validator(_: any, value: any) {
        if (isFisrtEndDate) return Promise.resolve();
        const values = getFieldsValue();
        if (!values.plan_end_date) {
          return Promise.resolve();
        }

        const startDate = changeDateToMiliSencondWithoutTime(
          values.plan_start_date
        );
        const endDate = changeDateToMiliSencondWithoutTime(values.plan_end_date);
        const now = moment()
          .hours(0)
          .minutes(0)
          .seconds(0)
          .milliseconds(0)
          .valueOf();
        if (endDate < now && isFuture) {
          return Promise.reject(new Error());
        } else if (
          values.plan_start_date &&
          values.plan_end_date &&
          startDate > endDate
        ) {
          return Promise.reject(new Error());
        }

        return Promise.resolve();
      },
      message: () => {
        const values = getFieldsValue();
        const endDate = changeDateToMiliSencondWithoutTime(values.plan_end_date);
        const now = moment()
          .hours(0)
          .minutes(0)
          .seconds(0)
          .milliseconds(0)
          .valueOf();
        return endDate < now
          ? validationMessage.afterOrEqual('終了日', '現在日')
          : validationMessage.afterOrEqual('終了日', '開始日');
      },
    });

const checkEmptyString =
  (isFirst: boolean, title: string = '本文') =>
    ({ getFieldsValue }: FormInstance) => ({
      validator(_: any, value: any) {
        if (isFirst) return Promise.resolve();
        const values = getFieldsValue();
        const content = values?.content;
        if (extractContent(content).trim() === '') {
          return Promise.reject(new Error());
        }
        return Promise.resolve();
      },
      message: () => {
        return validationMessage.required(title);
      },
    });

const checkEmptyStringAndImage =
  (isFirst: boolean, title: string = '本文') =>
    ({ getFieldsValue }: FormInstance) => ({
      validator(_: any, value: any) {
        const values = getFieldsValue();
        if (values?.upload) return Promise.resolve();
        if (isFirst) return Promise.resolve();
        const content = values?.content;
        if (extractContent(content).trim() === '') {
          return Promise.reject(new Error());
        }
        return Promise.resolve();
      },
      message: () => {
        return validationMessage.required_content_or_images();
      },
    });

const checkEmptyTextEditor = (title: string) => ({
  validator(_: any, value: any) {
    const content = value;
    if (extractContent(content).trim() === '') {
      return Promise.reject(new Error());
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.required(title);
  },
});

const checkEmptyTextEditorExam = (title: string) => ({
  validator(_: any, value: any) {
    const content = value;
    if (!content || extractContent(content).trim() === '') {
      return Promise.reject(new Error());
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.required_content_or_images();
  },
});

const checkMaxString = (title: string, maxLength: number) => ({
  validator(_: any, value: any) {
    if (value && value.length > maxLength) {
      return Promise.reject(new Error());
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.maxString(title, maxLength);
  },
});

const checkMaxLength = (title: string, maxLength: number) => ({
  validator(_: any, value: any) {
    if (typeof value === 'string') {
      if (value.length > maxLength) {
        return Promise.reject(new Error());
      }
    } else if (typeof value === 'number') {
      if (value >= Math.pow(10, maxLength)) {
        return Promise.reject(new Error());
      }
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.maxString(title, maxLength);
  },
});

const checkMaxLengthAns = (title: string, maxLength: number) => ({
  validator(_: any, value: any) {
    console.log('type: ', typeof value?.content);
    if (typeof value?.content === 'string') {
      if (value?.content.length > maxLength) {
        return Promise.reject(new Error());
      }
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.maxString(title, maxLength);
  },
});

const checkMinLength = (title: string, minLength: number) => ({
  validator(_: any, value: any) {
    if (typeof value === 'string') {
      if (value && value.length < minLength) {
        return Promise.reject(new Error());
      }
    } else if (typeof value === 'number') {
      if (value < Math.pow(10, minLength)) {
        return Promise.reject(new Error());
      }
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.minNumberic(title, minLength);
  },
});

const isNumeric = (value: any) => {
  return /^-?\d+$/.test(value);
};

const checkMaxLengthString = (title: string, min: number, max: number, type?: number) => ({
  validator(_: any, value: any) {
    if (extractContent(value).trim() === '') return Promise.resolve();
    if (
      extractContent(value).trim().length < min ||
      extractContent(value).trim().length > max
    ) {
      return Promise.reject(new Error());
    }
    return Promise.resolve();
  },
  message: () => {
    if (type) {
      return validationMessage.betweenString2(title, min, max);
    }
    return validationMessage.betweenString(title, min, max);
  },
});
const byteSize = (str: string) => new Blob([str]).size;

const checkMaxLengthEditor = (title: string, min: number, max: number) => ({
  validator(_: any, value: any) {
    // console.log(
    //   value.trim().length,
    //   byteSize(value),
    //   extractContent(value).trim().length
    // );
    if (!value || extractContent(value).trim() === '') return Promise.resolve();
    if (
      value.trim().length < min ||
      value.trim().length > max ||
      byteSize(value.trim()) > max
    ) {
      return Promise.reject(new Error());
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.betweenString(title, min, max);
  },
});

const checkID =
  (title: string, maxLength: number) =>
    ({ getFieldsValue }: FormInstance) => ({
      validator(_: any, value: any) {
        if (value?.toString().length === 0) return Promise.resolve();
        if (value?.toString().length > maxLength) {
          return Promise.reject(new Error());
        } else if (isNumeric(value) === false) {
          return Promise.reject(new Error());
        }
        return Promise.resolve();
      },
      message: () => {
        const values = getFieldsValue();
        return values?.text_id?.toString().length > maxLength
          ? `${title}は${maxLength}文字以下にしてください`
          : 'number';
      },
    });

const hasWhiteSpace = (title: string) => ({
  validator(_: any, value: any) {
    if (!value) return Promise.resolve();
    const content = value;
    if (!Number.isInteger(content)) {
      return Promise.reject(new Error());
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.integer(title);
  },
});

const hasDot = (title: string, maxLength: number) => ({
  validator(_: any, value: any) {
    if (!value) return Promise.resolve();
    if (value?.length > maxLength) return Promise.resolve();
    const content = Number(value);
    if (!Number.isInteger(content)) {
      return Promise.reject(new Error());
    }

    if (content < 0) {
      return Promise.reject(new Error());
    }
    return Promise.resolve();
  },
  message: () => {
    return validationMessage.integer(title);
  },
});

const checkIsString = (title: string) => ({
  validator(_: any, value: any) {
    // regex only katakana, hiragana, kanji, romaji
    const regexIsString =
      /^[ぁ-んァ-ン一-龥ｧ-ﾝﾞﾟa-zA-Z_ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ\s]+$/;
    if (!value || regexIsString.test(value)) return Promise.resolve();
    return Promise.reject();
  },
  message: () => {
    return validationMessage.string(title);
  },
});

const checkIsKana = (title: string) => ({
  validator(_: any, value: any) {
    // regex only katakana fullwidth and half-width
    // const regexIsKanaExp = /^([ァ-ン]|ー|[ｧ-ﾝﾞﾟ])+$/;
    const regexIsKanaExp =
      /[゠ァアィイゥウェエォオカガキギクグケゲコゴサザシジスズセゼソゾタダチヂッツヅテデトドナニヌネノハバパヒビピフブプヘベペホボポマミムメモャヤュユョヨラリルレロヮワヰヱヲンヴヵヶヷヸヹヺ・ーヽヾヿ｟｠｡｢｣､･ｦｧｨｩｪｫｬｭｮｯｰｱｲｳｴｵｶｷｸｹｺｻｼｽｾｿﾀﾁﾂﾃﾄﾅﾆﾇﾈﾉﾊﾋﾌﾍﾎﾏﾐﾑﾒﾓﾔﾕﾖﾗﾘﾙﾚﾛﾜﾝﾞ]+$/;
    if (!value || regexIsKanaExp.test(value)) return Promise.resolve();
    return Promise.reject();
  },
  message: () => {
    return validationMessage.kana(title);
  },
});

const checkLetterAndNumber = (title: string, type?: number) => ({
  validator(_: any, value: any) {
    // regex string is character and number
    const regexIsCharacterAndLetterExp = /^[a-zA-Z0-9]+$/;

    if (!value || regexIsCharacterAndLetterExp.test(value))
      return Promise.resolve();
    return Promise.reject();
  },
  message: () => {
    if (type) {
      return validationMessage.alpha_num2(title);
    }
    return validationMessage.alpha_num(title);
  },
});

const checkCharacterPassword = (title: any) => ({
  validator(_: any, value: any) {
    // regex string is character and number
    const regexIsCharacterAndLetterExp = /^[a-zA-Z0-9!@#%^&*<>,.?]+$/;

    if (!value || regexIsCharacterAndLetterExp.test(value))
      return Promise.resolve();
    return Promise.reject();
  },
  message: () => {
    return 'Sai form password bạn ơi';
  },
});
const checkPassPoint = ({ getFieldsValue }: FormInstance) => ({
  validator: (_: any, value: any) => {
    const { total_point } = getFieldsValue();
    if (!total_point || !value) {
      return Promise.resolve();
    }
    if (value > total_point) {
      return Promise.reject('満点以下の数字でなければいけません');
    }
    return Promise.resolve();
  },
});

const checkNumberQuestions =
  () =>
    ({ getFieldsValue }: FormInstance) => ({
      validator: ({ fullField }: { fullField: string }, value: any) => {
        if (!value) {
          return Promise.resolve();
        }
        const { category_settings } = getFieldsValue();
        const countDuplicate = category_settings?.reduce(
          (s: Object, setting: any, idx: number) => {
            if (!setting || !fullField.includes(`${idx}`)) {
              return s;
            }
            const { question_amount, require_correct_amount } = setting;
            if (
              !question_amount ||
              !require_correct_amount ||
              require_correct_amount === !value ||
              question_amount >= require_correct_amount
            ) {
              return s;
            }
            return '出題数以下の数字でなければいけません';
          },
          null
        );
        if (countDuplicate) {
          return Promise.reject(countDuplicate);
        }
        return Promise.resolve();
      },
    });

const checkNumberAmountQuestion =
  () =>
    ({ getFieldsValue, validateFields }: FormInstance) => ({
      validator: ({ fullField }: { fullField: string }, value: any) => {
        if (!value) {
          return Promise.resolve();
        }
        const { category_settings } = getFieldsValue();
        const countDuplicate = category_settings?.reduce(
          (s: number, setting: any, idx: number) => {
            if (!setting || !fullField.includes(`${idx}`)) {
              return s;
            }
            const { question_amount, require_correct_amount } = setting;
            if (
              !question_amount ||
              !require_correct_amount ||
              require_correct_amount === !value
            ) {
              return s;
            }
            return idx;
          },
          null
        );
        if (typeof countDuplicate === 'number') {
          validateFields([
            ['category_settings', countDuplicate, 'require_correct_amount'],
          ]);
        }
        return Promise.resolve();
      },
    });
const checkUnique =
  (title: string) =>
    ({ getFieldsValue }: FormInstance) => ({
      validator: async (_: any, value: any) => {
        if (!value) {
          return Promise.resolve();
        }
        const { category_settings } = getFieldsValue();
        const countDuplicate = category_settings?.reduce(
          (s: Array<number>, { category_id }: any, idx: number) => {
            if (category_id === value) {
              return s;
            }
            return [...s, ['category_settings', idx, 'category_id']];
          },
          []
        );
        if (countDuplicate.length < category_settings.length - 1) {
          return Promise.reject();
        }
        return Promise.resolve();
      },
      message: () => {
        return validationMessage.unique(title);
      },
    });

const checkImageValidate =
  (title: string = '本文') =>
    ({ getFieldsValue }: FormInstance) => ({
      validator(_: any, value: any) {
        const values = getFieldsValue();
        if (extractContent(values?.content).trim() === '' && !values?.upload)
          return Promise.reject(new Error());
        return Promise.resolve();
      },
      message: () => {
        return validationMessage.required_content_or_images();
      },
    });

const checkfieldUsernameKana = (title: string, maxLength: number) => ({
  validator(_: any, value: any) {
    const regexIsKanaExp =
      /^[゠ァアィイゥウェエォオカガキギクグケゲコゴサザシジスズセゼソゾタダチヂッツヅテデトドナニヌネノハバパヒビピフブプヘベペホボポマミムメモャヤュユョヨラリルレロヮワヰヱヲンヴヵヶヷヸヹヺ・ーヽヾヿ｟｠｡｢｣､･ｦｧｨｩｪｫｬｭｮｯｰｱｲｳｴｵｶｷｸｹｺｻｼｽｾｿﾀﾁﾂﾃﾄﾅﾆﾇﾈﾉﾊﾋﾌﾍﾎﾏﾐﾑﾒﾓﾔﾕﾖﾗﾘﾙﾚﾛﾜﾝﾞ\s]+$/;

    if (value && !regexIsKanaExp.test(value))
      return Promise.reject(new Error(validationMessage.kana(title)));
    if (value && value.length > maxLength) {
      return Promise.reject(
        new Error(validationMessage.maxString(title, maxLength))
      );
    }
    return Promise.resolve();
  },
});

const checkFieldEmail = (title: string, maxLength: number) => ({
  validator(_: any, value: any) {
    const regexAlphaNumLowercaseDot = /^[a-zA-Z0-9\._\-@]+$/;
    const regexIsEmailExp =
      /^[a-zA-Z0-9\._-]{1,}@[a-zA-Z0-9-]{2,}(\.[a-zA-Z0-9-]{2,}){1,}$/;
    if (value && !regexAlphaNumLowercaseDot.test(value))
      return Promise.reject(new Error(validationMessage.email()));
    if (value && !regexIsEmailExp.test(value))
      return Promise.reject(new Error(validationMessage.email()));
    if (value && value.length > maxLength) {
      return Promise.reject(
        new Error(validationMessage.maxString(title, maxLength))
      );
    }
    return Promise.resolve();
  },
});

const checkFieldLoginId = (
  title: string,
  minString: number,
  maxString: number
) => ({
  validator(_: any, value: any) {
    if (extractContent(value).trim() === '') return Promise.resolve();
    if (
      value &&
      (value.trim().length < minString || value.trim().length > maxString)
    ) {
      return Promise.reject(
        new Error(validationMessage.betweenString(title, minString, maxString))
      );
    }
    const regexIsCharacterAndLetterExp = /^[a-z0-9]+$/;
    if (value && !regexIsCharacterAndLetterExp.test(value)) {
      return Promise.reject(
        new Error(validationMessage.alpha_num_lowercase(title))
      );
    }
    return Promise.resolve();
  },
});

const checkFieldPassword = (
  title: string,
  minString: number,
  maxString: number
) => ({
  validator(_: any, value: any) {
    if (extractContent(value).trim() === '') return Promise.resolve();
    // const regularExpression =
    //   /^(?=.*[0-9])(?=.*[!@#$%^&*])(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9!@#$%^&*]$/;
    // all character, symbol is accepted except space
    // const mixed = /^(?=.*[a-z])(?=.*[A-Z])[^\s]{10,}$/;
    // const atLeastNumber = /^(?=.*\d)[^\s]{10,}$/;
    // const atLeastSymbol =
    //   /^(?=.*[./,~^<>?;:"'`!@#$%^&*()\[\]{}_+=|\\-])[^\s]{10,}$/;
    const passwordRegex =
      // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[./,~^<>?;:"'`!@#$%^&*()\[\]{}_+=|\\-]).{10,}$/;
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^a-zA-Z0-9]).{10,}$/;
    // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[./,~^<>?;:"'`!@#$%^&*()\[\]{}_+=|\\-])[.]{10,}$/;
    // (?=.*[a-z])(?=.*[A-Z]) at least character
    // (?=.*\d) at least number
    // (?=.*[./,~^<>?;:"'`!@#$%^&*()\[\]{}_+=|\\-]) at least symbol
    // only character, number and symbol
    // const symbol = /^(?=.*[./,~^<>?;:"'`!@#$%^&*()\[\]{}_+=|\\-])[A-Za-z\d./,~^<>?;:"'`!@#$%^&*()\[\]{}_+=|\\-]{2,}$/;
    if (value && (value.length < minString || value.length > maxString)) {
      return Promise.reject(
        new Error(validationMessage.betweenString(title, minString, maxString))
      );
    }
    if (value && !passwordRegex.test(value)) {
      return Promise.reject(new Error(validationMessage.password(title)));
    }
    return Promise.resolve();
  },
});

const checkUniqueItemQuestion =
  (title: string, key: string) =>
    ({ getFieldsValue }: FormInstance) => ({
      validator: async (_: any, value: any) => {
        if (!value) {
          return Promise.resolve();
        }
        const values = getFieldsValue()[key];
        const countDuplicate = values?.reduce(
          (s: Array<number>, law: any, idx: number) => {
            if (law?.id === value?.id) {
              return s;
            }
            return [...s, [key, idx]];
          },
          []
        );
        if (countDuplicate.length < values.length - 1) {
          return Promise.reject();
        }
        return Promise.resolve();
      },
      message: () => {
        return validationMessage.unique(title);
      },
    });

const checkBetweenNumberValue = (title: string, min: number, max: number) => ({
  validator(_: any, value: number) {
    if (value === undefined || value == null) {
      return Promise.resolve();
    }
    if (value >= min && value <= max) {
      return Promise.resolve();
    }
    return Promise.reject();
  },
  message: () => {
    return validationMessage.betweenNumber(title, min, max);
  },
});

const checkLink = (title: string) => ({
  validator(_: any, value: any) {
    const httpRegex =
      /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/;
    if (!httpRegex.test(value)) {
      return Promise.reject('リンクを入力してください。');
    }
    return Promise.resolve();
  },
});

const checkRequiredContentComment = (title: string, maxString: number) => ({
  validator(_: any, value: any) {
    if (!value || value.trim() === '') {
      return Promise.reject(new Error(validationMessage.required(title)));
    }
    if (value && value.length > maxString) {
      return Promise.reject(
        new Error(validationMessage.maxString(title, maxString))
      );
    }
    return Promise.resolve();
  },
});

const validate = {
  validateStartEndDateByDay,
  validateStartEndDate,
  validateStartDateByDay,
  validateStartDate,
  disabledDateBeforeOrEqualNow,
  disabledDateBeforNow,
  checkEmptyString,
  checkEmptyTextEditor,
  checkMaxLength,
  checkID,
  hasWhiteSpace,
  checkMinLength,
  checkMaxLengthString,
  checkMaxLengthEditor,
  checkEmptyStringAndImage,
  checkIsString,
  checkIsKana,
  checkLetterAndNumber,
  checkCharacterPassword,
  checkImageValidate,
  extractContent,
  checkUnique,
  checkPassPoint,
  checkMaxString,
  checkfieldUsernameKana,
  checkFieldLoginId,
  checkFieldEmail,
  checkFieldPassword,
  checkNumberQuestions,
  checkNumberAmountQuestion,
  checkEmptyTextEditorExam,
  checkUniqueItemQuestion,
  hasDot,
  checkMaxLengthAns,
  checkBetweenNumberValue,
  checkLink,
  checkRequiredContentComment,
};

export default validate;
