import CustomerModal from '@/components/Modal/CustomerModal';
import { getErrorsAction } from '@/store/actions/error';
import { RootState } from '@/store/configureStore';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MaintainContent from './MaintainContent';

const Maintain = () => {
  const dispatch = useDispatch();
  const error = useSelector((state: RootState) => state.error);
  const isMainTain = useMemo(
    () => Object.keys(error).some((key) => error[key]?.code === 503),
    [error]
  );

  useEffect(() => {
    const handle = (e: any) => {
      const { response } = e?.detail;
      dispatch(getErrorsAction.failure(response));
    };
    window.addEventListener('maintaining', handle);
  }, [dispatch]);

  if (!isMainTain) {
    return null;
  }
  return (
    <CustomerModal visible={true} closable={false}>
      <MaintainContent />
    </CustomerModal>
  );
};

export default Maintain;
