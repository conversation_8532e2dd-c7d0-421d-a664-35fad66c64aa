import { Pagination as AntdPagination } from 'antd';
import clsx from 'clsx';
import { ReactNode } from 'react';
import { ArrowLeftIcon, ArrowRightIcon } from '../Icon';
import { TPaginationProps } from './Pagination.types';
import styles from './styles.module.less';

const Pagination = ({ current, total, onChange }: TPaginationProps) => {
  const itemRender = (
    page: number,
    type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next',
    originalElement: ReactNode
  ) => {
    if (['prev'].includes(type)) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <ArrowLeftIcon />
        </div>
      );
    }
    if (['next'].includes(type)) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <ArrowRightIcon />
        </div>
      );
    }
    return originalElement;
  };
  const handleChange = (page: number) => {
    onChange(page);
  };

  return (
    <div className={clsx('Pagination', styles.pagination)}>
      <AntdPagination
        current={current}
        total={total}
        itemRender={itemRender}
        onChange={handleChange}
        pageSize={1}
        showQuickJumper={false}
        showSizeChanger={false}
      />
    </div>
  );
};

export default Pagination;
