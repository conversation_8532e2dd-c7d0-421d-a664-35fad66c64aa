import { Button, CloseIcon, DeleteIcon, EButtonStyleType } from '@/components';
import clsx from 'clsx';
import { Fragment, MouseEvent, useState } from 'react';
import { TSelectItemFormProps } from './mock-tests.types';
import { TOptionSub } from '@/commons/types';
import CustomerModal from '@/components/Modal/CustomerModal';
import SearchDigital from './SearchDigital';

const FormDigitalItem = ({
  value,
  onChange,
  disable,
  bottomTitle,
  bottomPlace,
  className,
}: TSelectItemFormProps<TOptionSub>) => {
  const [isVisibleSearch, setIsVisibleSearch] = useState<boolean>(false);

  const showSearchModal = () => {
    setIsVisibleSearch(true);
  };

  const handleRemove = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    onChange?.(undefined);
  };

  const onChangeDigital = (value: TOptionSub) => {
    onChange?.(value);
    setIsVisibleSearch(false);
  };

  const closeModal = () => {
    setIsVisibleSearch(false);
  };

  const hideSearchModal = () => {
    setIsVisibleSearch(false);
  };

  return (
    <Fragment>
      {isVisibleSearch && (
        <CustomerModal visible={true} onClose={hideSearchModal}>
          <SearchDigital
            value={value}
            onChangeDigital={onChangeDigital}
            onClose={closeModal}
            bottomTitle={bottomTitle}
            bottomPlace={bottomPlace}
          />
        </CustomerModal>
      )}
      <div className="flex gap-2.5 items-center mr-5">
        <div
          className={clsx(
            'flex-grow border border-black border-opacity-10',
            'rounded h-10 flex items-center px-3 justify-between',
            className,
            'bg-white'
          )}
        >
          <div className="relative flex-grow h-full">
            <div className="w-full absolute h-full top-0 left-0 flex items-center">
              <p
                className={clsx(
                  'flex-grow truncate text-sm overflow-hidden input-disabled',
                  disable ? 'text-gray-400' : 'text-black'
                )}
              >
                {value?.name}
              </p>
            </div>
          </div>
          {value && !disable ? (
            <button onClick={handleRemove} disabled={disable}>
              <DeleteIcon />
            </button>
          ) : null}
        </div>
        <Button
          title={'検索'}
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          onClick={showSearchModal}
        />
      </div>
    </Fragment>
  );
};

export default FormDigitalItem;
