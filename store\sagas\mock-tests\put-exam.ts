import { TGetExamsResponse, putExam } from '@/services/apis';
import { putExamAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* putExamSaga(
  action: ActionType<typeof putExamAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(putExam, materials);
    const putExamResponse = response as TGetExamsResponse;
    yield put(putExamAction.success(putExamResponse));
    successCallback?.(putExamResponse);
  } catch (err) {
    yield put(putExamAction.failure(err));
    failedCallback?.(err);
  }
}
