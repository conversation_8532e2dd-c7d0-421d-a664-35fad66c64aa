import { TGetAccountResponse } from '@/services/apis';
import { EGetAccountAction, TGetAccountSuccess } from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TAccountState {
  getAccountResponse?: TGetAccountResponse;
}

const initialState: TAccountState = {
  getAccountResponse: undefined,
};

const AccountSlice = createSlice({
  name: 'account-slice',
  initialState,
  reducers: {},
  extraReducers: {
    [EGetAccountAction.GET_ACCOUNT_SUCCESS]: (
      state: TAccountState,
      action: TGetAccountSuccess
    ): TAccountState => ({
      ...state,
      getAccountResponse: action.payload?.response,
    }),
  },
});

const { reducer: accountReducer } = AccountSlice;

export { accountReducer };
