import { TLaw, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetLawsParams = {
  category_id?: number;
  page?: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetLawsMaterials = {
  params?: TGetLawsParams;
};

export type TGetLawsResponse = TListData<TLaw>;

export const getLaws = async (
  materials: TGetLawsMaterials
): Promise<TGetLawsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/laws', { params });
  return response.data;
};
