import { <PERSON><PERSON>oupon, <PERSON><PERSON>ouponPlan, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetCouponsParams = {
  page: number
  name?: string
  code?: string
  status?: string
  training_school?: string
};

export type TGetCouponsMaterials = {
  params?: TGetCouponsParams;
};

export type TGetCouponsResponse = TListData<ICoupon>;

export const getCoupons = async (
  materials: TGetCouponsMaterials
): Promise<TGetCouponsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/coupons', { params });
  return response.data;
};
