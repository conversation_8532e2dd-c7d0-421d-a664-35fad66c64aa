import {
  TGetSubCategoriesMaterials,
  TGetSubCategoriesResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetSubCategoriesAction {
  GET_SUB_CATEGORIES = 'GET_SUB_CATEGORIES',
  GET_SUB_CATEGORIES_REQUEST = 'GET_SUB_CATEGORIES_REQUEST',
  GET_SUB_CATEGORIES_SUCCESS = 'GET_SUB_CATEGORIES_SUCCESS',
  GET_SUB_CATEGORIES_FAILED = 'GET_SUB_CATEGORIES_FAILED',
}

// TYPES

export type TGetSubCategoriesRequest = {
  type: EGetSubCategoriesAction.GET_SUB_CATEGORIES_REQUEST;
  payload: {
    materials: TGetSubCategoriesMaterials;
    successCallback?: (response: TGetSubCategoriesResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetSubCategoriesSuccess = {
  type: EGetSubCategoriesAction.GET_SUB_CATEGORIES_SUCCESS;
  payload: { response?: TGetSubCategoriesResponse };
};

export type TGetSubCategoriesFailed = {
  type: EGetSubCategoriesAction.GET_SUB_CATEGORIES_FAILED;
};

export const getSubCategoriesAction = {
  request: createActionCreator(
    EGetSubCategoriesAction.GET_SUB_CATEGORIES_REQUEST,
    (resolve) =>
      (
        materials: TGetSubCategoriesMaterials,
        successCallback?: (response: TGetSubCategoriesResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetSubCategoriesRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetSubCategoriesAction.GET_SUB_CATEGORIES_SUCCESS,
    (resolve) =>
      (response?: TGetSubCategoriesResponse): TGetSubCategoriesSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetSubCategoriesAction.GET_SUB_CATEGORIES_FAILED,
    (resolve) =>
      (error: unknown): TGetSubCategoriesFailed =>
        resolve({ error })
  ),
};
