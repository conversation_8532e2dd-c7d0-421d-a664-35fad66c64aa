import { TGetAccountResponse, getAccount } from '@/services/apis';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

import { getAccountAction } from 'store/actions/account';

export function* getAccountSaga(
  action: ActionType<typeof getAccountAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getAccount, materials);
    const getAccountResponse = response as TGetAccountResponse;
    yield put(getAccountAction.success(getAccountResponse));
    successCallback?.(getAccountResponse);
  } catch (err) {
    yield put(getAccountAction.failure(err));
    failedCallback?.(err);
  }
}
