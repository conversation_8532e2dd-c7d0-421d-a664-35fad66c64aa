export enum MODAL_MESSAGE {
  CONFIRM_CREATE = '登録します。よろしいでしょうか？',
  CONFIRM_EDIT = '編集します。よろしいでしょうか？',
  CONFIRM_EDIT_USER_FROM_EC_SITE = 'アカウント情報を変更してもECサイトへの再連携は行われませんが、更新しますか？',
  CONFIRM_DELETE = '削除すると元に戻せませんが、削除しますか？',
  CONFIRM_SETTING = '試験設定情報を変更すると、全試験の解答中のデータおよび途中保存データを破棄しますが、変更しますか？（履歴には影響ありません）',
  CONFIRM_SETTING_EXAM = '出題条件を満たしていないため、全試験の解答中のデータおよび途中保存データを破棄しますが、変更しますか？（履歴には影響ありません）',
  CONFIRM_SEEN_COMMENT = '回答完了としますか？',
}

export enum EXCEPTION {
  SERVER_ERROR = 'サーバーエラーが発生しました。',
  BAD_REQUEST = '要求の形式が正しくありません。',
  AUTHORIZED = 'このシステムにアクセスする許可がありません。',
  FORBIDDEN = '許可されていない操作のため実行できません。',
  NOT_FOUND = 'エラー404が発生しました。',
  ERROR_NETWORK = 'ネットワークが繋がりませんでした。ネットワークが接続された状態で再度操作してください。',
  ACTION_SUCCESS = '操作に成功しました。',
  ACTION_FAILURE = '操作に失敗しました。システム エラーが発生しました。',
  ONE_CORRECT = '正答が登録されていません。',
  TRY_AGAIN = 'エラーが発生しました。再度お試しください。',
}

const required = (title: string) => {
  return `${title}は必須項目です。`;
};

const minCorrectOption = (min: string) => {
  return `選択肢は${min}つ以上入力してください。`;
};

const minSubQuestion = (min: string) => {
  return `${min}つ以上の設問を入力してください。`;
};

const minNumberic = (title: string, min: number) => {
  return `${title}には、${min}以上の数字を指定してください。`;
};

const maxNumberic = (title: string, max: number) => {
  return `${title}は、${max}桁以下の数字でなければいけません。`;
};

const maxString = (title: string, max: number) => {
  return `${title}は${max}文字以内で入力してください。`;
};

const characterSize = (title: string) => {
  return `${title}は半角の数字を指定してください 。`;
};

const checkMaxLength = (title: string, maxLength: number | string) => {
  return `${title}は${maxLength}文字以下にしてください`;
};

const distinctInput = (title: string) => {
  return `${title}の値が重複しています。`;
};

const loginFail = () => {
  return 'ログインできません。ログインIDまたはパスワードを再確認してください。';
};

const afterOrEqual = (title: string, date: string) => {
  return `${title}には、${date}以降の日付を指定してください。`;
};

const checkBeforNow = (title: string) => {
  return `${title}日には、nowより後の日付を指定してください。`;
};

const ip = (title: string) => {
  return `${title}には、有効なIPv4アドレスを指定してください。`;
};

const integer = (title: string) => {
  return `${title}には、整数を指定してください。`;
};

const string = (title: string) => {
  return `${title}には、文字列を指定してください。`;
};

const betweenString = (title: string, min: number, max: number) => {
  return `${title}は、${min}文字から${max}文字で入力してください。`;
};

const betweenString2 = (title: string, min: number, max: number) => {
  return `${title}は、${min}文字から${max}文字にしてください。`;
};

const betweenNumber = (title: string, min: number, max: number) => {
  return `${title}には、${min}から${max}までの数字を指定してください。`;
};

const unique = (title: string) => {
  return `指定の${title}は既に使用されています。`;
};

const kana = (title: string) => {
  return `${title}には、カタカナの文字列を指定してください。`;
};

const email = () => {
  return '正しい形式のメールアドレスを登録してください。メールアドレスには、半角英数字と一部の記号（. _ -）が使用できます。';
};

const alpha_num = (title: string) => {
  return `${title}にアルファベット (a-z)、数字 (0-9)が利用できます。`;
};

const alpha_num2 = (title: string) => {
  return `${title}は半角英数字で入力してください`;
};

const alpha_num_lowercase = (title: string) => {
  return `${title}には、小文字のアルファベットと数字が使用できます。`;
};

const alpha_num_lowercase_dot = (title: string) => {
  return '正しい形式のメールアドレスを登録してください。メールアドレスには、半角英数字と一部の記号（. _ -）が使用できます。';
};

const password_mixed = (title: string) => {
  return `${title}は大文字と小文字をそれぞれ1文字以上含める必要があります。`;
};

const password_numbers = (title: string) => {
  return `${title}は数字を1文字以上含める必要があります。`;
};

const password_symbols = (title: string) => {
  return `${title}は記号を1文字以上含める必要があります。`;
};

const password = (title: string) => {
  return `${title}には次の文字種すべてを含めてください。半角のアルファベット（A～Z,a～z）、数字、記号。`;
};

const new_password = () => {
  return '現状のパスワードを変更しない場合、この項目を空白のままにしてください。';
};

const required_content_or_images = () => {
  return '問題本文か問題画像のどちらかは登録してください';
};

export const validationMessage = {
  required,
  minNumberic,
  maxNumberic,
  characterSize,
  distinctInput,
  loginFail,
  afterOrEqual,
  ip,
  checkBeforNow,
  checkMaxLength,
  integer,
  string,
  betweenString,
  betweenNumber,
  unique,
  kana,
  email,
  alpha_num,
  alpha_num_lowercase,
  alpha_num_lowercase_dot,
  maxString,
  password_mixed,
  password_numbers,
  password_symbols,
  minCorrectOption,
  minSubQuestion,
  password,
  new_password,
  required_content_or_images,
  betweenString2,
  alpha_num2,
};
