import { EIsActive, EPlacement, ETypeNotification } from '@/commons/enums';
import { TDigital } from '@/commons/types/Digital.types';
import { Button, ColumnType, EditIcon, Pagination, PreviewIcon, Table } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { getExportDigitalTexts } from '@/services/apis';
import { getDigitalTextsAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TFilterDigital } from './digital.types';
import FilterDigitalTexts from './FilterDigitalTexts';
import { EXCEPTION } from '@/commons/messages-enum';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import { resetDigitalTextSlice } from '@/store/slices/digital-texts';
import { digitalTextBaseUrl } from '@/commons/constants';

const ListDigitalText = ({ query }: { query: TFilterDigital }) => {
  const router = useRouter();
  const [filter, setFilter] = useState<TFilterDigital>();

  const dispatch = useDispatch();
  const { getDigitalTextsResponse: digitalTexts } = useSelector(
    (state: RootState) => state.digitalTexts
  );
  const { data, meta } = digitalTexts || {};

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
    };
    setFilter(defaultFilter);
  };

  useEffect(() => {
    return () => {
      dispatch(resetDigitalTextSlice());
    };
  }, [dispatch]);

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(router.asPath));
    router.push('/digital-texts/create');
  };

  const getActions = useCallback(
    (element: TDigital) => {
      return (
        <div className="flex items-center gap-5">
          <a href={`${digitalTextBaseUrl}${element?.content}`} target="_blank">
            <PreviewIcon />
          </a>
          <Link
            href={`/digital-texts/${element.id}/edit`}
            onClick={() => dispatch(setCallBackUrl(router.asPath))}
          >
            <EditIcon />
          </Link>
        </div>
      );
    },
    [dispatch, router.asPath]
  );
  const columns = useMemo(
    () =>
      [
        {
          title: 'テキストID',
          dataIndex: 'majorId',
          key: 'majorId',
          render: (element) => element?.id,
        },
        {
          title: '大項目名',
          dataIndex: 'bigProjectName',
          key: 'bigProjectName',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-lg break-all">
              {element?.category.name}
            </div>
          ),
        },
        {
          title: '小項目名',
          dataIndex: 'smallProjectName',
          key: 'smallProjectName',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-lg break-all">
              {element?.sub_category.name}
            </div>
          ),
        },
        {
          title: 'テキスト名',
          dataIndex: 'textName',
          key: 'textName',
          render: (element) => (
            <div className="max-w-70 2xl:max-w-lg break-all">
              {element?.name}
            </div>
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'invalid',
          key: 'invalid',
          render: (element) => (
            <>{element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}</>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TDigital>>,
    [getActions]
  );

  const fetchData = useCallback(
    (page: number, filter?: TFilterDigital) => {
      const category_id = filter?.category_id
        ? Number(filter?.category_id)
        : undefined;
      const sub_category_id = filter?.sub_category_id
        ? Number(filter?.sub_category_id)
        : undefined;
      const name = filter?.name?.trim() || undefined;
      const is_actives = filter?.is_actives as Array<number>;
      const params = { category_id, sub_category_id, page, name, is_actives };
      dispatch(getDigitalTextsAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ query }, undefined, {
        shallow: true,
      });
      fetchData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleExport = async () => {
    const category_id = filter?.category_id
      ? Number(filter?.category_id)
      : undefined;
    const sub_category_id = filter?.sub_category_id
      ? Number(filter?.sub_category_id)
      : undefined;
    const name = filter?.name?.trim() || undefined;
    const is_actives = filter?.is_actives as Array<number>;
    const params = { category_id, sub_category_id, name, is_actives };
    try {
      const response = await getExportDigitalTexts({ params });
      const fileName = `Kojiro_unkan_digital_texts_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };
  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
          <FilterDigitalTexts
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        <div className="self-end flex items-center gap-2">
          <Button
            onClick={handleExport}
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="出力"
            className="!font-normal"
          />
          <Button
            onClick={handleRedirectToAdd}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="新規登録"
            className="!font-normal"
          />
        </div>
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            meta?.total || 0
          }件`}</h2>
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<TDigital> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default ListDigitalText;
