import { EInquiryIsPublic } from '@/commons/enums';
import ApiService from '@/services/apis';

type TPutUpdateKnowledgeBoardPaths = {
  id: string;
};
type TPutUpdateKnowledgeBoardPayload = {
  is_public?: EInquiryIsPublic;
};

export type TPutUpdateKnowledgeBoardMaterials = {
  paths: TPutUpdateKnowledgeBoardPaths;
  payload: TPutUpdateKnowledgeBoardPayload;
};

export type TPutUpdateKnowledgeBoardResponse = unknown;

export const putUpdateKnowledgeBoard = async (
  materials: TPutUpdateKnowledgeBoardMaterials
): Promise<TPutUpdateKnowledgeBoardResponse> => {
  const { paths, payload } = materials;
  const response = await ApiService.put(
    `/knowledge-boards/${paths.id}`,
    payload
  );
  return response.data;
};
