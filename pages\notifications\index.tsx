import { ERoleId } from '@/commons/enums';
import { ListNotification } from '@/containers/notification';
import { TFilterNotification } from '@/containers/notification/notification.types';
import helpers from '@/services/helpers';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';

const Notification = ({ query }: { query: TFilterNotification }) => {
  return (
    <div className="mx-12 my-4">
      <h1 className="text-lg font-bold text-black-kj">お知らせ一覧</h1>
      <div className="mt-3 w-full">
        <ListNotification query={query} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    const { id, ...restQuery } = query;
    const is_active = restQuery?.is_active
      ? Number(restQuery?.is_active)
      : null;
    const filter = removeEmpty({
      ...restQuery,
      is_active,
    });
    return {
      props: { query: filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default Notification;
