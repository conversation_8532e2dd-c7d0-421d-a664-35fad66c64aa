import { EIsActive } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPutCouponPath = {
  id: string;
};

export type TPutCouponPayload = {
  name: string,
  code: string,
  start_date: string,
  end_date: string,
  training_school: string,
  discount: number,
  note: string
};

export type TPutCouponMaterials = {
  paths: TPutCouponPath;
  payload: TPutCouponPayload;
};

export type TPutCouponResponse = unknown;

export const putCoupon = async (
  materials: TPutCouponMaterials
): Promise<TPutCouponResponse> => {
  const { payload, paths } = materials;
  const response = await ApiService.put(`/coupons/${paths.id}`, payload);
  return response.data;
};
