import ApiService from '@/services/apis';

export type TGetAccuracyRateExportParams = {
  start_date: string;
  end_date: string;
};

export type TGetAccuracyRateExportMaterials = {
  params: TGetAccuracyRateExportParams;
};

export type TGetAccuracyRateExportResponse = Blob;

export const getExportAccuracyExams = async (
  materials: TGetAccuracyRateExportMaterials
): Promise<TGetAccuracyRateExportResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/accurate-rate/exams', {
    params,
    responseType: 'blob',
  });
  return response.data;
};

export const getExportAccuracyPracticeQuestions = async (
  materials: TGetAccuracyRateExportMaterials
): Promise<TGetAccuracyRateExportResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/accurate-rate/practice-questions', {
    params,
    responseType: 'blob',
  });
  return response.data;
};

export const getExportAccuracyExerciseQuestions = async (
  materials: TGetAccuracyRateExportMaterials
): Promise<TGetAccuracyRateExportResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/accurate-rate/exercise-questions', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
