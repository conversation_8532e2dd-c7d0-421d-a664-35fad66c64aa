/** @type {import('next').NextConfig} */
const withPlugins = require('next-compose-plugins');
const withAntdLess = require('next-plugin-antd-less');
const nextTranslate = require('next-translate-plugin');

const pluginAntdLess = withAntdLess({
  lessVarsFilePath: './styles/utilities/variables.less',
  cssLoaderOptions: {},
  nextjs: {
    localIdentNameFollowDev: true,
  },
  webpack(config) {
    return config;
  },
});

const nextConfig = {
  serverRuntimeConfig: {},
  publicRuntimeConfig: {
    apiServiceBaseUrl: process.env.API_BASE_URL,
    digitalTextBaseUrl: process.env.DIGITAL_TEXT_BASE_URL,
  },
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
};

module.exports = withPlugins([[pluginAntdLess], nextTranslate], nextConfig);
