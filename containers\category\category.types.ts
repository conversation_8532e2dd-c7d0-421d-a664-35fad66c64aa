import { TCategory } from '@/commons/types';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

export type TCategoryProps = {
  name: string;
  show_order: string;
  enable_disable: number;
  id?: number;
};

export type TCategoryFormContainerProps = {
  intinitalValues?: TCategory;
  handleSubmit: (values: TCategory) => void;
  onHandleDelete?: (id: number) => void;
  isUpdate?: boolean;
};

export type TFilterCategory = {
  name?: string;
  is_actives?: Array<number>;
};

export type TFilterCategoryProps = {
  defaultFilter?: TFilterCategory;
  setFilter: (value?: TFilterCategory) => void;
  clearFilter: () => void;
};

export type TListSubCategoryProps = {
  category: TCategory;
  query: TFilterCategory;
};
