import { getCouponAction, getCouponPlansAction, getCouponsAction } from '@/store/actions';
import { all, takeLatest } from 'redux-saga/effects';
import { getCouponSaga } from './get-coupon';
import { getCouponPlansSaga } from './get-coupon-plans';
import { getCouponsSaga } from './get-coupons';

export default function* root(): Generator {
  yield all([takeLatest(getCouponAction.request.type, getCouponSaga)]);
  yield all([takeLatest(getCouponPlansAction.request.type, getCouponPlansSaga)]);
  yield all([takeLatest(getCouponsAction.request.type, getCouponsSaga)]);

}
