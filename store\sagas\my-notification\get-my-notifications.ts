import {
  TGetMyNotificationsResponse,
  getMyNotifications,
} from '@/services/apis';
import { getMyNotificationsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getMyNotificationsSaga(
  action: ActionType<typeof getMyNotificationsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getMyNotifications, materials);
    const getMyNotificationsResponse = response as TGetMyNotificationsResponse;
    yield put(getMyNotificationsAction.success(getMyNotificationsResponse));
    successCallback?.(getMyNotificationsResponse);
  } catch (err) {
    yield put(getMyNotificationsAction.failure(err));
    failedCallback?.(err);
  }
}
