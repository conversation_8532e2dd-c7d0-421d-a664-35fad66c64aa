import { TGetCategoriesResponse } from '@/services/apis';
import { EGetCategoriesAction, TGetCategoriesSuccess } from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TcategoryState {
  getCategoriesResponse?: TGetCategoriesResponse;
}

const initialState: TcategoryState = {
  getCategoriesResponse: undefined,
};

const categorySlice = createSlice({
  name: 'category-slice',
  initialState,
  reducers: {
    resetCategoryState: () => initialState,
  },
  extraReducers: {
    [EGetCategoriesAction.GET_CATEGORIES_SUCCESS]: (
      state: TcategoryState,
      action: TGetCategoriesSuccess
    ): TcategoryState => ({
      ...state,
      getCategoriesResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: categoryReducer,
  actions: { resetCategoryState },
} = categorySlice;

export { categoryReducer, resetCategoryState };
