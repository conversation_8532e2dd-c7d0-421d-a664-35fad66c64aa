import { Button, EButtonStyleType, Input } from '@/components';
import { Select } from '@/components/Select';
import { useEffect, useState } from 'react';
import { TFilterCoupon, TFilterCouponProps } from './coupon.types';

const { Option } = Select;

const FilterCoupon = ({
  defaultFilter,
  clearFilter,
  setFilter,
}: TFilterCouponProps) => {
  const [filterCoupon, setFilterCoupon] = useState<TFilterCoupon>();
  const handleUpdateFilter = () => {
    setFilter?.(filterCoupon);
  };

  useEffect(() => {
    setFilterCoupon(defaultFilter);
  }, [defaultFilter]);

  const handleChangeCouponName = (name: string) => {
    setFilterCoupon((filterCoupon) => ({ ...filterCoupon, name }));
  };

  const handleChangeStatus = (status: string) => {
    setFilterCoupon((current) => ({ ...current, status }));
  };

  const hanldeChangeCouponCode = (code: string) => {
    setFilterCoupon((current) => ({ ...current, code }));
  };

  const handleChangeTrainingSchool = (training_school: string) => {
    setFilterCoupon((filterCoupon) => ({ ...filterCoupon, training_school }));
  };

  return (
    <div className="grid grid-cols-1 gap-y-4 w-full">
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5 gap-y-4">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">クーポン名</p>
          <Input
            size="large"
            classNames="w-full !rounded"
            name="username"
            onChange={handleChangeCouponName}
            value={filterCoupon?.name}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">ステータス</p>
          <Select
            className="w-full rounded"
            size="large"
            onChange={handleChangeStatus}
            value={filterCoupon?.status}
            placeholder="選択してください"
          >
            <Option value={'1'}>利用期間内</Option>
            <Option value={'2'}>利用期間外</Option>
          </Select>
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">クーポンコード</p>
          <Input
            size="large"
            classNames="w-full !rounded"
            name="email"
            onChange={hanldeChangeCouponCode}
            value={filterCoupon?.code}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">教習所名</p>
          <Input
            size="large"
            classNames="w-full !rounded"
            name="username"
            onChange={handleChangeTrainingSchool}
            value={filterCoupon?.training_school}
          />
        </div>
      </div>
      <div className="flex justify-end gap-2.5">
        <Button
          styleType={EButtonStyleType.OUTLINE_PRIMARY}
          title="クリア"
          className="!font-medium"
          onClick={clearFilter}
        />
        <Button
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          title="検索"
          className="!font-medium"
          onClick={handleUpdateFilter}
        />
      </div>
    </div>
  );
};

export default FilterCoupon;
