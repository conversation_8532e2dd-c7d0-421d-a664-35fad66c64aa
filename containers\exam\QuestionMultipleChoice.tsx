import { Form } from 'antd';
import { Fragment, useMemo } from 'react';
import QuestionItem from './QuestionItem';
import { EXCEPTION, validationMessage } from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';
import { ENumberChoice } from '@/commons/enums';

const QuestionMultipleChoice = ({
  name,
}: {
  name: string | Array<string | number>;
}) => {
  const form = Form.useFormInstance();
  const items = Array(ENumberChoice.MULTIPLE).fill(undefined);
  const { checkMaxLengthAns } = validate;

  const questionsRules = useMemo(() => {
    return [
      {
        validator: async (a: any, values: Array<any>) => {
          const sub_questions = form.getFieldValue('sub_questions');
          const sub_questionsAvalible = sub_questions?.filter(
            (values: any) =>
              !values?.every((value: any) => !value || !value?.content)
          );
          if (sub_questionsAvalible.length < 2) {
            return Promise.reject(
              new Error(validationMessage.minSubQuestion('2'))
            );
          }

          if (!values || values?.every((value) => !value || !value?.content)) {
            return Promise.resolve();
          }
          const checkMin =
            values?.filter((value) => value?.content).length >= 2;
          const checkMinCorrent =
            values?.filter((value) => value?.is_correct).length >= 1;

          if (!checkMin) {
            return Promise.reject(
              new Error(validationMessage.minCorrectOption('2'))
            );
          }
          if (!checkMinCorrent) {
            return Promise.reject(new Error(EXCEPTION.ONE_CORRECT));
          }
          return Promise.resolve();
        },
      },
    ];
  }, [form]);

  return (
    <div className="py-4 pb-0.5  px-5 rounded-b-md bg-new-white">
      <Form.List name={name} initialValue={items} rules={questionsRules}>
        {(fields, _, { errors }) => {
          return (
            <Fragment>
              {fields.map((field) => (
                <Form.Item
                  key={field.key}
                  name={field.name}
                  rules={
                    [
                      checkMaxLengthAns(`選択肢 ${field.name + 1}`, 255),
                    ] as unknown as Rule[]
                  }
                >
                  <QuestionItem name={`選択肢 ${field.name + 1}`} />
                </Form.Item>
              ))}
              <Form.ErrorList className="mb-3.5" errors={errors} />
            </Fragment>
          );
        }}
      </Form.List>
    </div>
  );
};

export default QuestionMultipleChoice;
