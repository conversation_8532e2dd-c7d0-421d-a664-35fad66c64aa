import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function AddImageIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="1226 476.486 20 20"
      {...props}
    >
      <g data-name="Group 1399">
        <path
          d="M1226 486.487h20"
          strokeLinejoin="round"
          strokeLinecap="round"
          strokeWidth="2"
          stroke="#999"
          fill="transparent"
          data-name="Path 7679"
        />
        <path
          d="M1236 476.486v20"
          strokeLinejoin="round"
          strokeLinecap="round"
          strokeWidth="2"
          stroke="#999"
          fill="transparent"
          data-name="Path 7680"
        />
      </g>
    </svg>
  );
}
