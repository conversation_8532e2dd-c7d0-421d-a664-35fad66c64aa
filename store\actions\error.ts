import { createActionCreator } from 'deox';

// CONSTANTS

export enum ESetErrorAction {
  SET_ERRROR_FAILED = 'SET_ERRROR_FAILED',
}

// TYPES

export type TSetErrorFailed = {
  type: ESetErrorAction.SET_ERRROR_FAILED;
};

export const getErrorsAction = {
  failure: createActionCreator(
    ESetErrorAction.SET_ERRROR_FAILED,
    (resolve) =>
      (error: unknown): TSetErrorFailed =>
        resolve({ error })
  ),
};
