import { EIsActive } from '@/commons/enums';
import { Button, Input } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Checkbox } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { Fragment, useEffect, useState } from 'react';
import { TFilterCategory, TFilterCategoryProps } from './category.types';

const FilterCategory = ({
  defaultFilter,
  setFilter,
  clearFilter,
}: TFilterCategoryProps) => {
  const [filterCategory, setFilterCategory] = useState<TFilterCategory>();

  useEffect(() => {
    setFilterCategory(defaultFilter);
  }, [defaultFilter]);

  const handleChangeName = (name: string) => {
    setFilterCategory((current) => ({ ...current, name }));
  };

  const changeValid = (values: Array<CheckboxValueType>) => {
    const is_actives = values as Array<number>;
    setFilterCategory((current) => ({ ...current, is_actives }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterCategory);
  };

  return (
    <Fragment>
      <div className="flex items-end flex-grow gap-5">
        <div className="grid grid-cols-1 gap-2 !text-sm w-125">
          <p className="text-black-kj font-bold">大項目名</p>
          <Input
            classNames="w-full !rounded !text-sm !py-[9px]"
            size="large"
            onChange={handleChangeName}
            value={filterCategory?.name}
            maxLength={255}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm !font-bold">
          <p className="text-black-kj">有効・無効</p>
          <div className="flex flex-row items-center mt-3">
            <Checkbox.Group
              onChange={changeValid}
              value={filterCategory?.is_actives}
            >
              <Checkbox value={EIsActive.ENABLE}>有効</Checkbox>
              <Checkbox value={EIsActive.DISABLE}>無効</Checkbox>
            </Checkbox.Group>
          </div>
        </div>
      </div>
      <div className="flex items-end flex-none gap-2.5">
        <Button
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          title="クリア"
          className="!font-normal"
          onClick={clearFilter}
        />
        <Button
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          title="検索"
          className="!font-normal"
          onClick={handleUpdateFilter}
        />
      </div>
    </Fragment>
  );
};

export default FilterCategory;
