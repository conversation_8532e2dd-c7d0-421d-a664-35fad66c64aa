import { TListData, TOptionCategory } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetSelectCategoriesParams = {
  page: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetSelectCategoriesMaterials = {
  params?: TGetSelectCategoriesParams;
};

export type TGetSelectCategoriesResponse = TListData<TOptionCategory>;

export const getSelectCategories = async (
  materials: TGetSelectCategoriesMaterials
): Promise<TGetSelectCategoriesResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/select2/categories', { params });
  return response.data;
};
