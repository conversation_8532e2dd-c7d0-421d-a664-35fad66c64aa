import { TListData, TNotification } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetMyNotificationsMaterials = {};

export type TGetMyNotificationsResponse = TListData<TNotification>;

export const getMyNotifications = async (
  materials: TGetMyNotificationsMaterials
): Promise<TGetMyNotificationsResponse> => {
  const response = await ApiService.get('/my-notifications');
  return response.data;
};
