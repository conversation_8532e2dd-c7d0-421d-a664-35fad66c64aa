import ApiService from '@/services/apis';

export type TDeleteVideoPath = {
  id: string;
};

export type TDeleteVideoMaterials = {
  paths?: TDeleteVideoPath;
};

export type TDeleteVideoResponse = unknown;

export const deleteVideo = async (
  materials: TDeleteVideoMaterials
): Promise<TDeleteVideoResponse> => {
  const { paths } = materials;
  const response = await ApiService.delete(`/videos/${paths?.id}`);
  return response.data;
};
