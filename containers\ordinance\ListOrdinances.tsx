import { EIsActive, EPlacement, ETypeNotification } from '@/commons/enums';
import { TLaw } from '@/commons/types';
import {
  Button,
  ColumnType,
  EButtonStyleType,
  EditIcon,
  Pagination,
  PreviewIcon,
  Table,
} from '@/components';
import { getExportLaws } from '@/services/apis';
import { getLawsAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FilterOrdinance from './FilterOrdinance';
import { TFilterOrdinace } from './ordinance.types';
import {
  exportFile,
  isObjectEmpty,
  removeEmpty,
  showNotification,
} from '@/utils/functions';
import { EXCEPTION } from '@/commons/messages-enum';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import PreviewLawModal from './PreviewLawModal';
import { resetLawSlice } from '@/store/slices/laws';

const ListOrdinances = ({ query }: { query: TFilterOrdinace }) => {
  const router = useRouter();
  const [filter, setFilter] = useState<TFilterOrdinace>();
  const [showModalPreview, setShowModalPreview] = useState<boolean>(false);
  const [lawPreview, setLawPreview] = useState<TLaw>();

  const dispatch = useDispatch();
  const { getLawsResponse: laws } = useSelector(
    (state: RootState) => state.law
  );
  const { data, meta } = laws || {};

  const setDefaultFilter = () => {
    const defaultFilter = {
      is_actives: [EIsActive.ENABLE],
    };
    setFilter(defaultFilter);
  };

  useEffect(() => {
    return () => {
      dispatch(resetLawSlice());
    };
  }, [dispatch]);

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const getActions = useCallback(
    (element: TLaw) => {
      const handlePreviewLaw = () => {
        setShowModalPreview(true);
        setLawPreview(element);
      };

      return (
        <div className="flex items-center justify-center gap-5">
          <button onClick={handlePreviewLaw}>
            <PreviewIcon />
          </button>
          <Link
            href={`/laws/${element.id}/edit`}
            onClick={() => dispatch(setCallBackUrl(router.asPath))}
          >
            <EditIcon />
          </Link>
        </div>
      );
    },
    [dispatch, router.asPath]
  );
  const columns = useMemo(
    () =>
      [
        {
          title: ' 法令ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => <div className="w-12">{element?.id}</div>,
        },
        {
          title: '大項目名',
          dataIndex: 'bigProjectName',
          key: 'bigProjectName',
          render: (element) => (
            <div className="max-w-50 2xl:max-w-70  break-all">
              {element?.category.name || '...'}
            </div>
          ),
        },
        {
          title: '法令名',
          dataIndex: 'textName',
          key: 'textName',
          render: (element) => (
            <div className="max-w-50 2xl:max-w-70 break-all">
              {element?.name || '...'}
            </div>
          ),
        },
        {
          title: '本文',
          dataIndex: 'content',
          key: 'content',
          render: (element) => (
            <div
              className="max-w-50 2xl:max-w-lg line-clamp-3 text-lg !leading-normal html-render"
              dangerouslySetInnerHTML={{ __html: element?.content || '' }}
            />
          ),
        },
        {
          title: '有効・無効',
          dataIndex: 'invalid',
          key: 'invalid',
          render: (element) => (
            <div className="w-20">
              {element?.is_active === EIsActive.ENABLE ? '有効' : '無効'}
            </div>
          ),
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<TLaw>>,
    [getActions]
  );

  const fetchData = useCallback(
    (page: number, filter?: TFilterOrdinace) => {
      const category_id = filter?.category_id
        ? Number(filter?.category_id)
        : undefined;
      const name = filter?.name?.trim() || undefined;
      const is_actives = filter?.is_actives as Array<number>;
      const params = { category_id, page, name, is_actives };
      dispatch(getLawsAction.request({ params }));
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      fetchData(1, filter);
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ query }, undefined, {
        shallow: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, filter]);

  const handleChangePage = (page: number) => {
    fetchData(page, filter);
  };

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(router.asPath));
    router.push('/laws/create');
  };

  const handleExportData = async () => {
    const category_id = filter?.category_id
      ? Number(filter?.category_id)
      : undefined;
    const name = filter?.name?.trim() || undefined;
    const is_actives = filter?.is_actives as Array<number>;
    const params = { category_id, name, is_actives };
    try {
      const response = await getExportLaws({ params });
      const fileName = `Kojiro_unkan_laws_${moment().format(
        'YYYYMMDDHHmm'
      )}.xlsx`;
      exportFile(response, fileName);
    } catch (error) {
      console.log(error);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };
  return (
    <Fragment>
      <PreviewLawModal
        showModal={showModalPreview}
        handleCloseModal={() => {
          setShowModalPreview(false);
          setLawPreview(undefined);
        }}
        law={lawPreview}
      />
      <div className="grid grid-cols-1 gap-2.5">
        <div className="flex flex-col gap-y-4">
          <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
            <FilterOrdinance
              defaultFilter={filter}
              setFilter={setFilter}
              clearFilter={setDefaultFilter}
            />
          </div>
          <div className="self-end flex items-center gap-2">
            <Button
              styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
              title="出力"
              className="!font-normal"
              onClick={handleExportData}
            />
            <Button
              onClick={handleRedirectToAdd}
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="新規登録"
              className="!font-normal"
            />
          </div>
        </div>
        <div className="flex flex-col items-center gap-y-4 justify-between">
          <div className="w-full flex justify-between items-center">
            <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
              meta?.total || 0
            }件`}</h2>
            <Pagination
              current={meta?.current_page || 0}
              total={meta?.last_page || 0}
              onChange={handleChangePage}
            />
          </div>
          <Table<TLaw> columns={columns} dataSource={data} />
          {meta?.total !== 0 && (
            <Pagination
              current={meta?.current_page || 0}
              total={meta?.last_page || 0}
              onChange={handleChangePage}
            />
          )}
        </div>
      </div>
    </Fragment>
  );
};

export default ListOrdinances;
