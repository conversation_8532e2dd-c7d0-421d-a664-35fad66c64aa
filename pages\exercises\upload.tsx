import {
  Button,
  ConfirmModal,
  EButtonStyleType,
  Input,
  UploadFile,
} from '@/components';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import { PreviewExcercise } from '@/containers';
import {
  TUploadCreateExerciseQuestions,
  TUploadCreateExerciseQuestionsResponse,
  helpers,
  postUploadExerciseQuestionsExplainationImages,
  postUploadExerciseQuestionsImages,
  uploadCreateExerciseQuestions,
  uploadUpdateExerciseQuestions,
} from '@/services/apis';
import { showNotification } from '@/utils/functions';
import { EPlacement, ERoleId, ETypeNotification } from '@/commons/enums';
import { GetServerSideProps } from 'next';
import { Radio, RadioChangeEvent, Space, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { useHistory } from '@/utils/contexts';
import { RootState } from '@/store/configureStore';
import { useSelector } from 'react-redux';

const PER_PAGE = 20;
const defaultPagination = {
  current_page: 1,
  last_page: 1,
  total: 0,
};

const RADIO_SELECT_VALUES = {
  EXERCISES: 1,
  EXERCISES_IMAGES: 2,
  EXERCISES_EXPLAIN_IMAGES: 3,
};

const PREVIEW_TYPE = {
  CREATE: 1,
  UPDATE: 2,
};

const IS_PREVIEW = {
  PREVIEW: 1,
  NOT_PREVIEW: 0,
};

const antIcon = <LoadingOutlined style={{ fontSize: 64 }} spin />;

const ExersciesUploadPage = () => {
  const router = useRouter();
  const historyManager = useHistory();
  const canGoBack = historyManager.canGoBack();
  const [fileExercises, setFileExercises] = useState<File>();
  const [fileExercisesImages, setFileExercisesImages] = useState<File>();
  const [fileExercisesExplainationImages, setFileExercisesExplainationImages] =
    useState<File>();
  const [fileExercisesValidateMessages, setFileExercisesValidateMessages] =
    useState<string>();
  const [
    fileExercisesImagesValidateMessages,
    setFileExercisesImagesValidateMessages,
  ] = useState<string>();
  const [
    fileExercisesExplainationImagesValidateMessages,
    setFileExercisesExplainationImagesValidateMessages,
  ] = useState<string>();
  const [radioValue, setRadioValue] = useState<number>(1);
  const [previewType, setPreviewType] = useState<number>();
  const [exerciseQuestionsUploadList, setExerciseQuestionsUploadList] =
    useState<Array<TUploadCreateExerciseQuestionsResponse>>([]);
  const [exerciseQuestionsListPagination, setExerciseQuestionsListPagination] =
    useState<Array<TUploadCreateExerciseQuestionsResponse>>([]);
  const [errors, setErrors] = useState<Array<any>>([]);
  const [pagination, setPagination] = useState(defaultPagination);
  const [isShowModalEdit, setIsShowModalEdit] = useState<boolean>(false);
  const [isShowModalCreate, setIsShowModalCreate] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const onHandleResetValues = () => {
    setPreviewType(undefined);
    setFileExercisesValidateMessages(undefined);
    setFileExercisesImagesValidateMessages(undefined);
    setFileExercisesExplainationImagesValidateMessages(undefined);
    setFileExercises(undefined);
    setFileExercisesImages(undefined);
    setFileExercisesExplainationImages(undefined);
    setExerciseQuestionsUploadList([]);
    setErrors([]);
  };

  const onChangeSelectRadio = (e: RadioChangeEvent) => {
    onHandleResetValues();
    setRadioValue(e.target.value);
  };

  const onHandlePreviewExercises = async (previewSelectType: number) => {
    if (!fileExercises) {
      setFileExercisesValidateMessages('ファイルは必須項目です。');
      return;
    }
    setExerciseQuestionsUploadList([]);
    setErrors([]);
    setIsLoading(true);
    try {
      let response;
      if (previewSelectType === PREVIEW_TYPE.CREATE) {
        response = await uploadCreateExerciseQuestions({
          file: fileExercises,
          is_preview: IS_PREVIEW.PREVIEW,
        });
      } else if (previewSelectType === PREVIEW_TYPE.UPDATE) {
        response = await uploadUpdateExerciseQuestions({
          file: fileExercises,
          is_preview: IS_PREVIEW.PREVIEW,
        });
      }

      if (!response) return;
      setExerciseQuestionsUploadList(response);
      setPreviewType(previewSelectType);

      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      const lastPage = Math.ceil(response.length / PER_PAGE);

      setPagination({
        ...defaultPagination,
        total: response.length,
        last_page: lastPage,
      });
    } catch (error: any) {
      if (error) {
        const listError = error?.data?.errors?.flat(Infinity);
        setErrors(listError);
        showNotification(
          ETypeNotification.ERROR,
          EXCEPTION.ACTION_FAILURE,
          EPlacement.BOTTOM_RIGHT
        );
      } else {
        setFileExercises(undefined);
        showNotification(
          ETypeNotification.ERROR,
          EXCEPTION.TRY_AGAIN,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
    setIsLoading(false);
  };

  const handleImportCreate = async () => {
    setIsLoading(true);
    try {
      let response;
      switch (radioValue) {
        case RADIO_SELECT_VALUES.EXERCISES: {
          if (!fileExercises) return;
          // call api here
          response = await uploadCreateExerciseQuestions({
            file: fileExercises,
            is_preview: IS_PREVIEW.NOT_PREVIEW,
          });
          break;
        }
        case RADIO_SELECT_VALUES.EXERCISES_IMAGES: {
          if (!fileExercisesImages) {
            setFileExercisesImagesValidateMessages('ファイルは必須項目です。');
            setIsLoading(false);
            return;
          }
          const payload = {
            file_zip: fileExercisesImages,
            is_creating: 1,
          };

          // call api here
          response = await postUploadExerciseQuestionsImages({ payload });
          break;
        }
        case RADIO_SELECT_VALUES.EXERCISES_EXPLAIN_IMAGES: {
          if (!fileExercisesExplainationImages) {
            setFileExercisesExplainationImagesValidateMessages(
              'ファイルは必須項目です。'
            );
            setIsLoading(false);
            return;
          }
          const payload = {
            file_zip: fileExercisesExplainationImages,
            is_creating: 1,
          };

          // call api here
          response = await postUploadExerciseQuestionsExplainationImages({
            payload,
          });
          break;
        }
        default:
          return;
      }
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      // reset to first screen
      onHandleResetValues();
      setRadioValue(RADIO_SELECT_VALUES.EXERCISES);
    } catch (error: any) {
      setErrors(error?.data?.data?.invalid_files);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
    setIsLoading(false);
  };
  const handleImportEdit = async () => {
    setIsLoading(true);
    try {
      let response;
      switch (radioValue) {
        case RADIO_SELECT_VALUES.EXERCISES: {
          if (!fileExercises) return;
          response = await uploadUpdateExerciseQuestions({
            file: fileExercises,
            is_preview: IS_PREVIEW.NOT_PREVIEW,
          });
          // call api here
          break;
        }
        case RADIO_SELECT_VALUES.EXERCISES_IMAGES: {
          if (!fileExercisesImages) {
            setFileExercisesImagesValidateMessages('ファイルは必須項目です。');
            setIsLoading(false);
            return;
          }
          const payload = {
            file_zip: fileExercisesImages,
            is_creating: 0,
          };

          // call api here
          response = await postUploadExerciseQuestionsImages({ payload });
          break;
        }
        case RADIO_SELECT_VALUES.EXERCISES_EXPLAIN_IMAGES: {
          if (!fileExercisesExplainationImages) {
            setFileExercisesExplainationImagesValidateMessages(
              'ファイルは必須項目です。'
            );
            setIsLoading(false);
            return;
          }
          const payload = {
            file_zip: fileExercisesExplainationImages,
            is_creating: 0,
          };

          // call api here
          response = await postUploadExerciseQuestionsExplainationImages({
            payload,
          });
          break;
        }
        default:
          return;
      }
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      onHandleResetValues();
      setRadioValue(RADIO_SELECT_VALUES.EXERCISES);
    } catch (error: any) {
      setErrors(error?.data?.data?.invalid_files);
      showNotification(
        ETypeNotification.ERROR,
        EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
    setIsLoading(false);
  };

  const backToList = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/exercises');
  };

  const handleSuccess = (file: File, radioType: number) => {
    setExerciseQuestionsUploadList([]);
    setPreviewType(undefined);
    switch (radioType) {
      case RADIO_SELECT_VALUES.EXERCISES:
        setFileExercises(file);
        setFileExercisesValidateMessages(undefined);
        break;
      case RADIO_SELECT_VALUES.EXERCISES_IMAGES:
        setFileExercisesImages(file);
        setFileExercisesImagesValidateMessages(undefined);
        break;
      case RADIO_SELECT_VALUES.EXERCISES_EXPLAIN_IMAGES:
        setFileExercisesExplainationImages(file);
        setFileExercisesExplainationImagesValidateMessages(undefined);
        break;
      default:
        break;
    }
    setErrors([]);
    // reset message errors
  };

  const handleError = () => {
    console.log('Error!');
  };

  const onErrorSizeExercises = () => {
    setFileExercisesValidateMessages(
      'ファイルは、50Mb以下のファイルでなければいけません。'
    );
  };
  const onErrorTypeExercises = () => {
    setFileExercisesValidateMessages(
      'ファイルには、以下のファイルタイプを指定してください。xlsx'
    );
  };
  const onErrorSizeExercisesImages = () => {
    setFileExercisesImagesValidateMessages(
      'ファイルは、5Gb以下のファイルでなければいけません。'
    );
  };
  const onErrorTypeExercisesImages = () => {
    setFileExercisesImagesValidateMessages(
      'ファイルには、以下のファイルタイプを指定してください。zip, 7zip'
    );
  };
  const onErrorSizeExercisesExplainImages = () => {
    setFileExercisesExplainationImagesValidateMessages(
      'ファイルは、5Gb以下のファイルでなければいけません。'
    );
  };
  const onErrorTypeExercisesExplainImages = () => {
    setFileExercisesExplainationImagesValidateMessages(
      'ファイルには、以下のファイルタイプを指定してください。zip, 7zip'
    );
  };

  const handleChangePage = (page: number) => {
    setPagination({
      ...pagination,
      current_page: page,
    });
  };

  const handleSetExerciseList = (pagination: any) => {
    // add line no for table data
    const questionsListWithRow = exerciseQuestionsUploadList.map(
      (question, index) => {
        question.line_no = index + 2;
        return question;
      }
    );
    let questionsListPage;
    // make pagination data
    if (pagination.current_page === 1) {
      questionsListPage = questionsListWithRow.slice(
        0,
        PER_PAGE * pagination.current_page
      );
    } else {
      questionsListPage = questionsListWithRow.slice(
        PER_PAGE * (pagination.current_page - 1),
        PER_PAGE * pagination.current_page
      );
    }

    setExerciseQuestionsListPagination(questionsListPage);
  };

  const onHandleOpenModalEdit = () => {
    setIsShowModalEdit(true);
  };

  const onHandleCloseModalEdit = () => {
    setIsShowModalEdit(false);
  };

  const onHandleOpenModalCreate = () => {
    setIsShowModalCreate(true);
  };

  const onHandleCloseModalCreate = () => {
    setIsShowModalCreate(false);
  };

  useEffect(() => {
    handleSetExerciseList(pagination);
  }, [pagination, exerciseQuestionsUploadList]);

  return (
    <>
      {isLoading && (
        <div className="fixed w-full h-full top-0 left-0 bg-gray-400 z-20 bg-opacity-30">
          <div className="w-full h-full flex items-center justify-center">
            <Spin indicator={antIcon} />
          </div>
        </div>
      )}
      <div className="mx-12 mt-4">
        <div className="text-lg font-bold mb-3">
          練習問題アップロード（登録・更新）
        </div>
        <div className="bg-alice-blue-light py-4 px-5">
          <Radio.Group onChange={onChangeSelectRadio} value={radioValue}>
            <Space direction="vertical">
              {/* Radio upload exercises */}
              <Radio
                className="font-bold"
                value={RADIO_SELECT_VALUES.EXERCISES}
              >
                <div className="w-full">
                  <div className="flex items-center">
                    <div className="text-textGray text-sm font-bold min-w-25 mx-2">
                      問題
                    </div>
                    <div className="mr-8">
                      <Input
                        disabled
                        value={fileExercises?.name}
                        classNames="!w-48 2xl:!w-96 !rounded !mr-8"
                        size="large"
                      />
                      <UploadFile
                        accept=".xlsx"
                        type={['xlsx']}
                        maxSize={52428800}
                        // 50Mb = 52428800 Bytes
                        setFile={(file) =>
                          handleSuccess(file, RADIO_SELECT_VALUES.EXERCISES)
                        }
                        maxCount={1}
                        onError={handleError}
                        onErrorSize={onErrorSizeExercises}
                        onErrorType={onErrorTypeExercises}
                      >
                        <button
                          className={clsx(
                            'rounded bg-primary bg-opacity-10',
                            '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold'
                          )}
                          disabled={
                            radioValue !== RADIO_SELECT_VALUES.EXERCISES
                          }
                        >
                          ファイル選択
                        </button>
                      </UploadFile>
                    </div>
                    {radioValue &&
                      radioValue === RADIO_SELECT_VALUES.EXERCISES && (
                      <div className="flex gap-4">
                        <Button
                          onClick={() =>
                            onHandlePreviewExercises(PREVIEW_TYPE.CREATE)
                          }
                          size={'small'}
                          styleType={EButtonStyleType.PRIMARY_VARIANT}
                          title={'登録プレビュー'}
                        />
                        <Button
                          onClick={() =>
                            onHandlePreviewExercises(PREVIEW_TYPE.UPDATE)
                          }
                          size={'small'}
                          styleType={EButtonStyleType.OUTLINE_PRIMARY}
                          title={'更新プレビュー'}
                        />
                      </div>
                    )}
                  </div>
                  {fileExercisesValidateMessages && (
                    <div style={{ color: '#ff4d4f' }} className="flex">
                      <div className="min-w-25 mx-2"></div>
                      {fileExercisesValidateMessages}
                    </div>
                  )}
                </div>
              </Radio>
              {/* Radio upload exercises images */}
              <Radio
                className="font-bold"
                value={RADIO_SELECT_VALUES.EXERCISES_IMAGES}
              >
                <div className="w-full">
                  <div className="flex items-center">
                    <div className="text-textGray text-sm font-bold min-w-25 mx-2">
                      問題画像
                    </div>
                    <div className="mr-8">
                      <Input
                        disabled
                        value={fileExercisesImages?.name}
                        classNames="!w-48 2xl:!w-96 !rounded !mr-8"
                        size="large"
                      />
                      <UploadFile
                        accept=".zip,.7zip"
                        type={['zip', '7zip']}
                        maxSize={5368709120}
                        // 5Gb = 5368709120 Bytes
                        setFile={(file) =>
                          handleSuccess(
                            file,
                            RADIO_SELECT_VALUES.EXERCISES_IMAGES
                          )
                        }
                        maxCount={1}
                        onError={handleError}
                        onErrorSize={onErrorSizeExercisesImages}
                        onErrorType={onErrorTypeExercisesImages}
                      >
                        <button
                          className={clsx(
                            'rounded bg-primary bg-opacity-10',
                            '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold'
                          )}
                          disabled={
                            radioValue !== RADIO_SELECT_VALUES.EXERCISES_IMAGES
                          }
                        >
                          ファイル選択
                        </button>
                      </UploadFile>
                    </div>
                  </div>
                  {fileExercisesImagesValidateMessages && (
                    <div style={{ color: '#ff4d4f' }} className="flex">
                      <div className="min-w-25 mx-2"></div>
                      {fileExercisesImagesValidateMessages}
                    </div>
                  )}
                </div>
              </Radio>
              {/* Radio upload exercises explain images */}
              <Radio
                className="font-bold"
                value={RADIO_SELECT_VALUES.EXERCISES_EXPLAIN_IMAGES}
              >
                <div className="w-full">
                  <div className="flex items-center">
                    <div className="text-textGray text-sm font-bold min-w-25 mx-2">
                      解説画像
                    </div>
                    <div className="mr-8">
                      <Input
                        disabled
                        value={fileExercisesExplainationImages?.name}
                        classNames="!w-48 2xl:!w-96 !rounded !mr-8"
                        size="large"
                      />
                      <UploadFile
                        accept=".zip,.7zip"
                        type={['zip', '7zip']}
                        maxSize={5368709120}
                        // 5Gb = 5368709120 Bytes
                        setFile={(file) =>
                          handleSuccess(
                            file,
                            RADIO_SELECT_VALUES.EXERCISES_EXPLAIN_IMAGES
                          )
                        }
                        maxCount={1}
                        onError={handleError}
                        onErrorSize={onErrorSizeExercisesExplainImages}
                        onErrorType={onErrorTypeExercisesExplainImages}
                      >
                        <button
                          className={clsx(
                            'rounded bg-primary bg-opacity-10',
                            '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold'
                          )}
                          disabled={
                            radioValue !==
                            RADIO_SELECT_VALUES.EXERCISES_EXPLAIN_IMAGES
                          }
                        >
                          ファイル選択
                        </button>
                      </UploadFile>
                    </div>
                  </div>
                  {fileExercisesExplainationImagesValidateMessages && (
                    <div style={{ color: '#ff4d4f' }} className="flex">
                      <div className="min-w-25 mx-2"></div>
                      {fileExercisesExplainationImagesValidateMessages}
                    </div>
                  )}
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </div>
        {exerciseQuestionsUploadList.length > 0 && (
          <PreviewExcercise
            data={exerciseQuestionsListPagination}
            pagination={pagination}
            handleChangePage={handleChangePage}
            previewType={previewType}
          />
        )}
        {/* Button handle create/update */}
        <div className="flex justify-between mt-8 gap-16">
          <div>
            {errors &&
              errors.map((error, index) => (
                <div key={index} style={{ color: '#ff4d4f' }}>
                  {error}
                </div>
              ))}
          </div>
          <div className="flex gap-4">
            {previewType === PREVIEW_TYPE.CREATE && (
              <Button
                onClick={onHandleOpenModalCreate}
                size={'small'}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title={'登録'}
              />
            )}
            {previewType === PREVIEW_TYPE.UPDATE && (
              <Button
                onClick={onHandleOpenModalEdit}
                size={'small'}
                styleType={EButtonStyleType.PRIMARY_VARIANT}
                title={'更新'}
              />
            )}
            {radioValue &&
              (radioValue === RADIO_SELECT_VALUES.EXERCISES_IMAGES ||
                radioValue ===
                  RADIO_SELECT_VALUES.EXERCISES_EXPLAIN_IMAGES) && (
              <>
                <Button
                  onClick={onHandleOpenModalCreate}
                  size={'small'}
                  styleType={EButtonStyleType.PRIMARY_VARIANT}
                  title={'登録'}
                />
                <Button
                  onClick={onHandleOpenModalEdit}
                  size={'small'}
                  styleType={EButtonStyleType.PRIMARY_VARIANT}
                  title={'更新'}
                />
              </>
            )}
            <Button
              onClick={backToList}
              size={'small'}
              styleType={EButtonStyleType.OUTLINE_PRIMARY}
              title={'戻る'}
            />
          </div>
        </div>
        <ConfirmModal
          visible={isShowModalEdit}
          onClose={onHandleCloseModalEdit}
          onConfirm={handleImportEdit}
          content={MODAL_MESSAGE.CONFIRM_EDIT}
        />
        <ConfirmModal
          visible={isShowModalCreate}
          onClose={onHandleCloseModalCreate}
          onConfirm={handleImportCreate}
          content={MODAL_MESSAGE.CONFIRM_CREATE}
        />
      </div>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default ExersciesUploadPage;
