import {
  deleteVideoAction,
  getVideoAction,
  getVideosAction,
  postVideoAction,
} from '@/store/actions';
import { all, takeLatest } from 'redux-saga/effects';
import { getVideosSaga } from './get-videos';
import { postVideoSaga } from './post-video';
import { deleteVideoSaga } from './delete-video';
import { getVideoSaga } from './get-video';

export default function* root(): Generator {
  yield all([
    takeLatest(getVideosAction.request.type, getVideosSaga),
    takeLatest(postVideoAction.request.type, postVideoSaga),
    takeLatest(deleteVideoAction.request.type, deleteVideoSaga),
    takeLatest(getVideoAction.request.type, getVideoSaga),
  ]);
}
