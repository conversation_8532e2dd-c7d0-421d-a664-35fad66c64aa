import { TKnowledgeBoard } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetKnowledgeBoardPaths = {
  id: string;
};

export type TGetKnowledgeBoardMaterials = {
  paths: TGetKnowledgeBoardPaths;
};

export type TGetKnowledgeBoardResponse = {
  data: TKnowledgeBoard;
};

export const getKnowledgeBoard = async (
  materials: TGetKnowledgeBoardMaterials
): Promise<TGetKnowledgeBoardResponse> => {
  const { paths } = materials;
  const response = await ApiService.get(`/knowledge-boards/${paths.id}`);
  return response.data;
};
