import { TCouponPlan, TListData } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetCouponPlansParams = {
  coupon_id: string;
};

export type TGetCouponPlansMaterials = {
  params?: TGetCouponPlansParams;
};

export type TGetCouponPlansResponse = TListData<TCouponPlan>;

export const getCouponPlans = async (
  materials: TGetCouponPlansMaterials
): Promise<TGetCouponPlansResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/coupon-plans', { params });
  return response.data;
};
