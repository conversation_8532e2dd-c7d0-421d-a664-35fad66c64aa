import { ESortType } from '@/commons/enums';
import { TKnowledgeBoardsFilter } from '@/commons/types';
import { KnowlegdeBoardsList } from '@/containers';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';

const KnowledgeBoards = ({ filter }: { filter: TKnowledgeBoardsFilter }) => {
  return (
    <div className="mx-12 my-4">
      <div className="flex justify-between items-center">
        <h1 className="text-lg font-bold text-black-kj">知識板一覧</h1>
      </div>
      <div className="mt-3 w-full">
        <KnowlegdeBoardsList query={filter} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  try {
    const category_id = query?.category_id
      ? Number(query?.category_id)
      : undefined;
    const sub_category_id = query?.sub_category_id
      ? Number(query?.sub_category_id)
      : undefined;
    const question_id = query?.question_id
      ? Number(query?.question_id)
      : undefined;
    const types =
      query?.types
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const teacher_reply =
      query?.teacher_reply
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const admin_reply =
      query?.admin_reply
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const isPublic =
      query?.is_publics
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const order_by_last_updated_at = query?.order_by_last_updated_at || null;
    const filter = removeEmpty({
      ...query,
      category_id,
      sub_category_id,
      question_id,
      types,
      teacher_reply,
      admin_reply,
      is_publics: isPublic,
      order_by_last_updated_at,
    });
    console.log('filter: ', filter);
    return {
      props: { filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};
export default KnowledgeBoards;
