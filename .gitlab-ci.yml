stages:
  - sonar
  - DeployDev
  - DeployStaging
  - DeployProd
  # - DeployProd
  # - CheckMergeRequest
  # - DeployTest

variables:
  # TAG_LATEST: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:latest
  # TAG_COMMIT: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:$CI_COMMIT_SHORT_SHA
  DOCKER_TLS_CERTDIR: "/certs"
  # Uncomment below 3 variables when using Kubernetes executor
  # DOCKER_HOST: tcp://docker-dind:2375
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  RUNNER_TAG: hblab-prod-eks-internal

sonarqube-check:
  stage: sonar
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: ['']
  variables:
    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar' # Defines the location of the analysis task cache
    GIT_DEPTH: '0' # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: '${CI_JOB_NAME}'
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  only:
    - develop
    - staging
    - master

DeployDev:
  stage: DeployDev
  image: docker:24.0.5
  services:
  - docker:24.0.5-dind
  only:
    - develop
  environment:
    name: dev
  # cache:
  #   key:
  #     files:
  #       - package.json
  #   paths:
  #     - vendor/
  script:
    - !reference [.deploy, script]

DeployStaging:
  stage: DeployStaging
  image: docker:24.0.5
  services:
  - docker:24.0.5-dind
  only:
    - staging
  environment:
    name: staging
  # cache:
  #   key:
  #     files:
  #       - package.json
  #   paths:
  #     - vendor/
  script:
    - !reference [.deploy, script]

DeployProd:
  stage: DeployProd
  image: docker:24.0.5
  services:
  - docker:24.0.5-dind
  only:
    - master
  environment:
    name: prod
  # cache:
  #   key:
  #     files:
  #       - package.json
  #   paths:
  #     - vendor/
  script:
    - !reference [.deploy, script]

.deploy:
  script:
    - apk add curl jq python3 py3-pip
    - pip install awscli
    - aws ecr get-login-password | docker login --username AWS --password-stdin $REPOSITORY_URI
    - echo "$ENV_FILE" > .env
    - docker pull $REPOSITORY_URI/$IMAGE_REPO_NAME:latest || true
    - docker build --cache-from $REPOSITORY_URI/$IMAGE_REPO_NAME:latest -t $REPOSITORY_URI/$IMAGE_REPO_NAME:latest .
    - docker push $REPOSITORY_URI/$IMAGE_REPO_NAME:latest
