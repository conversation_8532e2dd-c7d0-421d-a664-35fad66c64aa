import { Image } from '@/components';
import { TQuestionScreenProps } from './questions.types';
import { TExerciseResponse, TQuestion } from '@/commons/types';
import { useMemo } from 'react';
import QuestionSigleChoice from './QuestionSigleChoice';
import QuestionMultipleChoice from './QuestionMultipleChoice';
import { extractContent } from '@/utils/validate';

const QuestionContentScreen = ({
  question,
  disabled,
  handleChoice,
  choiceSelected,
  choiceCorrect,
  isExamQuestion = false,
}: TQuestionScreenProps) => {
  const choiceContent = useMemo(() => {
    if (!question) {
      return null;
    }
    const choices =
      (question as TExerciseResponse).exercise_choices ||
      (question as TQuestion).question_choices;
    const type = (question as TQuestion).type;
    if (!type) {
      return (
        <QuestionSigleChoice
          choices={choices}
          choiceSelected={choiceSelected}
          choiceCorrect={choiceCorrect}
          disabled={disabled}
          handleChoice={handleChoice}
        />
      );
    }
    return (
      <QuestionMultipleChoice
        type={type}
        choices={choices}
        disabled={disabled}
        choiceSelected={choiceSelected}
        choiceCorrect={choiceCorrect}
        handleChoice={handleChoice}
      />
    );
  }, [choiceCorrect, choiceSelected, disabled, handleChoice, question]);
  if (!question) {
    return null;
  }
  return (
    <div className="rounded px-2 py-3 bg-new-white grid grid-cols-1 gap-2.5">
      <div className="bg-white px-3 pt-5 pb-2.5 rounded">
        <div className="pb-2 flex justify-between items-center border-b border-blue-border border-dashed font-bold text-sm text-textGray">
          <div>{isExamQuestion && <>問{question.show_order || 0}</>}</div>
          <div className="flex items-center justify-end gap-3">
            <span>{question.question_year}</span>
            <span>{question.id}</span>
          </div>
        </div>
        <div className="w-full flex flex-col justify-center gap-3 py-3">
          {question.content && !!extractContent(question.content) && (
            <div className="w-full">
              <div
                dangerouslySetInnerHTML={{ __html: question.content }}
                className="w-full question-editor text-lg !leading-normal break-all html-render"
              />
            </div>
          )}
          {question.image && (
            <Image
              src={question.image}
              classnames="w-full aspect-video"
              alt="question"
            />
          )}
        </div>
      </div>
      <div className="my-2 flex flex-wrap gap-x-5 gap-y-2.5 justify-center">
        {choiceContent}
      </div>
    </div>
  );
};

export default QuestionContentScreen;
