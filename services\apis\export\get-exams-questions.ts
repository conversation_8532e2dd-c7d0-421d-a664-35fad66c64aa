import ApiService from '@/services/apis';

export type TGetExportExamsQuestionsParams = {
  category_id?: number;
  show_order?: number;
  content?: string;
  title?: string;
  id?: number;
  question_year?: string;
  types?: Array<number>;
  is_actives?: Array<number>;
};

export type TGetExportExamsQuestionsMaterials = {
  params?: TGetExportExamsQuestionsParams;
  exam_id: number;
};

export type TGetExportExamsQuestionsResponse = Blob;

export const getExportExamsQuestions = async (
  materials: TGetExportExamsQuestionsMaterials
): Promise<TGetExportExamsQuestionsResponse> => {
  const { params, exam_id } = materials;
  const response = await ApiService.get(`/export/${exam_id}/questions`, {
    params,
    responseType: 'blob',
  });
  return response.data;
};
