import {
  TGetKnowledgeBoardsResponse,
  getKnowledgeBoards,
} from '@/services/apis';
import { getKnowledgeBoardsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getKnowledgeBoardsSaga(
  action: ActionType<typeof getKnowledgeBoardsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getKnowledgeBoards, materials);
    const getKnowledgeBoardsResponse = response as TGetKnowledgeBoardsResponse;
    yield put(getKnowledgeBoardsAction.success(getKnowledgeBoardsResponse));
    successCallback?.(getKnowledgeBoardsResponse);
  } catch (err) {
    yield put(getKnowledgeBoardsAction.failure(err));
    failedCallback?.(err);
  }
}
