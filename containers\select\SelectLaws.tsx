import { EIsActive } from '@/commons/enums';
import { TOptionLaws } from '@/commons/types';
import { Select } from '@/components/Select';
import { getSelectLaws } from '@/services/apis';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { TFilterSelectCategory, TSelectLawProps } from './select.types';

const { Option } = Select;

const SelectLaws = ({
  value,
  category_id,
  onChange,
  size = 'large',
  className = 'w-full rounded',
  placeholder = '選択してください',
}: TSelectLawProps) => {
  const [filterSelect, setFilterSelect] = useState<TFilterSelectCategory>({
    page: 1,
    name: '',
  });
  const [options, setOptions] = useState<Array<TOptionLaws>>([]);
  const [lastPage, setLastPage] = useState<number>(1);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const fetchData = async (
    category_id: string,
    page: number = 1,
    name?: string
  ) => {
    try {
      const params = {
        category_id,
        page,
        name: name ? name : undefined,
        is_actives: [EIsActive.ENABLE, EIsActive.DISABLE],
      };
      const { data, meta } = await getSelectLaws({ params });
      setLastPage(meta.last_page);
      if (meta.current_page === 1) {
        setOptions(data);
      } else {
        setOptions((current) => [...current, ...data]);
      }
    } catch (error) {
      setOptions([]);
    }
  };

  useEffect(() => {
    if (!category_id) {
      setOptions([]);
      return;
    }
    fetchData(category_id, filterSelect?.page, filterSelect?.name);
  }, [category_id, filterSelect?.name, filterSelect?.page]);

  // useEffect(() => {
  //   const fetchLaw = async (id: any) => {
  //     try {
  //       const category = await getDetailLaws(id);
  //       const law = category?.data;
  //       if (law) {
  //         setLable({
  //           id: Number(law.id || id),
  //           name: law.name,
  //         });
  //       }
  //     } catch (error) {
  //       return null;
  //     }
  //   };
  //   const check = options.find(({ id }) => `${value}` === `${id}`);
  //   if (!options.length || lable?.id === value || check || !value) {
  //     return;
  //   }
  //   fetchLaw(value);
  // }, [lable, options, value]);

  const setKeyword = (name: string) => {
    if (name !== filterSelect.name) {
      setFilterSelect({ page: 1, name });
    }
  };

  const loadMore = useCallback(() => {
    if (
      !filterSelect?.page ||
      (filterSelect?.page && filterSelect?.page >= lastPage)
    ) {
      return null;
    }
    setFilterSelect((current) => ({
      ...current,
      page: (current?.page || 0) + 1,
    }));
  }, [filterSelect?.page, lastPage]);

  useEffect(() => {
    const check = options.find(({ id }) => `${value}` === `${id}`);
    if (!check && value) {
      loadMore();
    }
  }, [loadMore, options, value]);

  const handleOnChange = (value: string) => {
    const option = options.find(({ id }) => id === Number(value));
    if (option) {
      onChange?.(option);
    }
  };

  const valueOption = useMemo(() => {
    if (!options.length) {
      return;
    }
    return value ? `${value}` : undefined;
  }, [value, options]);

  return (
    <Select
      showSearch={true}
      onSearch={setKeyword}
      onLoadMore={loadMore}
      onChange={handleOnChange}
      size={size}
      className={className}
      value={valueOption}
      placeholder={placeholder}
    >
      {options.map((option) => (
        <Option key={option.id} value={`${option.id}`}>
          {` (ID:${option.id}) ${option.name}`}
        </Option>
      ))}
    </Select>
  );
};

export default SelectLaws;
