import ApiService from '@/services/apis';

export type TUploadUsers = {
  is_preview: number;
  file: File;
};
export type TUploadUsersResponse = {
  line_no?: number;
  login_id: string;
  username: string;
  username_kana: string;
  email: string;
  password: string;
  role_id: number;
  plan_start_date: string;
  plan_end_date: string;
  plan_status: string;
  register_type: number;
  video_plan: number;
  is_active: number;
};

export const uploadUsers = async (
  materials: TUploadUsers
): Promise<Array<TUploadUsersResponse>> => {
  const bodyFormData = new FormData();
  bodyFormData.append('file', materials?.file);
  bodyFormData.append('is_preview', `${materials?.is_preview}`);
  const response = await ApiService.post(
    '/import/users',
    bodyFormData,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
    }
  );
  return response.data.data;
};
