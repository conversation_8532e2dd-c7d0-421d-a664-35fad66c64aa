import { TGetHomeResponse, getHome } from '@/services/apis';
import { getHomeAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getHomeSaga(
  action: ActionType<typeof getHomeAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getHome, materials);
    const getHomeResponse = response as TGetHomeResponse;
    yield put(getHomeAction.success(getHomeResponse));
    successCallback?.(getHomeResponse);
  } catch (err) {
    yield put(getHomeAction.failure(err));
    failedCallback?.(err);
  }
}
