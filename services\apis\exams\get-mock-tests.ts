import { TListData, TMockTest } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetMockTestsParams = {
  category_id?: number;
  page?: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetMockTestsMaterials = {
  params?: TGetMockTestsParams;
};

export type TGetMockTestsResponse = TListData<TMockTest>;

export const getMockTests = async (
  materials: TGetMockTestsMaterials
): Promise<TGetMockTestsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/digital-texts', { params });
  return response.data;
};
