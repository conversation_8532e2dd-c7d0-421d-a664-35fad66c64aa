import { EIsActive } from '@/commons/enums';
import { Button, EButtonStyleType, Input, InputNumber } from '@/components';
import { Checkbox } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { useEffect, useState } from 'react';
import { SelectBigCategory, SelectSubCategory } from '../select';
import { TExerciseFilter, TFilterMockTestsProps } from './excercise.type';

const FilterExcercise = ({
  defaultFilter,
  setFilter,
  clearFilter,
}: TFilterMockTestsProps) => {
  const [filterObject, setFilterObject] = useState<TExerciseFilter>();

  useEffect(() => {
    setFilterObject(defaultFilter);
  }, [defaultFilter]);

  const handleChangeCategoryId = (category_id: string) => {
    setFilterObject((current) => ({
      ...current,
      category_id: category_id === '' ? undefined : category_id,
    }));
  };

  const handleChangeProblemTitle = (title: string) => {
    setFilterObject((current) => ({
      ...current,
      title: title === '' ? undefined : title,
    }));
  };

  const handleChangeQuestionYear = (question_year: string) => {
    setFilterObject((current) => ({
      ...current,
      question_year: question_year === '' ? undefined : question_year,
    }));
  };

  const handleChangeProblemId = (id: number) => {
    setFilterObject((current) => ({
      ...current,
      id,
    }));
  };

  const handleChangeContent = (content: string) => {
    setFilterObject((current) => ({
      ...current,
      content: content === '' ? undefined : content,
    }));
  };

  const handlechangeSubCategoryId = (sub_category_id: string) => {
    setFilterObject((current) => ({
      ...current,
      sub_category_id: sub_category_id === '' ? undefined : sub_category_id,
    }));
  };

  const handlechangeIsActives = (is_actives: Array<CheckboxValueType>) => {
    setFilterObject((current) => ({ ...current, is_actives }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterObject);
  };

  const handleClearFilter = () => {
    clearFilter?.();
  };

  return (
    <div className="grid grid-cols-1 gap-6 w-full">
      <div className="grid grid-cols-2 gap-x-5 gap-y-4 w-full">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">大項目名</p>
          <SelectBigCategory
            value={filterObject?.category_id}
            onChange={handleChangeCategoryId}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">小項目名</p>
          <SelectSubCategory
            onChange={handlechangeSubCategoryId}
            value={filterObject?.sub_category_id}
            category_id={filterObject?.category_id}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">問題ID</p>
          <InputNumber
            value={filterObject?.id}
            size="large"
            classNames="!w-full !rounded"
            onChange={handleChangeProblemId}
            hideControls
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">出題年度</p>
          <Input
            value={filterObject?.question_year?.toString()}
            size="large"
            classNames="!w-full !rounded"
            onChange={handleChangeQuestionYear}
            maxLength={50}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">問題タイトル</p>
          <Input
            value={filterObject?.title}
            size="large"
            classNames="w-full !rounded"
            onChange={handleChangeProblemTitle}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">問題本文</p>
          <Input
            value={filterObject?.content}
            size="large"
            classNames="!w-full !rounded"
            onChange={handleChangeContent}
          />
        </div>
        <div className="col-span-1 grid grid-cols-2">
          <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj">有効・無効</p>
            <div className="flex flex-row items-center">
              <Checkbox.Group
                onChange={handlechangeIsActives}
                value={filterObject?.is_actives}
              >
                <Checkbox value={EIsActive.ENABLE}>有効</Checkbox>
                <Checkbox value={EIsActive.DISABLE}>無効</Checkbox>
              </Checkbox.Group>
            </div>
          </div>
        </div>
        <div className="col-span-1 grid">
          <div className="flex items-center justify-end gap-2.5">
            <Button
              styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
              title="クリア"
              className="!font-normal"
              onClick={handleClearFilter}
            />
            <Button
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="検索"
              className="!font-normal"
              onClick={handleUpdateFilter}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterExcercise;
