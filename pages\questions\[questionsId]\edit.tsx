import {
  EIsActive,
  EPlacement,
  EQuestionForm,
  ERoleId,
  ETypeNotification,
} from '@/commons/enums';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { PracticeQuestionForm } from '@/containers';
import { getPracticeQuestionAction, getQuestionAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { Form } from 'antd';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { helpers, putUpdatePracticeQuestion } from '@/services/apis';
import { showNotification } from '@/utils/functions';
import { type } from 'os';
import { GetServerSideProps } from 'next';
import NotificationErrors from '@/components/NotificationErrors';
import { resetPracticeQuestionsSlice } from '@/store/slices/practice-questions';

const ViewExamQuestion = () => {
  const router = useRouter();
  const [isShowModalSubmit, setIsShowModalSubmit] = useState<boolean>(false);
  const [form] = Form.useForm<any>();
  const { questionsId } = router.query;
  const { getPracticeQuestionResponse: question } = useSelector(
    (store: RootState) => store.practiceQuestion
  );

  const dispatch = useDispatch();

  useEffect(() => {
    if (questionsId && typeof questionsId === 'string') {
      dispatch(
        getPracticeQuestionAction.request({ paths: { id: questionsId } })
      );
    }
  }, [questionsId, dispatch]);

  useEffect(() => {
    if (!question || !question.data) {
      return;
    }
    const { data } = question;
    const {
      category,
      allocation_point,
      content,
      type,
      is_active,
      explanation,
      image,
      question_choices,
      question_explanation_images,
      question_year,
      show_order,
      title,
      laws,
      digital_texts,
      videos,
      question_no,
    } = data;
    const numQuestion = type === EQuestionForm.SINGLE ? 1 : 4;
    const numAns = type === EQuestionForm.SINGLE ? 10 : 10;
    const initChoices = Array(numQuestion).fill(undefined);
    const sub_questions = initChoices.map((_, idx) => {
      const items = question_choices.filter(
        ({ subquestion_no }) => subquestion_no === idx + 1
      );
      if (!items.length) {
        return Array(numAns).fill(undefined);
      }
      return Array(numAns)
        .fill(undefined)
        .map((_, idx) => {
          const item = items.find(({ choice_no }) => choice_no === idx + 1);
          if (item) {
            return {
              ...item,
              disable: true,
              is_correct: item?.is_correct === EIsActive.ENABLE,
            };
          }
          return undefined;
        });
    });

    const explanation_images = Array(2)
      .fill(undefined)
      .map((_, idx) => question_explanation_images?.[idx]);

    const category_id = category?.id;
    const law_ids = Array(8)
      .fill(undefined)
      .map((_, idx) => {
        const law = laws?.[idx];
        if (!law) {
          return undefined;
        }
        const { category, name, id } = law;
        return { name, id, category_id: category?.id };
      });
    const video_ids = Array(4)
      .fill(undefined)
      .map((_, idx) => {
        const video = videos?.[idx];
        if (!video) {
          return undefined;
        }
        const { category, name, id } = video;
        return { name, id, category_id: category?.id };
      });
    const digital_text_ids = Array(4)
      .fill(undefined)
      .map((_, idx) => {
        const digital_text = digital_texts?.[idx];
        if (!digital_text) {
          return undefined;
        }
        const { category, name, id } = digital_text;
        return { name, id, category_id: category?.id };
      });

    form.setFieldsValue({
      category_id,
      allocation_point,
      content,
      type,
      is_active,
      explanation,
      image,
      sub_questions,
      explanation_images,
      question_year,
      show_order,
      title,
      law_ids,
      digital_text_ids,
      video_ids,
      question_no,
    });
  }, [form, question]);

  const backToList = () => {
    dispatch(resetPracticeQuestionsSlice());
    router.push('/questions');
  };
  const backToDetail = () => {
    dispatch(resetPracticeQuestionsSlice());
    router.push(`/questions/${questionsId}/view`);
  };

  const onCloseModalSubmit = () => {
    setIsShowModalSubmit(false);
  };
  const showModalSubmit = async () => {
    try {
      await form.validateFields();
      setIsShowModalSubmit(true);
    } catch (error) {}
  };

  const handleClickSubmit = async () => {
    if (!questionsId || typeof questionsId !== 'string') return;
    const show_order = form.getFieldValue('show_order');
    try {
      const response = await putUpdatePracticeQuestion({
        path: { id: questionsId },
        payload: { show_order },
      });

      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      backToDetail();
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  return (
    <div className="mx-12 my-4 grid grid-cols-1 gap-5">
      <div className="flex justify-between items-center">
        <div className="text-lg font-bold text-black-kj flex">
          <span>実践問題編集</span>
        </div>
      </div>
      <div className="w-full px-5 pt-4 bg-new-white">
        <h2 className="text-black-kj font-bold !mb-5">
          問題ID:
          <span className="mx-3 text-primary text-base">
            {question?.data.id}
          </span>
        </h2>
        <PracticeQuestionForm
          formInstance={form}
          disable
          isQuestionsEdit={true}
        />
      </div>
      <div className="w-full flex items-center justify-end gap-5">
        <Button
          styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
          title="戻る"
          className="!font-normal"
          onClick={backToDetail}
        />
        <Button
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          title="更新"
          className="!font-normal"
          onClick={showModalSubmit}
        />
      </div>
      <ConfirmModal
        visible={isShowModalSubmit}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
        onClose={onCloseModalSubmit}
        onConfirm={handleClickSubmit}
      />
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default ViewExamQuestion;
