
import { DigitalText } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetDigitalText = any;

export const createDigitalText = async (payload: DigitalText): Promise<TGetDigitalText> => {
  const response = await ApiService.post('/digital-texts', payload);
  return response.data;
};

export const getDetailDigitalText = async (id: any ): Promise<TGetDigitalText> => {
  const response = await ApiService.get(`/digital-texts/${id}`);
  return response.data;
};

export const updateDigitalText = async (id: any, payload: DigitalText): Promise<TGetDigitalText> => {
  const response = await ApiService.put(`/digital-texts/${id}`, payload);
  return response.data;
};

export const deleteDigitalText = async (id: any): Promise<TGetDigitalText> => {
  const response = await ApiService.delete(`/digital-texts/${id}`);
  return response.data;
};