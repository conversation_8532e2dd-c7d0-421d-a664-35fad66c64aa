import { EIsActive, EQuestionForm } from '@/commons/enums';
import { TListData, TQuestion } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetPracticeQuestionsParams = {
  page: number;
  category_id?: string;
  show_order?: number;
  question_year?: string;
  id?: number;
  title?: string;
  content?: string;
  is_actives?: Array<EIsActive>;
  types?: Array<EQuestionForm>;
};

export type TGetPracticeQuestionsMaterials = {
  params?: TGetPracticeQuestionsParams;
};

export type TGetPracticeQuestionsResponse = TListData<TQuestion>;

export const getPracticeQuestions = async (
  materials: TGetPracticeQuestionsMaterials
): Promise<TGetPracticeQuestionsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/practice-questions', {
    params,
  });
  return response.data;
};
