import ApiService from '@/services/apis';

export type TGetExportDigitalTextsParams = {
  category_id?: number;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetExportDigitalTextsMaterials = {
  params?: TGetExportDigitalTextsParams;
};

export type TGetExportDigitalTextsResponse = Blob;

export const getExportDigitalTexts = async (
  materials: TGetExportDigitalTextsMaterials
): Promise<TGetExportDigitalTextsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/export/digital-texts', {
    params,
    responseType: 'blob',
  });
  return response.data;
};
