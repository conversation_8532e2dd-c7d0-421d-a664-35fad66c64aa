import AuthorizedInstance from 'services/authorized-api';
import helpers from '@/services/helpers';
import { browserConfig } from '@/commons/constants';

const ApiService = AuthorizedInstance(browserConfig.apiServiceBaseUrl);

export default ApiService;
export { helpers };

export * from './account';
export * from './auth';
export * from './notification';
export * from './settings';
export * from './category';
export * from './sub-category';
export * from './select2';
export * from './laws';
export * from './digital-texts';
export * from './administrator';
export * from './my-notification';
export * from './export';
export * from './common';
export * from './exams';
export * from './excercise';
export * from './users';
export * from './questions';
export * from './admins';
export * from './videos';
export * from './practice-questions';
export * from './knowledgeboards';
export * from './accuracy-rate';
export * from './home';
export * from './coupon';
