import { TPostQuestionMaterials, TPostQuestionResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPostQuestionAction {
  POST_QUESTION = 'POST_QUESTION',
  POST_QUESTION_REQUEST = 'POST_QUESTION_REQUEST',
  POST_QUESTION_SUCCESS = 'POST_QUESTION_SUCCESS',
  POST_QUESTION_FAILED = 'POST_QUESTION_FAILED',
}

// TYPES

export type TPostQuestionRequest = {
  type: EPostQuestionAction.POST_QUESTION_REQUEST;
  payload: {
    materials: TPostQuestionMaterials;
    successCallback?: (response: TPostQuestionResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPostQuestionSuccess = {
  type: EPostQuestionAction.POST_QUESTION_SUCCESS;
  payload: { response?: TPostQuestionResponse };
};

export type TPostQuestionFailed = {
  type: EPostQuestionAction.POST_QUESTION_FAILED;
};

export const postQuestionAction = {
  request: createActionCreator(
    EPostQuestionAction.POST_QUESTION_REQUEST,
    (resolve) =>
      (
        materials: TPostQuestionMaterials,
        successCallback?: (response: TPostQuestionResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TPostQuestionRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPostQuestionAction.POST_QUESTION_SUCCESS,
    (resolve) =>
      (response?: TPostQuestionResponse): TPostQuestionSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPostQuestionAction.POST_QUESTION_FAILED,
    (resolve) =>
      (error: unknown): TPostQuestionFailed =>
        resolve({ error })
  ),
};
