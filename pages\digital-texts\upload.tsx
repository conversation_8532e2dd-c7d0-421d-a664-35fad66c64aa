import { ERoleId } from '@/commons/enums';
import { UploadImageDigitalText } from '@/containers';
import helpers from '@/services/helpers';
import { GetServerSideProps } from 'next';

const UploadImageDigitalTextPage = () => {
  return (
    <div className="mx-12 my-4">
      <h1 className="text-lg font-bold text-black-kj">
        デジタルテキスト画像アップロード
      </h1>
      <div className="mt-3 w-full">
        <UploadImageDigitalText />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  // try {
  return {
    redirect: {
      permanent: false,
      destination: '/',
    },
  };
  // const { cookies } = req;
  // const me = helpers.getMeFromReq(cookies);
  // if (!me) {
  //   throw new Error('Not me');
  // }
  // if (me.role_id === ERoleId.TEACHER) {
  //   return {
  //     redirect: {
  //       permanent: false,
  //       destination: '/403',
  //     },
  //   };
  // }
  // return {
  //   props: {},
  // };
  // } catch (error) {
  //   return {
  //     redirect: {
  //       permanent: false,
  //       destination: '/404',
  //     },
  //   };
  // }
};

export default UploadImageDigitalTextPage;
