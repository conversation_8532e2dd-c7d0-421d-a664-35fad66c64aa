import useTranslation from 'next-translate/useTranslation';
import clsx from 'clsx';
import { TConfirmModalProps } from './Modal.types';
import { Button, EButtonStyleType } from '../Button';
import CustomerModal from './CustomerModal';

const ConfirmModal = ({
  visible,
  content,
  classNames,
  centered,
  onClose,
  onConfirm,
  cancelContent = 'キャンセル',
  submitContent = 'OK',
}: TConfirmModalProps) => {
  const { t } = useTranslation('common');

  const handleConfirmModal = () => {
    onConfirm?.();
    onClose?.();
  };

  return (
    <CustomerModal visible={visible} onClose={onClose} centered={centered}>
      <div className={clsx(classNames, 'grid grid-cols-1 gap-7')}>
        <div className="flex items-center justify-between">
          <div className="text-base font-bold min-h-40 min-w-181 bg-new-white flex items-center justify-center">
            {content}
          </div>
        </div>
        <div className="flex justify-center gap-x-4 mt-0.5">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            onClick={onClose}
            title={cancelContent}
          />
          <Button
            styleType={EButtonStyleType.GREEN_BLUE}
            onClick={handleConfirmModal}
            title={submitContent}
          />
        </div>
      </div>
    </CustomerModal>
  );
};

export default ConfirmModal;
