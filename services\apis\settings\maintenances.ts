import ApiService from '@/services/apis';

export type TgetMaintenancesSettingsResponse = {
  content: string;
  is_active: number;
  whitelist_ips: Array<string>;
};

export type TpostMaintenancesSettingsResponse = unknown;

export const getMaintenacesSettings =
  async (): Promise<TgetMaintenancesSettingsResponse> => {
    const response = await ApiService.get('/settings/maintain');
    return response.data.data;
  };

export const postMaintenacesSettings = async (
  payload: any
): Promise<TpostMaintenancesSettingsResponse> => {
  const response = await ApiService.post('/settings/maintain', payload);
  return response.data;
};
