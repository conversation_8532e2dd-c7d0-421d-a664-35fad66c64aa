import { Button, ConfirmModal, EButtonStyleType, Input } from '@/components';
import { Select } from '@/components/Select';
import { Input as AntdInput, Form, Radio } from 'antd';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import {
  TGetAdminsProps,
  deleteAdmins,
  getAdmin,
  helpers,
  putUpdateAdmins,
} from '@/services/apis';
import { showNotification } from '@/utils/functions';
import { EPlacement, ERoleId, ETypeNotification } from '@/commons/enums';
import { GetServerSideProps } from 'next';
import NotificationErrors from '@/components/NotificationErrors';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';
const { Option } = Select;

const AddNewUserPage = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { id } = router.query;
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const [isShowDeleteModal, setIsShowDeleteModal] = useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const {
    checkMaxString,
    checkfieldUsernameKana,
    checkFieldLoginId,
    checkFieldPassword,
  } = validate;

  const onHandleOpenModal = () => {
    setIsShowModal(true);
  };

  const onHandleCloseModal = () => {
    setIsShowModal(false);
  };

  const onHandleOpenDeleteModal = () => {
    setIsShowDeleteModal(true);
  };

  const onHandleCloseDeleteModal = () => {
    setIsShowDeleteModal(false);
  };

  const onHandleConfirmSubmit = async () => {
    const values = form.getFieldsValue();
    const payload = {
      id,
      username: values.username,
      username_kana: values.username_kana,
      role_id: values.role_id,
      login_id: values.login_id,
      password: values.password,
      is_active: values.is_active,
    };

    try {
      const data = await putUpdateAdmins(payload);
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      backToList();
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };
  const onHandleConfirmDelete = async () => {
    const values = form.getFieldsValue();

    try {
      const data = await deleteAdmins({ id });
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      backToList();
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        <NotificationErrors error={error} />,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  const fetchCategory = async () => {
    const payload: TGetAdminsProps = {
      id,
    };
    try {
      const data = await getAdmin(payload);
      form.setFieldsValue({
        username: data.username,
        username_kana: data.username_kana,
        role_id: data.role_id,
        login_id: data.login_id,
        is_active: data.is_active,
      });
    } catch (error: any) {
      if (error?.request.status !== 503) {
        showNotification(
          ETypeNotification.ERROR,
          error?.data?.message
            ? error?.data?.message
            : EXCEPTION.ACTION_SUCCESS,
          EPlacement.BOTTOM_RIGHT
        );
      }
    }
  };

  const backToList = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/admins');
  };

  useEffect(() => {
    fetchCategory();
  }, []);
  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">管理者編集</div>
      <Form
        onFinish={onHandleOpenModal}
        form={form}
        initialValues={{
          is_active: 1,
          role_id: 2,
        }}
      >
        <div className="bg-alice-blue-light py-4 px-5">
          <div className="text-base font-bold mb-3">
            管理者ID <span className="text-primary ml-1">{id}</span>
          </div>
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* row 1 */}
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">氏名</div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('氏名'),
                    },
                    () => checkMaxString('氏名', 255),
                  ] as Rule[]
                }
                name={'username'}
                className="!mb-0"
              >
                <Input classNames={'w-2/5 mt-2'} maxLength={255} />
              </Form.Item>
            </div>
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                氏名カナ
              </div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('氏名カナ'),
                    },
                    () => checkfieldUsernameKana('氏名カナ', 255),
                  ] as Rule[]
                }
                name={'username_kana'}
                className="!mb-0"
              >
                <Input classNames={'!w-full'} maxLength={255} />
              </Form.Item>
            </div>
            {/* row 2 */}
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">権限</div>
              <Form.Item name={'role_id'} className="!mb-0">
                <Select>
                  <Option value={1}>管理者</Option>
                  <Option value={2}>講師</Option>
                </Select>
              </Form.Item>
            </div>
            {/* row 3 */}
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                ログインID
              </div>
              <Form.Item
                rules={
                  [
                    {
                      required: true,
                      message: validationMessage.required('ログインID'),
                    },
                    () => checkFieldLoginId('ログインID', 6, 20),
                  ] as Rule[]
                }
                name={'login_id'}
                className="!mb-0"
              >
                <Input classNames={'w-2/5 mt-2'} maxLength={20} />
              </Form.Item>
            </div>
            <div className="w-full">
              <div className="text-textGray text-sm font-bold mb-2">
                パスワード
              </div>
              <Form.Item
                rules={
                  [() => checkFieldPassword('パスワード', 10, 20)] as Rule[]
                }
                name={'password'}
                className="!mb-0"
              >
                {/* <Input classNames={'w-2/5 mt-2'} maxLength={255} /> */}
                <AntdInput
                  className={'w-2/5 mt-2'}
                  autoComplete="new-password"
                />
              </Form.Item>
              <div style={{ color: '#ff4d4f' }}>
                {validationMessage.new_password()}
              </div>
            </div>
            {/* row 4 */}
            <div>
              <div className="flex flex-row">
                <span className="text-textGray text-sm font-bold">
                  有効・無効
                </span>
              </div>
              <div className="flex flex-row items-center mt-2">
                <Form.Item
                  name="is_active"
                  rules={[
                    {
                      required: true,
                      message: validationMessage.required('有効・無効'),
                    },
                  ]}
                  className="!mb-0"
                >
                  <Radio.Group>
                    <Radio className="font-bold" value={1}>
                      有効
                    </Radio>
                    <Radio className="font-bold" value={2}>
                      無効
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={backToList}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            onClick={onHandleOpenDeleteModal}
            size={'small'}
            styleType={EButtonStyleType.DANGER}
            title={'削除'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
          />
        </div>
      </Form>
      <ConfirmModal
        visible={isShowModal}
        onClose={onHandleCloseModal}
        onConfirm={onHandleConfirmSubmit}
        content={MODAL_MESSAGE.CONFIRM_EDIT}
      />
      <ConfirmModal
        visible={isShowDeleteModal}
        onClose={onHandleCloseDeleteModal}
        onConfirm={onHandleConfirmDelete}
        content={MODAL_MESSAGE.CONFIRM_DELETE}
      />
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default AddNewUserPage;
