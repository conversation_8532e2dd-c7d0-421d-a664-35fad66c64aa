import { getDetailExerciseAction, getExerciseAction } from '@/store/actions';
import { all, takeLatest } from 'redux-saga/effects';
import { getDetailExercisesSaga } from './get-detail-exercises';
import { getExercisesSaga } from './get-exercises';

export default function* root(): Generator {
  yield all([
    takeLatest(getExerciseAction.request.type, getExercisesSaga),
    takeLatest(getDetailExerciseAction.request.type, getDetailExercisesSaga),
  ]);
}
