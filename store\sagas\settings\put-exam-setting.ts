import { TGetExamSettingResponse, putExamSetting } from '@/services/apis';
import { putExamSettingAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* putExamSettingSaga(
  action: ActionType<typeof putExamSettingAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(putExamSetting, materials);
    const putExamSettingResponse = response as TGetExamSettingResponse;
    yield put(putExamSettingAction.success(putExamSettingResponse));
    successCallback?.(putExamSettingResponse);
  } catch (err) {
    yield put(putExamSettingAction.failure(err));
    failedCallback?.(err);
  }
}
