import { EIsActive } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TPostExamPayload = {
  show_order: number;
  name: string;
  is_active: EIsActive;
};

export type TPostExamMaterials = {
  payload: TPostExamPayload;
};

export type TPostExamResponse = unknown;

export const postExam = async (
  materials: TPostExamMaterials
): Promise<TPostExamResponse> => {
  const { payload } = materials;
  const response = await ApiService.post('/exams', payload);
  return response.data;
};
