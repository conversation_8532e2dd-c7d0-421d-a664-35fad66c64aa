import { Button, Image, Input } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { Form } from 'antd';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { getMeAction, postLoginAction } from '@/store/actions';
import { validationMessage } from '@/commons/messages-enum';
import validate from '@/utils/validate';
import { Rule } from 'antd/lib/form';
import { useState } from 'react';
import logo from '@/components/Icon/LogoTopIcon.svg';

const { checkMaxLength } = validate;
const checkLength = () => checkMaxLength('ログインID', 255);
const checkLengthPW = () => checkMaxLength('パスワード', 255);

const LoginFromContainer = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [disableButton, setDisableButton] = useState<boolean>(false);
  const { checkMaxLength, checkMinLength } = validate;

  const onSubmit = async (values: any) => {
    const payload = {
      login_id: values.login_id,
      password: values.password,
      setDisable: setDisableButton,
    };
    setDisableButton(true);
    dispatch(
      postLoginAction.request({ payload }, () => {
        dispatch(getMeAction.request({}));
        router.push('/');
      })
    );
  };
  return (
    <div className="bg-white rounded-md max-w-8xl p-12">
      <Form onFinish={onSubmit} form={form}>
        <div className="w-full flex items-center justify-center">
          <Image src={logo} classnames="h-12 w-64" alt="" />
        </div>
        <div className="text-2xl text-primary font-bold text-center my-4">
          ログイン
        </div>
        <div className="w-96">
          <div className="text-base font-bold mb-3">ログインID</div>
          <Form.Item
            rules={[
              {
                required: true,
                message: validationMessage.required('ログインID'),
              },
              checkLength as Rule,
            ]}
            name="login_id"
          >
            <Input classNames="h-12" placeholder="IDを入力" />
          </Form.Item>
        </div>
        <div className="w-96">
          <div className="text-base font-bold mb-3">パスワード</div>
          <Form.Item
            rules={[
              {
                required: true,
                message: validationMessage.required('パスワード'),
              },
              checkLengthPW as Rule,
            ]}
            name="password"
          >
            <Input
              classNames="h-12"
              placeholder="パスワードを入力"
              type="password"
            />
          </Form.Item>
        </div>
        <div className="w-full flex justify-center">
          <Button
            title="ログイン"
            size="large"
            styleType={EButtonStyleType.PRIMARY}
            className="w-full mt-2"
            htmlType="submit"
            disabled={disableButton}
          />
        </div>
      </Form>
    </div>
  );
};

export default LoginFromContainer;
