import { TGetExamsMaterials, TGetExamsResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetExamsAction {
  GET_EXAMS = 'GET_EXAMS',
  GET_EXAMS_REQUEST = 'GET_EXAMS_REQUEST',
  GET_EXAMS_SUCCESS = 'GET_EXAMS_SUCCESS',
  GET_EXAMS_FAILED = 'GET_EXAMS_FAILED',
}

// TYPES

export type TGetExamsRequest = {
  type: EGetExamsAction.GET_EXAMS_REQUEST;
  payload: {
    materials: TGetExamsMaterials;
    successCallback?: (response: TGetExamsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetExamsSuccess = {
  type: EGetExamsAction.GET_EXAMS_SUCCESS;
  payload: { response?: TGetExamsResponse };
};

export type TGetExamsFailed = {
  type: EGetExamsAction.GET_EXAMS_FAILED;
};

export const getExamsAction = {
  request: createActionCreator(
    EGetExamsAction.GET_EXAMS_REQUEST,
    (resolve) =>
      (
        materials: TGetExamsMaterials,
        successCallback?: (response: TGetExamsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetExamsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetExamsAction.GET_EXAMS_SUCCESS,
    (resolve) =>
      (response?: TGetExamsResponse): TGetExamsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetExamsAction.GET_EXAMS_FAILED,
    (resolve) =>
      (error: unknown): TGetExamsFailed =>
        resolve({ error })
  ),
};
