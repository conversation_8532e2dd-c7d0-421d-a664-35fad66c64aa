import { TComment } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetCommentsPaths = {
  knowledgeBoardId: string;
};

export type TGetCommentsMaterials = {
  paths: TGetCommentsPaths;
};

export type TGetCommentsResponse = {
  data: TComment[];
};

export const getComments = async (
  materials: TGetCommentsMaterials
): Promise<TGetCommentsResponse> => {
  const { paths } = materials;
  const response = await ApiService.get(
    `/knowledge-boards/${paths.knowledgeBoardId}/comments`
  );
  return response.data;
};
