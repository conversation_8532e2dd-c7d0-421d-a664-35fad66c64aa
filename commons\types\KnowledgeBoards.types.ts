import { T<PERSON><PERSON><PERSON><PERSON>, TGetMeResponse } from '@/services/apis';
import { TSubCategory } from './Category.types';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { TUser } from './User.types';
import {
  EAdminReplyStatus,
  EInquiryIsPublic,
  EReplyType,
  ESendToTeacher,
  ESortType,
  ETeacherReplyStatus,
} from '../enums';
import { TAdministrator } from './Administrator.types';

export type TKnowledgeBoards = {
  id?: number;
  question_id: number;
  category?: TCategory;
  category_name?: string;
  sub_category?: TSubCategory;
  sub_category_name?: string;
  type: number;
  inquiry: string;
  comment: number;
  last_update_at: string;
  teacher_reply: null | number;
  admin_reply: null | number;
  public: number;
};

export type TInquiry = {
  id?: number;
  inquiry: string;
  type: number;
  exercise_question?: any;
  practice_question?: any;
  question?: any;
  comments_count: number;
  teacher_reply_status: number;
  admin_reply_status: number;
  is_public: number;
  user_id: number;
  user?: any;
  last_updated_at?: string;
  created_at: string;
  updated_at: string;
};

export type TKnowledgeBoard = {
  id: number;
  send_to_teacher?: ESendToTeacher;
  user_id: number;
  question_id: number;
  type: number;
  inquiry: string;
  is_public: number;
  created_at: string;
  updated_at: string;
  teacher_reply_status: number;
  admin_reply_status: number;
  user: TUser;
  question?: any;
  exercise_question?: any;
  exam_question?: any;
  last_updated_at: string;
};

export type TKnowledgeBoardsFilter = {
  category_id?: string;
  sub_category_id?: string;
  question_id?: number;
  types?: Array<CheckboxValueType>;
  is_publics?: Array<CheckboxValueType>;
  teacher_reply?: Array<CheckboxValueType>;
  admin_reply?: Array<CheckboxValueType>;
  order_by_last_updated_at?: ESortType;
};

export type TKnowledgeBoardsFilterProps = {
  defaultFilter?: TKnowledgeBoardsFilter;
  setFilter?: (value?: TKnowledgeBoardsFilter) => void;
  clearFilter?: () => void;
  me?: TGetMeResponse;
};

export type TComment = {
  id: number;
  comment_no: number;
  knowledge_board_id: number;
  commenter_id: number;
  commenter_type: number;
  reply_type: EReplyType;
  reply_to_id: number;
  content: string;
  is_public: EInquiryIsPublic;
  send_to_teacher?: ESendToTeacher;
  created_at: string;
  updated_at: string;
  teacher_reply_status: ETeacherReplyStatus;
  admin_reply_status: EAdminReplyStatus;
  user?: TUser;
  admin?: TAdministrator;
  reply_to_comment?: any;
  reply_to_knowledge_board?: any;
};
