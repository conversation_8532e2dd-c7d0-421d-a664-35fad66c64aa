import { Input } from '../../../components/Input';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import { DatePicker, Form, Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import validate from '@/utils/validate';
import dynamic from 'next/dynamic';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import locale from 'antd/lib/date-picker/locale/ja_JP';
import moment from 'moment-timezone';
import { useRouter } from 'next/router';
import {
  deleteNotification,
  getNotification,
  updateNotification,
} from '@/services/apis/notification';
import { showNotification } from '@/utils/functions/showNotification';
import { EIsActive, ERoleId, ETypeNotification } from '@/commons/enums';
import { Rule } from 'antd/lib/form';
import {
  EXCEPTION,
  MODAL_MESSAGE,
  validationMessage,
} from '@/commons/messages-enum';
dayjs.extend(customParseFormat);
import type { Moment } from 'moment';
import DatePickerNoti from './DatePickerNoti';
import { GetServerSideProps } from 'next';
import helpers from '@/services/helpers';
import { convertErrorListToArray } from '@/utils/functions';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';

interface DataNotification {
  title: string;
  content: string;
  is_active: string;
  start_time: any;
  end_time: any;
}

const EditNotification = () => {
  moment.tz.setDefault('Asia/Tokyo');
  const {
    validateStartEndDate,
    validateStartDate,
    disabledDateBeforNow,
    checkEmptyString,
    checkMaxLengthEditor,
    checkMaxLength,
  } = validate;
  const EditorBoxForm = dynamic(
    () => import('@/components/EditorBox/EditorBoxForm'),
    { ssr: false }
  );
  const [isVisible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const router = useRouter();
  const { id } = router.query;
  const [isLoading, setIsLoading] = useState(true);
  const [isVisibleEdit, setVisibleEdit] = useState<boolean>(false);
  const [isFisrtStartDate, setFisrtStartDate] = useState<boolean>(true);
  const [isFisrtEndDate, setFisrtEndDate] = useState<boolean>(true);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const data = await getNotification(id);
      const paramData = data?.data;
      if (paramData) {
        form.setFieldsValue({
          is_active: paramData?.is_active,
          content: paramData?.content,
          start_time: moment(paramData?.start_time),
          end_time: moment(paramData?.end_time),
          title: paramData?.title,
        });
        setIsLoading(false);
      }
    } catch (error: any) {
      setIsLoading(false);
      showNotification(ETypeNotification?.ERROR, error?.data?.message);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const ratioRender = () => {
    return (
      <div className="flex flex-row items-center mt-2">
        <Form.Item className={'!mb-0'} name="is_active">
          <Radio.Group>
            <Radio className="font-bold" value={EIsActive.ENABLE}>
              有効
            </Radio>
            <Radio className="font-bold" value={EIsActive.DISABLE}>
              無効
            </Radio>
          </Radio.Group>
        </Form.Item>
      </div>
    );
  };

  const onSubmit = async () => {
    try {
      const values = form.getFieldsValue();
      const payload = {
        title: values?.title,
        content: values?.content,
        is_active: String(values?.is_active),
        start_time: values?.start_time?.format('YYYY-MM-DD HH:mm:00'),
        end_time: values?.end_time?.format('YYYY-MM-DD HH:mm:00'),
      };
      const data = await updateNotification(id, payload);
      if (data) {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        onBack();
      }
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onDelete = async () => {
    try {
      const data = await deleteNotification(id);
      showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
      onBack();
    } catch (error: any) {
      showNotification(
        ETypeNotification?.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE
      );
    }
  };

  const onCloseModal = () => {
    setVisible(false);
  };

  const onOpenModal = () => {
    setVisible(true);
  };

  const onBack = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router?.push('/notifications/');
  };

  const onValuesChange = () => {
    const values = form.getFieldsValue();
    if (values?.start_time && values?.end_time) {
      form.validateFields(['start_time', 'end_time']);
    }
  };

  const onCloseModalEdit = () => {
    setVisibleEdit(false);
  };

  const onOpenModalEdit = () => {
    setVisibleEdit(true);
  };

  if (isLoading) return;

  return (
    <div className="mx-12 mt-4">
      <span className="text-lg font-bold">お知らせ編集</span>
      <ConfirmModal
        visible={isVisible}
        onClose={onCloseModal}
        content={MODAL_MESSAGE.CONFIRM_DELETE}
        onConfirm={onDelete}
      />
      <ConfirmModal
        visible={isVisibleEdit}
        onClose={onCloseModalEdit}
        content={MODAL_MESSAGE?.CONFIRM_EDIT}
        onConfirm={onSubmit}
      />
      <Form
        onValuesChange={onValuesChange}
        form={form}
        onFinish={onOpenModalEdit}
      >
        <div className="bg-alice-blue-light py-4 px-5 mt-3">
          <div className="grid grid-cols-2 justify-between gap-5">
            <div>
              <div>
                <span className="text-textGray text-sm font-bold">
                  タイトル
                </span>
                <Form.Item
                  rules={
                    [
                      {
                        required: true,
                        message: validationMessage.required('タイトル'),
                      },
                      () => checkMaxLength('タイトル', 255),
                    ] as Rule[]
                  }
                  className={'!mb-0'}
                  name={'title'}
                >
                  <Input maxLength={255} classNames={'!mt-2'} />
                </Form.Item>
              </div>
              <div className="mt-7">
                <span className="text-textGray text-sm font-bold">
                  公開期間
                </span>
                <div className="grid grid-cols-2 gap-5 mt-2">
                  <Form.Item
                    rules={
                      [
                        {
                          required: true,
                          message: validationMessage.required('開始日'),
                        },
                        validateStartDate(isFisrtStartDate),
                      ] as Rule[]
                    }
                    className={'!mb-0'}
                    name={'start_time'}
                  >
                    <DatePickerNoti
                      disabledDate={disabledDateBeforNow}
                      setFisrt={() => setFisrtStartDate(false)}
                    />
                  </Form.Item>
                  <Form.Item
                    rules={
                      [
                        {
                          required: true,
                          message: validationMessage.required('終了日'),
                        },
                        validateStartEndDate(isFisrtEndDate),
                      ] as Rule[]
                    }
                    className={'!mb-0'}
                    name={'end_time'}
                  >
                    <DatePickerNoti
                      disabledDate={disabledDateBeforNow}
                      setFisrt={() => setFisrtEndDate(false)}
                    />
                  </Form.Item>
                </div>
                <div className="mt-7">
                  <div className="flex flex-row">
                    <span className="text-textGray text-sm font-bold">
                      有効・無効
                    </span>
                  </div>
                  {ratioRender()}
                </div>
              </div>
            </div>
            <div>
              <span className="text-textGray text-sm font-bold">本文</span>
              <Form.Item
                rules={
                  [
                    checkEmptyString(false),
                    () => checkMaxLengthEditor('本文', 1, 65535),
                  ] as Rule[]
                }
                className={'!mb-0 !mt-2'}
                name={'content'}
              >
                <EditorBoxForm />
              </Form.Item>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            onClick={onBack}
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
          />
          <Button
            onClick={onOpenModal}
            size={'small'}
            styleType={EButtonStyleType.DANGER}
            title={'削除'}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
          />
        </div>
      </Form>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default EditNotification;
