import React from 'react';
import { SizeType } from 'antd/lib/config-provider/SizeContext';

export interface TInputProps {
  classNames?: string;
  placeholder?: string;
  type?: 'text' | 'password' | 'number';
  value?: string;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  width?: number;
  disabled?: boolean;
  numberic?: boolean;
  phoneNumber?: boolean;
  onChange?: (value: string) => void;
  onEnter?: () => void;
  maxLength?: number;
  name?: string;
  size?: SizeType;
}

export interface TInputNumberProps {
  classNames?: string;
  hideControls?: boolean;
  value?: number | null;
  defaultValue?: number;
  min?: number;
  max?: number;
  type?: string;
  size?: SizeType;
  maxLength?: number;
  disabled?: boolean;
  prefix?: React.ReactNode;
  placeholder?: string;
  stringMode?: boolean;
  onChange?: (value: any) => void;
}
