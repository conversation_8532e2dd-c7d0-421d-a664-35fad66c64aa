import { EIsActive, ERoleId } from '@/commons/enums';
import ApiService from '@/services/apis';

export type TGetMeMaterials = {};

export type TGetMeData = {
  username: string;
  username_kana: string;
  login_id: string;
  role_id: ERoleId;
  is_active: EIsActive;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
};
export type TGetMeResponse = { data: TGetMeData };

export const getMe = async (
  materials: TGetMeMaterials
): Promise<TGetMeResponse> => {
  const response = await ApiService.get('/auth/me');
  return response.data;
};
