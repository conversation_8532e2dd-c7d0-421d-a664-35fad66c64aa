import { Form } from 'antd';
import FormUploadItem from './FormUploadItem';
import dynamic from 'next/dynamic';
import LawItemForm from './FormLawItem';
import FormDigitalItem from './FormDigitalItem';
import FormVideoItem from './FormVideoItem';
import { useMemo } from 'react';
import { Rule } from 'antd/lib/form';
import validate from '@/utils/validate';
import { EFolderUpload } from '@/commons/enums';
const EditorBoxForm = dynamic(
  () => import('@/components/EditorBox/EditorBoxInForm'),
  {
    ssr: false,
  }
);

const { checkMaxLengthEditor, checkUniqueItemQuestion } = validate;

const QuestionOtherTab = ({ disable }: { disable?: boolean }) => {
  const rulescExplanation = useMemo(() => {
    const checkLength = () => checkMaxLengthEditor('解説本文', 1, 65535);
    return [checkLength];
  }, []) as Array<Rule>;

  const rulesLaw = useMemo(() => {
    const checkUnique = checkUniqueItemQuestion('法令連携', 'law_ids');
    return [checkUnique] as Array<Rule>;
  }, []);

  const rulesDigital = useMemo(() => {
    const checkUnique = checkUniqueItemQuestion(
      'デジタルテキスト連携',
      'digital_text_ids'
    );
    return [checkUnique] as Array<Rule>;
  }, []);

  const rulesVideo = useMemo(() => {
    const checkUnique = checkUniqueItemQuestion('動画', 'video_ids');
    return [checkUnique] as Array<Rule>;
  }, []);

  return (
    <div className="px-5 pt-9 bg-new-white flex flex-col gap-3">
      <div className="grid grid-cols-4 gap-x-5 items-start">
        <div className="col-span-2 grid-cols-1 grid gap-2">
          <p className="text-black-kj !font-bold">解説本文</p>
          <Form.Item name={'explanation'} rules={rulescExplanation}>
            <EditorBoxForm key="explanation" height="288px" disable={disable} />
          </Form.Item>
        </div>
        <Form.List
          name={'explanation_images'}
          initialValue={[undefined, undefined]}
        >
          {(fields) => {
            return fields.map((field, idx) => (
              <div
                key={field.key}
                className="col-span-1 flex flex-col gap-2"
              >
                <p className="text-black-kj">{`解説画像 ${idx + 1}`}</p>
                <Form.Item
                  name={[idx, 'explanation_image']}
                  className="w-32 2xl:w-72"
                >
                  <FormUploadItem
                    width="w-32 2xl:w-72"
                    accept={'.png,.jpeg,.jpg'}
                    type={['png', 'jpeg', 'jpg']}
                    name={['explanation_images', idx, 'explanation_image']}
                    label={`解説画像 ${idx + 1}`}
                    disable={disable}
                    folder={EFolderUpload.QUESTIONS_EXPLANATIONS}
                  />
                </Form.Item>
              </div>
            ));
          }}
        </Form.List>
      </div>
      <div className="flex flex-col gap-3">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5">
          <Form.List name={'law_ids'} initialValue={Array(8).fill(undefined)}>
            {(fields) => {
              return fields.map((field, idx) => (
                <div key={field.key} className="col-span-1 flex flex-col gap-2">
                  <p className="text-black-kj !font-bold">{`法令 ${
                    idx + 1
                  }`}</p>
                  <Form.Item name={[idx]} rules={rulesLaw}>
                    <LawItemForm disable={disable} />
                  </Form.Item>
                </div>
              ));
            }}
          </Form.List>
        </div>
      </div>
      <div className="flex flex-col gap-3">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5">
          <Form.List
            name={'digital_text_ids'}
            initialValue={[undefined, undefined, undefined, undefined]}
          >
            {(fields) => {
              return fields.map((field, idx) => (
                <div key={field.key} className="col-span-1 flex flex-col gap-2">
                  <p className="text-black-kj !font-bold">{`デジタルテキスト ${
                    idx + 1
                  }`}</p>
                  <Form.Item name={[idx]} rules={rulesDigital}>
                    <FormDigitalItem disable={disable} />
                  </Form.Item>
                </div>
              ));
            }}
          </Form.List>
        </div>
      </div>
      <div className="flex flex-col gap-3">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-x-5">
          <Form.List
            name={'video_ids'}
            initialValue={[undefined, undefined, undefined, undefined]}
          >
            {(fields) => {
              return fields.map((field, idx) => (
                <div key={field.key} className="col-span-1 flex flex-col gap-2">
                  <p className="text-black-kj !font-bold">{`動画 ${
                    idx + 1
                  }`}</p>
                  <Form.Item name={[idx]} rules={rulesVideo}>
                    <FormVideoItem disable={disable} />
                  </Form.Item>
                </div>
              ));
            }}
          </Form.List>
        </div>
      </div>
    </div>
  );
};

export default QuestionOtherTab;
