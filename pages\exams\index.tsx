import { ListExams, TExamsFilter } from '@/containers';
import { removeEmpty } from '@/utils/functions';
import { GetServerSideProps } from 'next';

const ExamsListPage = ({ filter }: { filter: TExamsFilter }) => {
  return (
    <div className="mx-12 my-4">
      <div className="flex justify-between items-center">
        <h1 className="text-lg font-bold text-black-kj">模擬試験一覧</h1>
      </div>
      <div className="mt-3 w-full">
        <ListExams query={filter} />
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  try {
    const is_actives =
      query?.is_actives
        ?.toString()
        .split(',')
        .map((item) => Number(item)) || null;
    const filter = removeEmpty({ ...query, is_actives });
    return {
      props: { filter },
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default ExamsListPage;
