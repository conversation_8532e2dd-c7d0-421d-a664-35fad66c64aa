import {
  EIsActive,
  ENeedCheckWithSetting,
  EQuestionForm,
} from '@/commons/enums';
import { TSubQuestion } from '@/commons/types';
import ApiService from '@/services/apis';

export type TPostQuestionPayload = {
  need_check_with_setting: ENeedCheckWithSetting;
  category_id: number;
  show_order: number;
  question_year: string;
  title: string;
  is_active: EIsActive;
  allocation_point: number;
  content?: string;
  image?: string;
  type: EQuestionForm;
  explanation?: string;
  explanation_images?: Array<{ explanation_image: string }>;
  law_ids?: Array<number>;
  digital_text_ids?: Array<number>;
  video_ids?: Array<number>;
  sub_questions?: Array<Array<TSubQuestion>>;
};

export type TPostQuestionPaths = {
  exam_id: string;
};
export type TPostQuestionMaterials = {
  payload: TPostQuestionPayload;
  paths: TPostQuestionPaths;
};

export type TPostQuestionResponse = unknown;

export const postQuestion = async (
  materials: TPostQuestionMaterials
): Promise<TPostQuestionResponse> => {
  const { payload, paths } = materials;
  const response = await ApiService.post(
    `/exams/${paths.exam_id}/questions`,
    payload
  );
  return response.data;
};
