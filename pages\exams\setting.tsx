import { ERoleId, ETypeNotification } from '@/commons/enums';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { TExamSetting } from '@/commons/types';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { ExamSettingForm } from '@/containers';
import { getExamSettingAction, putExamSettingAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { resetStateSettingSlice } from '@/store/slices/settings';
import { useHistory } from '@/utils/contexts';
import { deepEqualObj, showNotification } from '@/utils/functions';
import { Form } from 'antd';
import { useRouter } from 'next/router';
import { Fragment, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const ExamsSettingPage = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const historyManager = useHistory();
  const canGoBack = historyManager.canGoBack();

  const [form] = Form.useForm<TExamSetting>();
  const [isShowModalSubmit, setIsShowModalSubmit] = useState<boolean>(false);
  const { getExamSettingResponse: examSetting } = useSelector(
    (state: RootState) => state.setting
  );
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    dispatch(getExamSettingAction.request({}));
  }, [dispatch]);

  useEffect(() => {
    if (me?.data.role_id === ERoleId.TEACHER) {
      router.push('/403');
    }
  }, [me?.data.role_id, router]);

  useEffect(() => {
    if (examSetting) {
      const settingsDefault = {
        category_id: undefined,
        question_amount: undefined,
        require_correct_amount: undefined,
      };
      const category_settings =
        examSetting.category_settings &&
        examSetting.category_settings?.length > 0
          ? examSetting.category_settings
          : [settingsDefault];
      const limit_time = !examSetting.limit_time
        ? undefined
        : examSetting.limit_time;
      const pass_point = !examSetting.pass_point
        ? undefined
        : examSetting.pass_point;
      const total_point = !examSetting.total_point
        ? undefined
        : examSetting.total_point;
      form.setFieldsValue({
        limit_time,
        pass_point,
        total_point,
        category_settings,
      });
    }
  }, [examSetting, form]);

  const handleSubmit = (payload: TExamSetting) => {
    dispatch(
      putExamSettingAction.request({ payload }, () => {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        handleClickToBack();
      })
    );
  };

  const handleClickSubmit = () => {
    if (!form) {
      return;
    }
    form.submit();
  };

  const handleClickToBack = () => {
    dispatch(resetStateSettingSlice());
    if (canGoBack) {
      router.back();
      return;
    }
    router.push('/exams');
  };

  const onCloseModalSubmit = () => {
    setIsShowModalSubmit(false);
  };

  const showModalSubmit = async () => {
    try {
      await form.validateFields();
      const values = form.getFieldsValue();
      const change = deepEqualObj(values, examSetting);
      if (!change) {
        setIsShowModalSubmit(true);
      } else {
        handleClickToBack();
      }
    } catch (error) {}
  };

  return (
    <Fragment>
      <ConfirmModal
        visible={isShowModalSubmit}
        content={
          <div className="max-w-xl">{MODAL_MESSAGE.CONFIRM_SETTING}</div>
        }
        onClose={onCloseModalSubmit}
        onConfirm={handleClickSubmit}
      />
      <div className="mx-12 my-4 grid grid-cols-1 gap-3">
        <div className="flex justify-between items-center">
          <h1 className="text-lg font-bold text-black-kj">模擬試験設定</h1>
        </div>
        <div className="w-full">
          <ExamSettingForm formInstance={form} handleSubmit={handleSubmit} />
        </div>
        <div className="w-full flex items-center justify-end gap-5 mt-2">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="戻る"
            className="!font-normal"
            onClick={handleClickToBack}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="更新"
            className="!font-normal"
            onClick={showModalSubmit}
          />
        </div>
      </div>
    </Fragment>
  );
};

export default ExamsSettingPage;
