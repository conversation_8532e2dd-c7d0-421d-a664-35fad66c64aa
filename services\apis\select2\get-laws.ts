import { TListData, TOptionLaws } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetSelectLawsParams = {
  page: number;
  category_id: string;
  name?: string;
  is_actives?: Array<number>;
};

export type TGetSelectLawsMaterials = {
  params?: TGetSelectLawsParams;
};

export type TGetSelectLawsResponse = TListData<TOptionLaws>;

export const getSelectLaws = async (
  materials: TGetSelectLawsMaterials
): Promise<TGetSelectLawsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/select2/laws', { params });
  return response.data;
};
