import { DatePicker } from 'antd';
import React from 'react';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import locale from 'antd/lib/date-picker/locale/ja_JP';
import moment from 'moment-timezone';
dayjs.extend(customParseFormat);
import type { Moment } from 'moment';
import { EFormatDateTime } from '@/commons/enums';

const DatePickerNoti = ({ value, onChange, setFisrt, disabledDate }: any) => {
  const getMinusAfter = (minus: number) => {
    let formatMinus = '00';
    if (minus < 10) {
      formatMinus = '10';
    } else if (10 <= minus && minus < 20) {
      formatMinus = '20';
    } else if (20 <= minus && minus < 30) {
      formatMinus = '30';
    } else if (30 <= minus && minus < 40) {
      formatMinus = '40';
    } else if (40 <= minus && minus < 50) {
      formatMinus = '50';
    } else {
      formatMinus = '00';
    }
    return formatMinus;
  };
  const getMinus = (minus: number) => {
    let formatMinus = '00';
    if (0 < minus && minus <= 10) {
      formatMinus = '10';
    } else if (10 < minus && minus <= 20) {
      formatMinus = '20';
    } else if (20 < minus && minus <= 30) {
      formatMinus = '30';
    } else if (30 < minus && minus <= 40) {
      formatMinus = '40';
    } else if (40 < minus && minus <= 50) {
      formatMinus = '50';
    } else {
      formatMinus = '00';
    }
    return formatMinus;
  };

  const formatDatePicker = (value: Moment | null) => {
    if (!value) return '';
    const current = moment();
    const isAffer = Math.floor(
      moment.duration(current.diff(value)).asMinutes()
    );
    const minus = value.minute();
    const hour = value.hour();
    const year = value.format('YYYY');
    const month = value.format('MM');
    let day = value.format('DD');
    let formatHour = hour;
    if (minus > 50 && minus <= 59) {
      formatHour = hour + 1;
      if (formatHour === 24) {
        formatHour = 0;
        day = moment().add(1, 'days').format('DD');
      }
    }
    const formatMinus = isAffer ? getMinus(minus) : getMinusAfter(minus);
    return `${year}-${month}-${day} ${formatHour}:${formatMinus}`;
  };

  return (
    <DatePicker
      inputReadOnly
      locale={locale}
      format={formatDatePicker}
      disabledDate={disabledDate}
      minuteStep={1}
      placeholder=""
      className="w-full"
      showTime={{ format: 'HH:mm' }}
      value={value}
      onChange={(value: Moment | null) => {
        setFisrt?.();
        if (!value) {
          onChange(null);
          return;
        }
        const date = moment(formatDatePicker(value));
        onChange(date);
      }}
    />
  );
};

export default DatePickerNoti;
