import { TGetSubCategoriesResponse } from '@/services/apis';
import {
  EGetSubCategoriesAction,
  TGetSubCategoriesSuccess,
} from '@/store/actions';
import { createSlice } from '@reduxjs/toolkit';

export interface TSubCategoryState {
  getSubCategoriesResponse?: TGetSubCategoriesResponse;
}

const initialState: TSubCategoryState = {
  getSubCategoriesResponse: undefined,
};

const subCategorySlice = createSlice({
  name: 'subCategories-slice',
  initialState,
  reducers: {
    resetSubCategorySlice: () => initialState,
  },
  extraReducers: {
    [EGetSubCategoriesAction.GET_SUB_CATEGORIES_SUCCESS]: (
      state: TSubCategoryState,
      action: TGetSubCategoriesSuccess
    ): TSubCategoryState => ({
      ...state,
      getSubCategoriesResponse: action.payload?.response,
    }),
  },
});

const {
  reducer: subCategoryReducer,
  actions: { resetSubCategorySlice },
} = subCategorySlice;

export { subCategoryReducer, resetSubCategorySlice };
