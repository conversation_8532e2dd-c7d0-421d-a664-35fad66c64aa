import { Form, Radio } from 'antd';
import { Button, ConfirmModal } from '@/components';
import { EButtonStyleType } from '@/components/Button/Button.enums';
import validate from '@/utils/validate';
import dynamic from 'next/dynamic';
import { Rule } from 'antd/lib/form';
import { useRouter } from 'next/router';
import { MODAL_MESSAGE, validationMessage } from '@/commons/messages-enum';
import { useState } from 'react';
import { FormInstance } from 'rc-field-form';
const EditorBoxForm = dynamic(
  () => import('@/components/EditorBox/EditorBoxForm'),
  {
    ssr: false,
  }
);

export interface SettingsFormProps {
  onSubmit?: (values: any) => Promise<void>;
  form: any;
  initialValues?: any;
  isFirstTime: boolean;
  fieldMessage: string;
}

const SettingsForm = ({
  onSubmit,
  form,
  initialValues,
  isFirstTime,
  fieldMessage,
}: SettingsFormProps) => {
  const { checkEmptyTextEditor, checkMaxLengthEditor } = validate;
  const route = useRouter();

  const [isShowModal, setIsShowModal] = useState<boolean>(false);

  const onOpenModal = () => {
    setIsShowModal(true);
  };

  const onCloseModal = () => {
    setIsShowModal(false);
  };

  const backToHome = () => {
    route.push('/');
  };

  return (
    <>
      <Form onFinish={onOpenModal} form={form} initialValues={initialValues}>
        <div className="bg-alice-blue-light pb-4 px-5">
          <Form.Item
            name="description"
            className="!mb-0"
            rules={
              [
                () => checkEmptyTextEditor(fieldMessage),
                () => checkMaxLengthEditor(fieldMessage, 1, 65535),
              ] as Rule[]
            }
          >
            <EditorBoxForm />
          </Form.Item>
          <div className="mt-8">
            <div className="flex flex-row">
              <span className="text-textGray text-sm font-bold">公開設定</span>
            </div>
            <div className="flex flex-row items-center mt-2">
              <Form.Item
                name="is_public"
                className="!mb-0"
                rules={[
                  {
                    required: true,
                    message: validationMessage.required('公開設定'),
                  },
                ]}
              >
                <Radio.Group className=" font-bold">
                  <Radio value={1}>公開</Radio>
                  <Radio value={2}>非公開</Radio>
                </Radio.Group>
              </Form.Item>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-end mt-2.5 gap-4">
          <Button
            size={'small'}
            styleType={EButtonStyleType.OUTLINE_PRIMARY}
            title={'戻る'}
            onClick={backToHome}
          />
          <Button
            htmlType={'submit'}
            size={'small'}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title={'更新'}
          />
        </div>
        <ConfirmModal
          visible={isShowModal}
          content={
            isFirstTime
              ? MODAL_MESSAGE.CONFIRM_CREATE
              : MODAL_MESSAGE.CONFIRM_EDIT
          }
          onClose={onCloseModal}
          onConfirm={() => onSubmit?.(form.getFieldsValue())}
        />
      </Form>
    </>
  );
};

export default SettingsForm;
