import { Form } from 'antd';
import { Fragment, useMemo } from 'react';
import QuestionItem from './QuestionItem';
import { EXCEPTION, validationMessage } from '@/commons/messages-enum';
import { Rule } from 'antd/lib/form';
import validate from '@/utils/validate';

const QuestionSingleChoice = ({
  name,
  minQuestion = '4',
}: {
  name: string | Array<string | number>;
  minQuestion?: string;
}) => {
  const items = Array(10).fill(undefined);
  const { checkMaxLengthAns } = validate;

  const questionsRules = useMemo(() => {
    return [
      {
        validator: async (_: any, values: Array<any>) => {
          const checkMin =
            values?.filter((value) => value?.content).length >=
            parseInt(minQuestion);
          const checkMinCorrent =
            values?.filter((value) => value?.is_correct).length >= 1;
          if (!checkMin) {
            return Promise.reject(
              new Error(
                validationMessage.minCorrectOption(minQuestion?.toString())
              )
            );
          }
          if (!checkMinCorrent) {
            if (minQuestion === '2') {
              return Promise.reject(
                new Error(EXCEPTION.ONE_CORRECT)
              );
            }
            return Promise.reject(new Error(EXCEPTION.ONE_CORRECT));
          }
          return Promise.resolve();
        },
      },
    ];
  }, [minQuestion]);

  return (
    <div className="pt-4 pb-0.5 px-5 rounded-b-md bg-new-white">
      <Form.List name={name} initialValue={items} rules={questionsRules}>
        {(fields, _, { errors }) => {
          return (
            <Fragment>
              {fields.map((field) => (
                <Form.Item
                  rules={
                    [
                      checkMaxLengthAns(`選択肢 ${field.name + 1}`, 255),
                    ] as unknown as Rule[]
                  }
                  key={field.key}
                  name={field.name}
                >
                  <QuestionItem name={`選択肢 ${field.name + 1}`} />
                </Form.Item>
              ))}
              <Form.ErrorList errors={errors} className="mb-3.5" />
            </Fragment>
          );
        }}
      </Form.List>
    </div>
  );
};

export default QuestionSingleChoice;
