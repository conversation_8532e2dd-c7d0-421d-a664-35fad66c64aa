import ApiService from '@/services/apis';

export type TPracticeQuestionsDataUpdate = {
  id: number;
  show_order: number;
};
export type TPostUpdatePracticeQuestionsPayload = {
  question_show_order: Array<TPracticeQuestionsDataUpdate>;
};

export type TPostUpdatePracticeQuestionsMaterials = {
  payload: TPostUpdatePracticeQuestionsPayload;
};

export type TPostUpdatePracticeQuestionsResponse = unknown;

export const postUpdatePracticeQuestions = async (
  materials: TPostUpdatePracticeQuestionsMaterials
): Promise<TPostUpdatePracticeQuestionsResponse> => {
  const { payload } = materials;
  const response = await ApiService.post(
    '/practice-questions/update-questions',
    payload
  );
  return response.data;
};
