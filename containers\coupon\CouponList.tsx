import {
  EIsActive,
  EPlacement,
  ETypeNotification,
  EVideoPlan,
} from '@/commons/enums';
import { ICoupon, TListData } from '@/commons/types';
import {
  Button,
  ColumnType,
  EButtonStyleType,
  EditIcon,
  Pagination,
  Table,
} from '@/components';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import CouponFilter from './CouponFilter';
import { useDispatch } from 'react-redux';
import moment from 'moment';
import { isObjectEmpty, removeEmpty } from '@/utils/functions';
import { ParsedUrlQueryInput } from 'querystring';
import { setCallBackUrl } from '@/store/slices/status/history';
import { TFilterCoupon } from './coupon.types';
import { getCouponsAction } from '@/store/actions';

const CouponListOrganism = ({ query }: { query: TFilterCoupon }) => {
  const router = useRouter();
  const [filter, setFilter] = useState<TFilterCoupon>();
  const dispatch = useDispatch();
  const [coupons, setCoupons] = useState<TListData<ICoupon>>();
  const { data, meta } = coupons || {};

  const setDefaultFilter = () => {
    const defaultFitler = {};
    setFilter(defaultFitler);
  };

  useEffect(() => {
    if (query && !isObjectEmpty(query)) {
      setFilter(query);
      return;
    }
    setDefaultFilter();
    return () => setFilter(undefined);
  }, [query]);

  const getActions = useCallback(
    (element: ICoupon) => {
      return (
        <div className="flex items-center gap-5">
          <Link
            href={`/coupon/${element.id}/edit`}
            onClick={() => {
              dispatch(setCallBackUrl(router.asPath));
            }}
          >
            <EditIcon />
          </Link>
        </div>
      );
    },
    [dispatch, router.asPath]
  );
  const columns = useMemo(
    () =>
      [
        {
          title: 'ID',
          dataIndex: 'id',
          key: 'id',
          render: (element) => (
            <div className="max-w-30 break-all">{element?.id || '...'}</div>
          ),
        },
        {
          title: 'クーポン名',
          dataIndex: 'name',
          key: 'name',
          render: (element) => (
            <div className="max-w-25 break-all">{element?.name || '...'}</div>
          ),
        },
        {
          title: 'クーポンコード',
          dataIndex: 'code',
          key: 'code',
          render: (element) => (
            <div className="max-w-25 break-all">{element?.code || '...'}</div>
          ),
        },
        {
          title: '教習所名',
          dataIndex: 'training_school',
          key: 'training_school',
          render: (element) => (
            <div className="max-w-30 break-all">
              {element?.training_school || '...'}
            </div>
          ),
        },
        {
          title: '値引き',
          dataIndex: 'discount',
          key: 'discount',
          render: (element) => (
            <div className="max-w-25 break-all">
              {element?.discount || '...'}
            </div>
          ),
        },
        {
          title: '有効期限',
          dataIndex: 'expiration',
          key: 'expiration',
          render: (element) => (
            <div className="">
              <span>
                {moment(element?.start_date).format('YYYY-MM-DD') || '...'}
              </span>
              <span className="mx-1">~</span>
              <span>
                {moment(element?.end_date).format('YYYY-MM-DD') || '...'}
              </span>
            </div>
          ),
        },
        {
          title: 'ステータス',
          dataIndex: 'status',
          key: 'status',
          render: (element) =>
            element?.status === 1 ? '利用期間内' : '利用期間外',
        },
        {
          title: '利用回数',
          dataIndex: 'apply_number',
          key: 'apply_number',
          render: (element) => {
            return <div>{element?.iap_receipts_count || 0}</div>;
          },
        },
        {
          title: '備考',
          dataIndex: 'note',
          key: 'note',
          render: (element) => {
            return (
              <div className="w-38 break-all line-clamp-3 whitespace-pre-line">
                {element?.note}
              </div>
            );
          },
        },
        {
          title: 'アクション',
          dataIndex: 'action',
          key: 'action',
          className: 'w-32',
          render: getActions,
        },
      ] as Array<ColumnType<ICoupon>>,
    [getActions]
  );

  const fetchCouponsData = useCallback(
    (page: number, filter?: TFilterCoupon) => {
      const params = { page, ...filter };
      dispatch(
        getCouponsAction.request({ params }, (coupons) => {
          setCoupons(coupons);
        })
      );
    },
    [dispatch]
  );

  useEffect(() => {
    if (filter) {
      const query = removeEmpty(filter) as ParsedUrlQueryInput;
      router.push({ query }, undefined, {
        shallow: true,
      });
      fetchCouponsData(1, filter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter, fetchCouponsData]);

  const handleRedirectToAdd = () => {
    dispatch(setCallBackUrl(router.asPath));
    router.push('/coupon/create');
  };

  const handleChangePage = (page: number) => {
    fetchCouponsData(page, filter);
  };

  return (
    <div className="grid grid-cols-1 gap-2.5">
      <div className="flex flex-col gap-y-4">
        <div className="bg-alice-blue p-5 flex justify-between items-center rounded-md">
          <CouponFilter
            defaultFilter={filter}
            setFilter={setFilter}
            clearFilter={setDefaultFilter}
          />
        </div>
        <div className="self-end flex items-center gap-2">
          <Button
            onClick={handleRedirectToAdd}
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="新規登録"
            className="!font-normal"
          />
        </div>
      </div>
      <div className="flex flex-col items-center gap-y-4 justify-between">
        <div className="w-full flex justify-between items-center">
          <h2 className="text-lg font-bold text-dark-shade-of-gray">{`全${
            meta?.total || 0
          }件`}</h2>
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        </div>
        <Table<ICoupon> columns={columns} dataSource={data} />
        {meta?.total !== 0 && (
          <Pagination
            current={meta?.current_page || 0}
            total={meta?.last_page || 0}
            onChange={handleChangePage}
          />
        )}
      </div>
    </div>
  );
};

export default CouponListOrganism;
