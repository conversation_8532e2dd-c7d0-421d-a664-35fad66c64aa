import { SvgIconConstituentValues } from './SvgIconConstituentValues';

export default function CloseIcon(props: SvgIconConstituentValues) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12.571"
      height="12.571"
      viewBox="11.714 21.214 12.571 12.571"
      {...props}
    >
      <g data-name="Group 285">
        <path
          d="M12.343 33.157a.889.889 0 0 1 0-1.257l10.056-10.056a.889.889 0 1 1 1.257 1.257L13.6 33.157a.889.889 0 0 1-1.257 0Z"
          fill={props.fillcolor || '#fff'}
          fillRule="evenodd"
          data-name="menu-bar"
        />
        <path
          d="M12.343 21.843a.889.889 0 0 1 1.257 0L23.657 31.9a.889.889 0 1 1-1.257 1.257L12.343 23.1a.889.889 0 0 1 0-1.257Z"
          fill={props.fillcolor || '#fff'}
          fillRule="evenodd"
          data-name="menu-bar"
        />
      </g>
    </svg>
  );
}
