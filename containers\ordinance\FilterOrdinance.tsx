import { EIsActive } from '@/commons/enums';
import { Button, EButtonStyleType, Input } from '@/components';
import { Checkbox } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { useEffect, useState } from 'react';
import { SelectBigCategory } from '../select';
import { TFilterOrdinace, TFilterOrdinaceProps } from './ordinance.types';

const FilterOrdinance = ({
  defaultFilter,
  setFilter,
  clearFilter,
}: TFilterOrdinaceProps) => {
  const [filterOrdinance, setFilterOrdinance] = useState<TFilterOrdinace>();

  useEffect(() => {
    setFilterOrdinance(defaultFilter);
  }, [defaultFilter]);

  const handleChangeBigProjectName = (category_id: string) => {
    setFilterOrdinance((current) => ({ ...current, category_id }));
  };

  const handleChangeLegalName = (name: string) => {
    setFilterOrdinance((current) => ({ ...current, name }));
  };

  const changeValid = (is_actives: Array<CheckboxValueType>) => {
    setFilterOrdinance((current) => ({ ...current, is_actives }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterOrdinance);
  };

  return (
    <div className="w-full flex flex-col items-center justify-between gap-5">
      <div className="w-full grid grid-cols-2 gap-x-5">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">大項目名</p>
          <SelectBigCategory
            value={filterOrdinance?.category_id}
            onChange={handleChangeBigProjectName}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">法令名</p>
          <Input
            value={filterOrdinance?.name}
            size="large"
            classNames="w-full !rounded"
            onChange={handleChangeLegalName}
          />
        </div>
      </div>
      <div className="flex justify-between w-full items-start">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
          <p className="text-black-kj">有効・無効</p>
          <div className="flex flex-row items-center mt-3">
            <Checkbox.Group
              onChange={changeValid}
              value={filterOrdinance?.is_actives}
            >
              <Checkbox value={EIsActive.ENABLE}>有効</Checkbox>
              <Checkbox value={EIsActive.DISABLE}>無効</Checkbox>
            </Checkbox.Group>
          </div>
        </div>
        <div className="flex items-end flex-none gap-2.5">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="クリア"
            className="!font-normal"
            onClick={clearFilter}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="検索"
            className="!font-normal"
            onClick={handleUpdateFilter}
          />
        </div>
      </div>
    </div>
  );
};

export default FilterOrdinance;
