import { EIsCreating, EResponseCode, ETypeNotification } from '@/commons/enums';
import { Button, EButtonStyleType, Input, UploadFile } from '@/components';
import { postUploadDigitalTexts } from '@/services/apis';
import { showNotification } from '@/utils/functions';
import clsx from 'clsx';
import { useRouter } from 'next/router';
import { Fragment, useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';

const antIcon = <LoadingOutlined style={{ fontSize: 64 }} spin />;

const UploadImageDigitalText = () => {
  const router = useRouter();
  const [file, setFile] = useState<File>();
  const [messageError, setMessageError] = useState<string>();
  const [invalidFiles, setInvalidFiles] = useState<Array<string>>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const handleBack = () => {
    if (callBackUrl) {
      router.push(callBackUrl);
      return;
    }
    router.push('/digital-texts');
  };

  const handleUploadFile = (is_creating: EIsCreating) => async () => {
    if (!file) {
      return;
    }
    try {
      setIsLoading(true);
      const payload = { file_zip: file, is_creating };
      await postUploadDigitalTexts({ payload });
      showNotification(ETypeNotification.SUCCESS, ' 操作に成功しました。');
    } catch (error: any) {
      if (
        [EResponseCode.UNPROCESSABLE_ENTITY, EResponseCode.FORBIDDEN].includes(
          error?.status
        )
      ) {
        const messageError = error?.data?.message;
        const dataError = error?.data?.data;
        setMessageError(messageError);
        if (dataError) {
          const { invalid_files } = dataError;
          setInvalidFiles(invalid_files);
        }
      }
    }
    setIsLoading(false);
  };
  const handleError = () => {
    setMessageError('ファイルは不正です。');
  };
  const handleSuccess = (file: File) => {
    setMessageError(undefined);
    setInvalidFiles(undefined);
    setFile(file);
  };
  return (
    <Fragment>
      {isLoading && (
        <div className="fixed w-full h-full top-0 left-0 bg-gray-400 z-20 bg-opacity-30">
          <div className="w-full h-full flex items-center justify-center">
            <Spin indicator={antIcon} />
          </div>
        </div>
      )}
      <div className="grid grid-cols-1 gap-5">
        <div className="bg-alice-blue p-5 rounded-md">
          <h4 className="!mb-2 font-bold text-sm text-textGray">
            テキスト画像
          </h4>
          <div className="flex items-center  gap-5">
            <div className="flex items-center gap-5">
              <Input
                disabled
                value={file?.name}
                classNames="!w-60 !rounded"
                size="large"
              />
              <UploadFile
                accept=".zip,.7zip"
                type={['zip', '7zip']}
                setFile={handleSuccess}
                maxCount={1}
                onError={handleError}
              >
                <button
                  className={clsx(
                    'rounded bg-primary bg-opacity-10',
                    '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold'
                  )}
                >
                  フォルダ選択
                </button>
              </UploadFile>
            </div>
            <Button
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="登録"
              className="!font-normal"
              disabled={!file}
              onClick={handleUploadFile(1)}
            />
            <Button
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="更新"
              className="!font-normal"
              onClick={handleUploadFile(0)}
            />
          </div>
        </div>
        <div className="relative">
          <div className="absolute right-0 top-0">
            <Button
              styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
              title="戻る"
              onClick={handleBack}
            />
          </div>

          <div>
            <p className="text-sm font-bold text-danger">{messageError}</p>
            {invalidFiles?.map((item) => (
              <p key={Math.random()} className="text-sm font-bold text-danger">
                {item}
              </p>
            ))}
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default UploadImageDigitalText;
