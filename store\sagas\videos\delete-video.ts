import { TDeleteVideoResponse, deleteVideo } from '@/services/apis';
import { deleteVideoAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* deleteVideoSaga(
  action: ActionType<typeof deleteVideoAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(deleteVideo, materials);
    const deleteVideoResponse = response as TDeleteVideoResponse;
    yield put(deleteVideoAction.success(deleteVideoResponse));
    successCallback?.(deleteVideoResponse);
  } catch (err) {
    yield put(deleteVideoAction.failure(err));
    failedCallback?.(err);
  }
}
