import { ERoleId, ETypeNotification } from '@/commons/enums';
import { EXCEPTION, MODAL_MESSAGE } from '@/commons/messages-enum';
import { Button, ConfirmModal, EButtonStyleType } from '@/components';
import { ExamForm, TExamForm } from '@/containers';
import { helpers, postExam } from '@/services/apis';
import { postExamAction } from '@/store/actions';
import { RootState } from '@/store/configureStore';
import { useHistory } from '@/utils/contexts';
import { showNotification } from '@/utils/functions';
import { Form } from 'antd';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { Fragment, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const CreateExamsPage = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { getMeResponse: me } = useSelector((state: RootState) => state.auth);
  const [isShowModalSubmit, setIsShowModalSubmit] = useState<boolean>(false);
  const historyManager = useHistory();
  const canGoBack = historyManager.canGoBack();

  const [form] = Form.useForm<TExamForm>();

  const handleClickToBack = () => {
    if (canGoBack) {
      router.back();
      return;
    }
    router.push('/exams');
  };

  useEffect(() => {
    if (me?.data.role_id === ERoleId.TEACHER) {
      router.push('/403');
    }
  }, [me?.data.role_id, router]);

  const handleSubmit = (payload: TExamForm) => {
    dispatch(
      postExamAction.request({ payload }, () => {
        showNotification(ETypeNotification?.SUCCESS, EXCEPTION.ACTION_SUCCESS);
        setTimeout(() => {
          handleClickToBack();
        });
      })
    );
  };

  const handleClickSubmit = () => {
    if (!form) {
      return;
    }
    form.submit();
  };

  const showModalSubmit = async () => {
    try {
      await form.validateFields();
      setIsShowModalSubmit(true);
    } catch (error) {}
  };

  const onCloseModalSubmit = () => {
    setIsShowModalSubmit(false);
  };

  return (
    <Fragment>
      <ConfirmModal
        visible={isShowModalSubmit}
        content={MODAL_MESSAGE.CONFIRM_CREATE}
        onClose={onCloseModalSubmit}
        onConfirm={handleClickSubmit}
      />
      <div className="mx-12 my-4 grid grid-cols-1 gap-5">
        <div className="flex justify-between items-center">
          <h1 className="text-lg font-bold text-black-kj">
            模擬試験名新規登録
          </h1>
        </div>
        <div className="w-full px-5 pt-7 pb-1 bg-new-white">
          <ExamForm formInstance={form} handleSubmit={handleSubmit} />
        </div>
        <div className="w-full flex items-center justify-end gap-5">
          <Button
            styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
            title="戻る"
            className="!font-normal"
            onClick={handleClickToBack}
          />
          <Button
            styleType={EButtonStyleType.PRIMARY_VARIANT}
            title="登録"
            className="!font-normal"
            onClick={showModalSubmit}
          />
        </div>
      </div>
    </Fragment>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default CreateExamsPage;
