import { Button as AntdButton, ConfigProvider } from 'antd';
import clsx from 'clsx';
import styles from './styles.module.less';
import { TButtonProps } from './Button.types';
import { Fragment, useMemo } from 'react';

const Button = ({
  type,
  title,
  size,
  styleType,
  className,
  icon,
  htmlType,
  disabled,
  onClick,
}: TButtonProps) => {
  const content = useMemo(() => {
    if (icon) {
      return (
        <Fragment>
          <div className="flex-none w-4"></div>
          <span className="flex-grow">{title}</span>
          <div className="flex-none w-4">i</div>
        </Fragment>
      );
    }
    return <span className="flex-grow font-inherit">{title}</span>;
  }, [icon, title]);

  return (
    <ConfigProvider autoInsertSpaceInButton={false}>
      <div className={clsx(styles.button, 'Button')}>
        <AntdButton
          size={size}
          type={type}
          htmlType={htmlType}
          className={clsx(styleType, className)}
          onClick={onClick}
          disabled={disabled}
        >
          {content}
        </AntdButton>
      </div>
    </ConfigProvider>
  );
};

export default Button;
