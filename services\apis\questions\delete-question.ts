import { ENeedCheckWithSetting } from '@/commons/enums';
import { TListData, TQuestion } from '@/commons/types';
import ApiService from '@/services/apis';

export type TDeleteQuestionParams = {
  need_check_with_setting: ENeedCheckWithSetting;
};

export type TDeleteQuestionPaths = {
  exam_id: string;
  questionId: string;
};

export type TDeleteQuestionMaterials = {
  params?: TDeleteQuestionParams;
  paths: TDeleteQuestionPaths;
};

export type TDeleteQuestionResponse = unknown;

export const deleteQuestion = async (
  materials: TDeleteQuestionMaterials
): Promise<TDeleteQuestionResponse> => {
  const { params, paths } = materials;
  const response = await ApiService.delete(
    `/exams/${paths.exam_id}/questions/${paths.questionId}`,
    {
      params,
    }
  );
  return response.data;
};
