import clsx from 'clsx';
import Image from 'next/image';
import { TImage } from './Image.types';

const CustomImage = (props: TImage) => {
  const {
    alt = 'alt-image',
    src,
    classnames,
    style,
    objectFit = 'contain',
    blurDataURL,
    placeholder,
  } = props;
  return (
    <div
      className={clsx('relative overflow-hidden', classnames)}
      style={style}
      {...props}
    >
      <Image
        src={src}
        alt={alt}
        layout="fill"
        style={{ objectFit }}
        blurDataURL={blurDataURL}
        placeholder={placeholder}
      />
    </div>
  );
};

export default CustomImage;
