import { Button, EButtonStyleType } from '@/components';
import { KnowledgeBoardDetail } from '@/containers';
import { RootState } from '@/store/configureStore';
import { useRouter } from 'next/router';
import React, { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';

const KnowledgeBoardsEditPage = () => {
  const router = useRouter();
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const onBack = useCallback(() => {
    if (callBackUrl) {
      router.push(callBackUrl);
    } else router.push('/knowledge-boards');
  }, [callBackUrl, router]);
  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">知識板編集</div>
      <KnowledgeBoardDetail />
      <div className="flex justify-center mb-3">
        <Button
          onClick={onBack}
          size={'small'}
          styleType={EButtonStyleType.OUTLINE_PRIMARY}
          title={'戻る'}
        />
      </div>
    </div>
  );
};

export default KnowledgeBoardsEditPage;
