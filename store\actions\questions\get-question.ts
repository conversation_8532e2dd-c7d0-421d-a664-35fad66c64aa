import { TGetQuestionMaterials, TGetQuestionResponse } from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EGetQuestionAction {
  GET_QUESTION = 'GET_QUESTION',
  GET_QUESTION_REQUEST = 'GET_QUESTION_REQUEST',
  GET_QUESTION_SUCCESS = 'GET_QUESTION_SUCCESS',
  GET_QUESTION_FAILED = 'GET_QUESTION_FAILED',
}

// TYPES

export type TGetQuestionRequest = {
  type: EGetQuestionAction.GET_QUESTION_REQUEST;
  payload: {
    materials: TGetQuestionMaterials;
    successCallback?: (response: TGetQuestionResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TGetQuestionSuccess = {
  type: EGetQuestionAction.GET_QUESTION_SUCCESS;
  payload: { response?: TGetQuestionResponse };
};

export type TGetQuestionFailed = {
  type: EGetQuestionAction.GET_QUESTION_FAILED;
};

export const getQuestionAction = {
  request: createActionCreator(
    EGetQuestionAction.GET_QUESTION_REQUEST,
    (resolve) =>
      (
        materials: TGetQuestionMaterials,
        successCallback?: (response: TGetQuestionResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TGetQuestionRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EGetQuestionAction.GET_QUESTION_SUCCESS,
    (resolve) =>
      (response?: TGetQuestionResponse): TGetQuestionSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EGetQuestionAction.GET_QUESTION_FAILED,
    (resolve) =>
      (error: unknown): TGetQuestionFailed =>
        resolve({ error })
  ),
};
