import { TGetCommonMaintainResponse, getCommonMaintain } from '@/services/apis';
import { useLayoutEffect, useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';

const antIcon = <LoadingOutlined style={{ fontSize: 64 }} spin />;

const MaintainContent = () => {
  const [maintainData, setMaintainData] =
    useState<TGetCommonMaintainResponse>();

  useLayoutEffect(() => {
    const fetchData = async () => {
      const data = await getCommonMaintain({});
      setMaintainData(data);
    };
    fetchData();
  }, []);
  const content = maintainData?.data.content;

  if (content) {
    return (
      <div
        className="w-full h-full mx-auto min-w-181 min-h-40 html-render"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  }
  return (
    <div className="w-full h-full mx-auto min-w-181 min-h-40 flex items-center justify-center">
      <Spin indicator={antIcon} />
    </div>
  );
};

export default MaintainContent;
