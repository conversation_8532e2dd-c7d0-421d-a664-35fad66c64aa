import { TListData, TNotification } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetNotificationsParams = {
  page: number;
  status?: number;
  is_active?: number;
};

export type TGetNotificationsMaterials = {
  params?: TGetNotificationsParams;
};

export type TGetNotificationsResponse = TListData<TNotification>;

export const getNotifications = async (
  materials: TGetNotificationsMaterials
): Promise<TGetNotificationsResponse> => {
  const { params } = materials;
  const response = await ApiService.get('/notifications', { params });
  return response.data;
};
