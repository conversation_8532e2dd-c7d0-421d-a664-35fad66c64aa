import { Button, EButtonStyleType } from '@/components';
import { SelectBigCategory, SelectLaws } from '../select';
import { TOptionCategory, TOptionSub } from '@/commons/types';
import { useEffect, useState } from 'react';

const SearchLaw = ({
  value,
  onChangeLaw,
  onClose,
  bottomTitle,
  bottomPlace,
}: {
  value?: TOptionSub;
  onClose?: () => void;
  onChangeLaw: (value: TOptionSub) => void;
  bottomTitle?: string;
  bottomPlace?: string;
}) => {
  const [categoryId, setCategoryId] = useState<string>();
  const [law, setLaw] = useState<TOptionCategory>();
  useEffect(() => {
    if (!value) {
      setCategoryId(undefined);
      setLaw(undefined);
    } else {
      setLaw(value);
      setCategoryId(value.category_id);
    }
  }, [value]);

  const selectLaw = () => {
    if (law && categoryId) {
      onChangeLaw({ ...law, category_id: categoryId });
    }
  };
  const selectCategory = (id?: string) => {
    setCategoryId(id);
    setLaw(undefined);
  };

  return (
    <div className="grid grid-cols-1 gap-5 w-125 h-56">
      <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
        <p className="text-black-kj font-bold">大項目名</p>
        <SelectBigCategory onChange={selectCategory} value={categoryId} />
      </div>
      <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
        <p className="text-black-kj font-bold">
          {bottomTitle || ' 法令名'}
        </p>
        <SelectLaws
          category_id={categoryId}
          value={law?.id || ''}
          onChange={setLaw}
          className="w-full"
          placeholder={bottomPlace}
        />
      </div>
      <div className="flex justify-center gap-5">
        <Button
          title="戻る"
          className="self-center"
          styleType={EButtonStyleType.OUTLINE_PRIMARY}
          onClick={onClose}
        />
        <Button
          disabled={!law}
          title="追加"
          className="self-center"
          styleType={EButtonStyleType.PRIMARY_VARIANT}
          onClick={selectLaw}
        />
      </div>
    </div>
  );
};

export default SearchLaw;
