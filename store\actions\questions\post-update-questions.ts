import {
  TPostUpdateQuestionsMaterials,
  TPostUpdateQuestionsResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPostUpdateQuestionsAction {
  POST_UPDATE_QUESTIONS = 'POST_UPDATE_QUESTIONS',
  POST_UPDATE_QUESTIONS_REQUEST = 'POST_UPDATE_QUESTIONS_REQUEST',
  POST_UPDATE_QUESTIONS_SUCCESS = 'POST_UPDATE_QUESTIONS_SUCCESS',
  POST_UPDATE_QUESTIONS_FAILED = 'POST_UPDATE_QUESTIONS_FAILED',
}

// TYPES

export type TPostUpdateQuestionsRequest = {
  type: EPostUpdateQuestionsAction.POST_UPDATE_QUESTIONS_REQUEST;
  payload: {
    materials: TPostUpdateQuestionsMaterials;
    successCallback?: (response: TPostUpdateQuestionsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPostUpdateQuestionsSuccess = {
  type: EPostUpdateQuestionsAction.POST_UPDATE_QUESTIONS_SUCCESS;
  payload: { response?: TPostUpdateQuestionsResponse };
};

export type TPostUpdateQuestionsFailed = {
  type: EPostUpdateQuestionsAction.POST_UPDATE_QUESTIONS_FAILED;
};

export const postUpdateQuestionsAction = {
  request: createActionCreator(
    EPostUpdateQuestionsAction.POST_UPDATE_QUESTIONS_REQUEST,
    (resolve) =>
      (
        materials: TPostUpdateQuestionsMaterials,
        successCallback?: (response: TPostUpdateQuestionsResponse) => void,
        failedCallback?: (err: unknown) => void
      ): TPostUpdateQuestionsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPostUpdateQuestionsAction.POST_UPDATE_QUESTIONS_SUCCESS,
    (resolve) =>
      (response?: TPostUpdateQuestionsResponse): TPostUpdateQuestionsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPostUpdateQuestionsAction.POST_UPDATE_QUESTIONS_FAILED,
    (resolve) =>
      (error: unknown): TPostUpdateQuestionsFailed =>
        resolve({ error })
  ),
};
