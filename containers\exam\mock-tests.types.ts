import { EFolderUpload, EIsActive } from '@/commons/enums';
import { TExamSetting } from '@/commons/types';
import { FormInstance } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { NamePath } from 'antd/lib/form/interface';

export type TMockTestsFilter = {
  category_id?: string;
  show_order?: number;
  question_year?: string;
  id?: number;
  title?: string;
  content?: string;
  is_actives?: Array<CheckboxValueType>;
  types?: Array<CheckboxValueType>;
};

export type TExamsFilter = {
  name?: string;
  is_actives?: Array<CheckboxValueType>;
};

export type TExamsFilterProps = {
  defaultFilter?: TExamsFilter;
  setFilter?: (value?: TMockTestsFilter) => void;
  clearFilter?: () => void;
};

export type TFilterMockTestsProps = {
  defaultFilter?: TMockTestsFilter;
  setFilter?: (value?: TMockTestsFilter) => void;
  clearFilter?: () => void;
};

export type TExamForm = {
  name: string;
  is_active: EIsActive;
  show_order: number;
};

export type TExamFormProps = {
  formInstance: FormInstance<TExamForm>;
  handleSubmit: (values: TExamForm) => void;
};

export type TExamSettingFormProps = {
  formInstance: FormInstance<TExamSetting>;
  handleSubmit?: (values: TExamSetting) => void;
  disable?: boolean;
};

export type TQuestionFormProps = {
  formInstance: FormInstance<any>;
  handleSubmit?: (values: any) => void;
  disable?: boolean;
  invalidTab?: { value: string };
  isQuestionsEdit?: boolean;
};

export type TNumberQuestionProps = {
  index: number;
  remove: (index: number | number[]) => void;
};

export type TPassPointProps = {
  index: number;
  remove: (index: number | number[]) => void;
};

export type TFormUploadItemProps = {
  value?: string;
  width?: string;
  onChange?: (value?: string) => void;
  accept?: string;
  type?: string[];
  disable?: boolean;
  name?: NamePath;
  label?: string;
  folder: EFolderUpload;
};

export type TSelectItemFormProps<T> = {
  value?: T;
  disable?: boolean;
  onChange?: (value?: T) => void;
  bottomPlace?: string;
  bottomTitle?: string;
  className?: string;
  role?: any;
};
