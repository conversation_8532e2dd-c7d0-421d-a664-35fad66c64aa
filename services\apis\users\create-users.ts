import ApiService from '@/services/apis';

export type TPostCreateUsers = {
  username: string;
  username_kana: string;
  role_id: number;
  video_plan: number;
  plan_start_date: string;
  plan_end_date: string;
  email: string;
  login_id: string;
  password: string;
  is_active: number;
};

export type TCreateUsersResponse = unknown;

export const postCreateUser = async (
  payload: TPostCreateUsers
): Promise<TCreateUsersResponse> => {
  const response = await ApiService.post('/users', payload);
  return response.data;
};
