import React, { useEffect, useMemo, useRef, useState } from 'react';
import JoditEditor from 'jodit-react';

const EditorBox = (props: any) => {
  const {
    placeholder,
    className,
    buttons,
    onChangeText,
    defaultContent = '',
  } = props;
  const editor = useRef(null);
  const [content, setContent] = useState('');

  useEffect(() => {
    setContent(defaultContent);
  }, [defaultContent]);

  const config = useMemo(
    () => ({
      defaultMode: 2,
      readonly: false,
      height: '300px',
      width: '100%',
      buttons: [...buttons, 'preview'],
      uploader: { insertImageAsBase64URI: true },
      toolbarAdaptive: false,
      style: {
        background: '#ffffff',
        color: '#000000',
      },
      placeholder: placeholder || '',
      cleanHTML: {
        fillEmptyParagraph: false,
        removeEmptyElements: true,
      },
      limitChars: 65535,
    }),
    []
  );

  return (
    <div className={className}>
      <JoditEditor
        className="text-editor"
        ref={editor}
        value={content}
        config={config}
        onBlur={(newContent) => {
          setContent(newContent);
          onChangeText(newContent);
        }}
        onChange={(newContent) => {
          onChangeText(newContent);
          setContent(newContent);
        }}
      />
    </div>
  );
};

export default EditorBox;
