import {
  TPostUpdatePracticeQuestionsMaterials,
  TPostUpdatePracticeQuestionsResponse,
} from '@/services/apis';
import { createActionCreator } from 'deox';

// CONSTANTS

export enum EPostUpdatePracticeQuestionsAction {
  POST_UPDATE_PRACTICE_QUESTIONS = 'POST_UPDATE_PRACTICE_QUESTIONS',
  POST_UPDATE_PRACTICE_QUESTIONS_REQUEST = 'POST_UPDATE_PRACTICE_QUESTIONS_REQUEST',
  POST_UPDATE_PRACTICE_QUESTIONS_SUCCESS = 'POST_UPDATE_PRACTICE_QUESTIONS_SUCCESS',
  POST_UPDATE_PRACTICE_QUESTIONS_FAILED = 'POST_UPDATE_PRACTICE_QUESTIONS_FAILED',
}

// TYPES

export type TPostUpdatePracticeQuestionsRequest = {
  type: EPostUpdatePracticeQuestionsAction.POST_UPDATE_PRACTICE_QUESTIONS_REQUEST;
  payload: {
    materials: TPostUpdatePracticeQuestionsMaterials;
    successCallback?: (response: TPostUpdatePracticeQuestionsResponse) => void;
    failedCallback?: (err: unknown) => void;
  };
};

export type TPostUpdatePracticeQuestionsSuccess = {
  type: EPostUpdatePracticeQuestionsAction.POST_UPDATE_PRACTICE_QUESTIONS_SUCCESS;
  payload: { response?: TPostUpdatePracticeQuestionsResponse };
};

export type TPostUpdatePracticeQuestionsFailed = {
  type: EPostUpdatePracticeQuestionsAction.POST_UPDATE_PRACTICE_QUESTIONS_FAILED;
};

export const postUpdatePracticeQuestionsAction = {
  request: createActionCreator(
    EPostUpdatePracticeQuestionsAction.POST_UPDATE_PRACTICE_QUESTIONS_REQUEST,
    (resolve) =>
      (
        materials: TPostUpdatePracticeQuestionsMaterials,
        successCallback?: (
          response: TPostUpdatePracticeQuestionsResponse
        ) => void,
        failedCallback?: (err: unknown) => void
      ): TPostUpdatePracticeQuestionsRequest =>
        resolve({ materials, successCallback, failedCallback })
  ),
  success: createActionCreator(
    EPostUpdatePracticeQuestionsAction.POST_UPDATE_PRACTICE_QUESTIONS_SUCCESS,
    (resolve) =>
      (
        response?: TPostUpdatePracticeQuestionsResponse
      ): TPostUpdatePracticeQuestionsSuccess =>
        resolve({ response })
  ),
  failure: createActionCreator(
    EPostUpdatePracticeQuestionsAction.POST_UPDATE_PRACTICE_QUESTIONS_FAILED,
    (resolve) =>
      (error: unknown): TPostUpdatePracticeQuestionsFailed =>
        resolve({ error })
  ),
};
