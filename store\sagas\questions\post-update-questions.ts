import { TGetExamSettingResponse, postUpdateQuestions } from '@/services/apis';
import { postUpdateQuestionsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* postUpdateQuestionsSaga(
  action: ActionType<typeof postUpdateQuestionsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(postUpdateQuestions, materials);
    const postUpdateQuestionsResponse = response as TGetExamSettingResponse;
    yield put(postUpdateQuestionsAction.success(postUpdateQuestionsResponse));
    successCallback?.(postUpdateQuestionsResponse);
  } catch (err) {
    yield put(postUpdateQuestionsAction.failure(err));
    failedCallback?.(err);
  }
}
