import { TQuestion } from '@/commons/types';
import ApiService from '@/services/apis';

export type TGetPracticeQuestionPath = {
  id: string;
};

export type TGetPracticeQuestionMaterials = {
  paths: TGetPracticeQuestionPath;
};

export type TGetPracticeQuestionResponse = { data: TQuestion };

export const getPracticeQuestion = async (
  materials: TGetPracticeQuestionMaterials
): Promise<TGetPracticeQuestionResponse> => {
  const { paths } = materials;
  const response = await ApiService.get(`/practice-questions/${paths.id}`);
  return response.data;
};
