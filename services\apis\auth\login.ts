import ApiService from '@/services/apis';

export type TPostLoginPaths = {};
export type TPostLoginParams = unknown;

export type TPayloadLoginMaterials = {
  login_id: string;
  password: string;
  setDisable: any;
};
export type TPostLoginMaterials = {
  payload: TPayloadLoginMaterials;
};

export type TLoginData = {
  access_token: string;
  expires_at: string;
};
export type TPostLoginResponse = {
  data: TLoginData;
};

export const postLogin = async (
  materials: TPostLoginMaterials
): Promise<TPostLoginResponse> => {
  const { payload } = materials;
  const response = await ApiService.post('/auth/login', payload);
  return response.data;
};
