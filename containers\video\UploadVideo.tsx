import { Input, UploadFile } from '@/components';
import clsx from 'clsx';
import React, {
  MouseEvent,
  useCallback,
  useState,
} from 'react';
import { uploadVideo } from '@/services/apis';
import { Spin } from 'antd';
import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined';
import { TFormUploadVideoProps } from './video.types';

const antIcon = <LoadingOutlined style={{ fontSize: 64 }} spin />;

const FormUploadVideo = ({
  value,
  onChange,
  onErrorType,
  onErrorSize,
  accept,
  type,
  maxSize,
}: TFormUploadVideoProps) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSuccess = (file: File) => {
    setIsLoading(true);
    uploadVideo(file)
      .then((res) => {
        const url = res.name;
        onChange?.(url);
        setIsLoading(false);
      })
      .catch(() => {
        setIsLoading(false);
      });
  };

  const handleRemoveImage = useCallback(
    (event: MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      onChange?.(undefined);
    },
    [onChange]
  );

  const handleError = () => {};

  return (
    <>
      {isLoading && (
        <div className="fixed w-full h-full top-0 left-0 bg-gray-400 z-20 bg-opacity-30">
          <div className="w-full h-full flex items-center justify-center">
            <Spin indicator={antIcon} />
          </div>
        </div>
      )}
      <div className="flex items-center gap-5 w-full">
        <Input
          disabled
          value={!isLoading ? value : ''}
          classNames="!flex-grow !rounded"
          size="large"
        />
        <UploadFile
          accept={accept}
          type={type}
          maxSize={maxSize}
          setFile={handleSuccess}
          maxCount={1}
          onError={handleError}
          disabled={isLoading}
          onErrorSize={onErrorSize}
          onErrorType={onErrorType}
        >
          <div
            className={clsx(
              '!text-black-kj py-1.5 px-6 break-keep h-9 font-bold rounded',
              isLoading ? 'bg-[#f5f5f5]' : 'bg-primary bg-opacity-10'
            )}
          >
            ファイル選択
          </div>
        </UploadFile>
      </div>
    </>
  );
};

export default FormUploadVideo;
