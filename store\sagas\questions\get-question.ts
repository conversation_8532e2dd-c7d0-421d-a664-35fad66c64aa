import { TGetQuestionResponse, getQuestion } from '@/services/apis';
import { getQuestionAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* getQuestionSaga(
  action: ActionType<typeof getQuestionAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(getQuestion, materials);
    const getQuestionResponse = response as TGetQuestionResponse;
    yield put(getQuestionAction.success(getQuestionResponse));
    successCallback?.(getQuestionResponse);
  } catch (err) {
    yield put(getQuestionAction.failure(err));
    failedCallback?.(err);
  }
}
