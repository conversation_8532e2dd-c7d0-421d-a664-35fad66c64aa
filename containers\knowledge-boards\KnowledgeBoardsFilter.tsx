import {
  EInquiryIsPublic,
  EIsActive,
  EQuestionType,
  ERoleId,
} from '@/commons/enums';
import { Button, EButtonStyleType, Input, InputNumber } from '@/components';
import { Checkbox } from 'antd';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { useEffect, useState } from 'react';
import { SelectBigCategory, SelectSubCategory } from '../select';
import {
  TKnowledgeBoardsFilter,
  TKnowledgeBoardsFilterProps,
} from '@/commons/types';

const KnowledgeBoardsFilter = ({
  defaultFilter,
  setFilter,
  clearFilter,
  me,
}: TKnowledgeBoardsFilterProps) => {
  const [filterObject, setFilterObject] = useState<TKnowledgeBoardsFilter>();

  useEffect(() => {
    setFilterObject(defaultFilter);
  }, [defaultFilter]);

  const handleChangeCategoryId = (category_id: string) => {
    setFilterObject((current) => ({
      ...current,
      category_id: category_id === '' ? undefined : category_id,
    }));
  };

  const handleChangeQuestionId = (question_id: number) => {
    setFilterObject((current) => ({
      ...current,
      question_id,
    }));
  };

  const handlechangeSubCategoryId = (sub_category_id: string) => {
    setFilterObject((current) => ({
      ...current,
      sub_category_id: sub_category_id === '' ? undefined : sub_category_id,
    }));
  };

  const handlechangeType = (types: Array<CheckboxValueType>) => {
    setFilterObject((current) => ({ ...current, types }));
  };

  const handlechangeIsPublic = (isPublic: Array<CheckboxValueType>) => {
    setFilterObject((current) => ({ ...current, is_publics: isPublic }));
  };

  const handlechangeIsTeacherReply = (
    teacher_reply: Array<CheckboxValueType>
  ) => {
    setFilterObject((current) => ({
      ...current,
      teacher_reply,
    }));
  };

  const handlechangeIsAdminReply = (admin_reply: Array<CheckboxValueType>) => {
    setFilterObject((current) => ({
      ...current,
      admin_reply,
    }));
  };

  const handleUpdateFilter = () => {
    setFilter?.(filterObject);
  };

  const handleClearFilter = () => {
    clearFilter?.();
  };

  return (
    <div className="grid grid-cols-1 gap-6 w-full">
      <div className="grid grid-cols-2 gap-x-5 gap-y-4 w-full">
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">大項目名</p>
          <SelectBigCategory
            value={filterObject?.category_id}
            onChange={handleChangeCategoryId}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">小項目名</p>
          <SelectSubCategory
            onChange={handlechangeSubCategoryId}
            value={filterObject?.sub_category_id}
            category_id={filterObject?.category_id}
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1">
          <p className="text-black-kj font-bold">問題ID</p>
          <InputNumber
            value={filterObject?.question_id}
            size="large"
            classNames="!w-full !rounded"
            onChange={handleChangeQuestionId}
            hideControls
            stringMode
          />
        </div>
        <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
          <p className="text-black-kj">タイプ</p>
          <div className="flex flex-row items-center">
            <Checkbox.Group
              onChange={handlechangeType}
              value={filterObject?.types}
            >
              <Checkbox value={EQuestionType.EXERCISE}>練習問題</Checkbox>
              <Checkbox value={EQuestionType.PRACTICE}>実践問題</Checkbox>
              <Checkbox value={EQuestionType.EXAM}>模擬試験</Checkbox>
            </Checkbox.Group>
          </div>
        </div>
        <div className="col-span-1 grid grid-cols-3">
          <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj">講師返信</p>
            <div className="flex flex-row items-center">
              <Checkbox.Group
                onChange={handlechangeIsTeacherReply}
                value={filterObject?.teacher_reply}
              >
                <Checkbox value={1}>未返信</Checkbox>
              </Checkbox.Group>
            </div>
          </div>
          {me && me?.data.role_id === ERoleId.ADMIN && (
            <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
              <p className="text-black-kj">管理者返信</p>
              <div className="flex flex-row items-center">
                <Checkbox.Group
                  onChange={handlechangeIsAdminReply}
                  value={filterObject?.admin_reply}
                >
                  <Checkbox value={1}>未返信</Checkbox>
                </Checkbox.Group>
              </div>
            </div>
          )}
          <div className="grid grid-cols-1 gap-2 !text-sm col-span-1 !font-bold">
            <p className="text-black-kj">公開・非公開</p>
            <div className="flex flex-row items-center">
              <Checkbox.Group
                onChange={handlechangeIsPublic}
                value={filterObject?.is_publics}
              >
                <Checkbox value={EInquiryIsPublic.PUBLIC}>公開</Checkbox>
                <Checkbox value={EInquiryIsPublic.PRIVATE}>非公開</Checkbox>
              </Checkbox.Group>
            </div>
          </div>
        </div>
        <div className="col-span-1 grid">
          <div className="flex items-center justify-end gap-2.5">
            <Button
              styleType={EButtonStyleType.OUTLINE_GREEN_BLUE}
              title="クリア"
              className="!font-normal"
              onClick={handleClearFilter}
            />
            <Button
              styleType={EButtonStyleType.PRIMARY_VARIANT}
              title="検索"
              className="!font-normal"
              onClick={handleUpdateFilter}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBoardsFilter;
