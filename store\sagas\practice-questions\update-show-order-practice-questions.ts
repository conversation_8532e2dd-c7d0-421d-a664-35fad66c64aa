import { postUpdatePracticeQuestions } from '@/services/apis';
import { postUpdatePracticeQuestionsAction } from '@/store/actions';
import { ActionType } from 'deox';
import { call, put } from 'redux-saga/effects';

export function* postUpdatePracticeQuestionsSaga(
  action: ActionType<typeof postUpdatePracticeQuestionsAction.request>
): Generator {
  const { materials, successCallback, failedCallback } = action.payload;
  try {
    const response = yield call(postUpdatePracticeQuestions, materials);
    const postUpdatePracticeQuestionsResponse = response as unknown;
    yield put(postUpdatePracticeQuestionsAction.success(postUpdatePracticeQuestionsResponse));
    successCallback?.(postUpdatePracticeQuestionsResponse);
  } catch (err) {
    yield put(postUpdatePracticeQuestionsAction.failure(err));
    failedCallback?.(err);
  }
}
