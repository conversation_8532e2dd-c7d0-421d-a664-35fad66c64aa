import { Button, EButtonStyleType, InputNumber } from '@/components';
import { Form } from 'antd';
import { TNumberQuestionProps } from './mock-tests.types';
import { SelectBigCategory } from '../select';
import { useMemo } from 'react';
import { Rule } from 'antd/lib/form';
import validate from '@/utils/validate';
import { validationMessage } from '@/commons/messages-enum';

const { checkMaxLength, hasWhiteSpace } = validate;

const PassPoint = ({ index, remove }: TNumberQuestionProps) => {
  const rulesRequireCorrect = useMemo(() => {
    const checkLength = () => checkMaxLength('必須正解数', 2);
    const checkSpave = () => hasWhiteSpace('必須正解数');
    return [
      {
        required: true,
        message: validationMessage.required('必須正解数'),
      },
      checkLength,
      checkSpave,
    ] as Array<Rule>;
  }, []);

  const rulesCategory = useMemo(() => {
    return [
      {
        required: true,
        message: validationMessage.required('大項目名'),
      },
    ] as Array<Rule>;
  }, []);

  return (
    <div className="flex w-full items-end gap-3">
      <div className="flex-grow grid grid-cols-2 gap-5">
        <div className="col-span-1 grid-cols-1 grid gap-2 -mb-5">
          <p className="text-black-kj font-bold">大項目名</p>
          <Form.Item
            name={[index, 'category']}
            className="!mb-5"
            rules={rulesCategory}
          >
            <SelectBigCategory placeholder={'選択してください'} />
          </Form.Item>
        </div>
        <div className="col-span-1 grid-cols-1 grid gap-2 -mb-5">
          <p className="text-black-kj font-bold">必須正解数</p>
          <Form.Item
            name={[index, 'require_correct_amount']}
            className="!mb-5"
            rules={rulesRequireCorrect}
          >
            <InputNumber
              classNames="!w-full !rounded !text-sm"
              size="large"
              maxLength={2}
              hideControls
              min={0}
            />
          </Form.Item>
        </div>
      </div>
      <Button
        title="- 行を削除"
        className="!font-normal flex-none mb-1"
        styleType={EButtonStyleType.DANGER}
        onClick={() => remove(index)}
      />
    </div>
  );
};

export default PassPoint;
