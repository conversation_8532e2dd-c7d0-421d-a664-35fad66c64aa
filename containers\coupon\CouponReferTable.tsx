import { TCouponPlan } from '@/commons/types';
import clsx from 'clsx';
import { useMemo } from 'react';

type TTableRefer = {
  id: number;
  title: string;
  productId: string;
};

type TVoucherReferTableProps = {
  couponPlans: Array<TCouponPlan>;
};

const VoucherReferTable = ({ couponPlans }: TVoucherReferTableProps) => {
  const headRefer = useMemo(() => {
    return [
      { title: 'ID', column: 'id', className: 'w-12' },
      { title: 'プラン名', column: 'title', className: 'w-60' },
      { title: 'product_id', column: 'productId' },
    ];
  }, []);
  const tableRefer = useMemo(() => {
    return couponPlans.map(({ id, plan, key, plan_key }) => {
      const hasVideo = plan_key.includes('video');
      const title = `通常購入${plan?.days || 0}日${
        hasVideo ? '＋対策動画' : ''
      }`;
      const productId = key;
      return { id, title, productId };
    });
  }, [couponPlans]) as Array<TTableRefer>;

  const getFieldsValue = (
    item: TTableRefer,
    key: 'id' | 'title' | 'productId'
  ) => {
    if (item.hasOwnProperty(key)) {
      return item[key];
    }
    return null;
  };
  return (
    <table className="w-full border-gray-300 border-2">
      <thead className="border-b border-black border-opacity-10">
        <tr className="divide-x-2">
          {headRefer.map(({ column, title, className }) => (
            <th
              className={clsx('text-left px-2.5 bg-[#ADD8E6]', className)}
              key={column}
            >
              {title}
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="divide-y-2">
        {tableRefer.map((item) => (
          <tr key={item.id} className="divide-x-2">
            {headRefer.map(({ column, className }) => (
              <td className={clsx('text-left px-2.5', className)} key={column}>
                <div className={clsx(className, 'truncate')}>
                  {getFieldsValue(item, column as any)}
                </div>
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default VoucherReferTable;
