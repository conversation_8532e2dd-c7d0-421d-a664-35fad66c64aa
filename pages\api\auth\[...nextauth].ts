import { postLogin } from '@/services/apis';
import NextAuth, { RequestInternal } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

export const authOptions = {
  providers: [
    CredentialsProvider({
      type: 'credentials',
      name: 'Credentials',
      id: 'username-password',
      credentials: {},
      async authorize(
        credentials: Record<string, string> | undefined,
        req: Pick<RequestInternal, 'body' | 'query' | 'headers' | 'method'>
      ) {
        if (!credentials) {
          return null;
        }
        try {
          const { username, password } = credentials;
          const payload = { username, password };
          // const data = await postLogin({ payload });

          // if (data) {
          //   return { id: username, ...data };
          // }
          return null;
        } catch (error: any) {
          throw new Error(error.data.message);
        }
      },
    }),
  ],
  callbacks: {
    jwt({ token, user }: any) {
      if (user) {
        return {
          ...token,
          ...user.data,
        };
      }
      return token;
    },

    async session({ session, token }: any) {
      session.user.accessToken = token.access_token;
      session.user.accessTokenExpiry = token.expires_at;
      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

export default NextAuth(authOptions);
