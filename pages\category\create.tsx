import { EPlacement, ERoleId, ETypeNotification } from '@/commons/enums';
import { TCategory } from '@/commons/types';
import { CategoryForm } from '@/containers';
import {
  TPostCreateCategory,
  postCreateCategory,
} from '@/services/apis/category';
import { convertErrorListToArray, showNotification } from '@/utils/functions';
import { useRouter } from 'next/router';
import React from 'react';
import { EXCEPTION } from '@/commons/messages-enum';
import { GetServerSideProps } from 'next';
import helpers from '@/services/helpers';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/configureStore';

const CategoryEditPage = () => {
  const routes = useRouter();
  const { callBackUrl } = useSelector((state: RootState) => state.history);

  const handleSubmit = async (values: TCategory) => {
    const payload: TPostCreateCategory = {
      name: values.name,
      is_active: values.is_active,
      show_order: values.show_order,
    };
    try {
      const data = await postCreateCategory(payload);
      showNotification(
        ETypeNotification.SUCCESS,
        EXCEPTION.ACTION_SUCCESS,
        EPlacement.BOTTOM_RIGHT
      );
      if (callBackUrl) {
        routes.push(callBackUrl);
        return;
      }
      routes.push('/category');
    } catch (error: any) {
      showNotification(
        ETypeNotification.ERROR,
        convertErrorListToArray(error) || EXCEPTION.ACTION_FAILURE,
        EPlacement.BOTTOM_RIGHT
      );
    }
  };

  return (
    <div className="mx-12 mt-4">
      <div className="text-lg font-bold mb-3">大項目新規登録</div>
      <CategoryForm handleSubmit={handleSubmit} />
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    const { cookies } = req;
    const me = helpers.getMeFromReq(cookies);
    if (!me) {
      throw new Error('Not me');
    }
    if (me.role_id === ERoleId.TEACHER) {
      return {
        redirect: {
          permanent: false,
          destination: '/403',
        },
      };
    }
    return {
      props: {},
    };
  } catch (error) {
    return {
      redirect: {
        permanent: false,
        destination: '/404',
      },
    };
  }
};

export default CategoryEditPage;
